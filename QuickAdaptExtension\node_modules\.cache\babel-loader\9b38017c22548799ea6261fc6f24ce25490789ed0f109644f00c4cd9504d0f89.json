{"ast": null, "code": "import React,{useEffect,useState,useRef,useCallback}from\"react\";import{Box,Button,IconButton,LinearProgress,MobileStepper,Popover,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HotspotPreview=_ref=>{var _savedGuideData$Guide2,_savedGuideData$Guide3,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide36,_savedGuideData$Guide37,_savedGuideData$Guide38;let{anchorEl,guideStep,title,text,imageUrl,onClose,onPrevious,onContinue,videoUrl,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData,hotspotProperties,handleHotspotHover,handleHotspotClick,isHotspotPopupOpen,showHotspotenduser}=_ref;const{setCurrentStep,selectedTemplate,toolTipGuideMetaData,elementSelected,axisData,tooltipXaxis,tooltipYaxis,setOpenTooltip,openTooltip,pulseAnimationsH,hotspotGuideMetaData,selectedTemplateTour,selectedOption,ProgressColor}=useDrawerStore(state=>state);const[targetElement,setTargetElement]=useState(null);// State to track if the popover should be shown\n// State for popup visibility is managed through openTooltip\nconst[popupPosition,setPopupPosition]=useState(null);const[dynamicWidth,setDynamicWidth]=useState(null);const[hotspotSize,setHotspotSize]=useState(30);// Track hotspot size for dynamic popup positioning\nconst contentRef=useRef(null);const buttonContainerRef=useRef(null);let hotspot;const getElementByXPath=xpath=>{if(!xpath)return null;const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;// Return parent if it's a text node\n}else{return null;}};// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\nconst smoothScrollTo=function(element,targetTop){let duration=arguments.length>2&&arguments[2]!==undefined?arguments[2]:300;// Ensure targetTop is within valid bounds\nconst maxScroll=element.scrollHeight-element.clientHeight;const clampedTargetTop=Math.max(0,Math.min(targetTop,maxScroll));// Method 1: Try native smooth scrolling first\ntry{if('scrollTo'in element&&typeof element.scrollTo==='function'){element.scrollTo({top:clampedTargetTop,behavior:'smooth'});return;}}catch(error){console.log(\"Native smooth scrollTo failed, trying animation fallback\");}// Method 2: Manual animation fallback\ntry{const startTop=element.scrollTop;const distance=clampedTargetTop-startTop;const startTime=performance.now();const animateScroll=currentTime=>{const elapsed=currentTime-startTime;const progress=Math.min(elapsed/duration,1);// Easing function for smooth animation\nconst easeInOutCubic=t=>t<0.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1;const easedProgress=easeInOutCubic(progress);element.scrollTop=startTop+distance*easedProgress;if(progress<1){requestAnimationFrame(animateScroll);}};requestAnimationFrame(animateScroll);}catch(error){console.log(\"RequestAnimationFrame failed, using direct assignment\");// Method 3: Direct assignment as final fallback\nelement.scrollTop=clampedTargetTop;}};// Enhanced cross-environment scrolling function\nconst universalScrollTo=(element,options)=>{const isWindow=element===window;const targetElement=isWindow?document.documentElement:element;// Method 1: Try native scrollTo if available and not blocked\nif(!isWindow&&'scrollTo'in element&&typeof element.scrollTo==='function'){try{element.scrollTo(options);return true;}catch(error){console.log(\"Native scrollTo blocked or failed:\",error);}}// Method 2: Try window.scrollTo for window element\nif(isWindow&&options.behavior==='smooth'){try{window.scrollTo(options);return true;}catch(error){console.log(\"Window scrollTo failed:\",error);}}// Method 3: Try smooth scrolling with custom animation\nif(options.behavior==='smooth'&&options.top!==undefined){try{smoothScrollTo(targetElement,options.top);return true;}catch(error){console.log(\"Smooth scroll animation failed:\",error);}}// Method 4: Direct property assignment (final fallback)\ntry{if(options.top!==undefined){targetElement.scrollTop=options.top;}if(options.left!==undefined){targetElement.scrollLeft=options.left;}return true;}catch(error){console.log(\"Direct property assignment failed:\",error);return false;}};// Enhanced reusable element polling function with exponential backoff and better timing\nconst pollForElement=useCallback(function(xpath,possibleElementPath,onElementFound){let maxAttempts=arguments.length>3&&arguments[3]!==undefined?arguments[3]:30;let initialIntervalMs=arguments.length>4&&arguments[4]!==undefined?arguments[4]:16;let description=arguments.length>5&&arguments[5]!==undefined?arguments[5]:\"Element\";let onTimeout=arguments.length>6?arguments[6]:undefined;let attempts=0;let currentInterval=initialIntervalMs;let timeoutId;const poll=()=>{attempts++;console.log(`🔍 ${description} polling attempt ${attempts}/${maxAttempts}`);// Try primary xpath first, then fallback\nlet element=getElementByXPath(xpath);if(!element&&possibleElementPath){element=getElementByXPath(possibleElementPath);}if(element){console.log(`✅ ${description} found after ${attempts} attempts`);onElementFound(element);return;}if(attempts>=maxAttempts){console.log(`❌ ${description} not found after ${maxAttempts} attempts`);if(onTimeout){onTimeout();}return;}// Exponential backoff with jitter to avoid thundering herd\nconst jitter=Math.random()*0.1*currentInterval;const nextInterval=Math.min(currentInterval*1.2+jitter,200);// Cap at 200ms\ncurrentInterval=nextInterval;timeoutId=setTimeout(poll,currentInterval);};// Start polling\npoll();// Return cleanup function\nconst cleanup=()=>{if(timeoutId){clearTimeout(timeoutId);}};// Return cleanup function\nreturn cleanup;},[]);const scrollToTargetElement=useCallback(async function(targetElement){let placement=arguments.length>1&&arguments[1]!==undefined?arguments[1]:\"top\";if(!targetElement){console.log(\"ScrollToTargetElement: No target element provided\");return;}console.log(\"🎯 Starting enhanced auto-scroll to target hotspot element:\",{element:targetElement,tagName:targetElement.tagName,className:targetElement.className,id:targetElement.id,placement:placement});try{// Queue scroll operations to prevent conflicts\nconst scrollOperationQueue=Promise.resolve().then(async()=>{const rect=targetElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Calculate optimal scroll position based on placement and viewport\nlet targetScrollTop=window.scrollY;let targetScrollLeft=window.scrollX;// Enhanced positioning logic based on hotspot placement\nswitch(placement){case\"top\":// Position element in lower third of viewport to leave room for hotspot above\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.7;break;case\"bottom\":// Position element in upper third of viewport to leave room for hotspot below\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.3;break;case\"left\":// Position element towards right side to leave room for hotspot on left\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;targetScrollLeft=window.scrollX+rect.left-viewportWidth*0.7;break;case\"right\":// Position element towards left side to leave room for hotspot on right\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;targetScrollLeft=window.scrollX+rect.left-viewportWidth*0.3;break;default:// Default: center the element vertically\ntargetScrollTop=window.scrollY+rect.top-viewportHeight*0.5;}// Ensure scroll positions are within valid bounds\nconst maxScrollTop=Math.max(0,document.documentElement.scrollHeight-viewportHeight);const maxScrollLeft=Math.max(0,document.documentElement.scrollWidth-viewportWidth);targetScrollTop=Math.max(0,Math.min(targetScrollTop,maxScrollTop));targetScrollLeft=Math.max(0,Math.min(targetScrollLeft,maxScrollLeft));console.log(\"📍 Calculated hotspot scroll position:\",{targetScrollTop,targetScrollLeft,currentScrollY:window.scrollY,currentScrollX:window.scrollX,elementRect:rect});// Perform the scroll with multiple fallback methods\nlet scrollSuccess=false;// Method 1: Try smooth scrolling\ntry{scrollSuccess=universalScrollTo(window,{top:targetScrollTop,left:targetScrollLeft,behavior:'smooth'});console.log(\"✅ Universal hotspot scroll success:\",scrollSuccess);}catch(error){console.log(\"❌ Universal hotspot scroll failed:\",error);}// Method 2: Fallback to immediate scroll if smooth scroll failed\nif(!scrollSuccess){try{window.scrollTo(targetScrollLeft,targetScrollTop);scrollSuccess=true;console.log(\"✅ Fallback hotspot scroll successful\");}catch(error){console.log(\"❌ Fallback hotspot scroll failed:\",error);}}// Method 3: Final fallback using direct property assignment\nif(!scrollSuccess){try{document.documentElement.scrollTop=targetScrollTop;document.documentElement.scrollLeft=targetScrollLeft;document.body.scrollTop=targetScrollTop;document.body.scrollLeft=targetScrollLeft;console.log(\"✅ Direct property assignment completed\");}catch(error){console.log(\"❌ Direct property assignment failed:\",error);}}// Small delay to allow scroll to complete\nawait new Promise(resolve=>setTimeout(resolve,100));console.log(\"🏁 Auto-scroll operation completed\");});await scrollOperationQueue;}catch(error){console.error(\"❌ ScrollToTargetElement error:\",error);}},[smoothScrollTo,universalScrollTo]);// Enhanced useEffect for step changes - ensures proper hotspot positioning\nuseEffect(()=>{var _savedGuideData$Guide;if(selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide!==void 0&&_savedGuideData$Guide[currentStep-1]){console.log(\"🎯 Step change detected for hotspot, ensuring proper positioning:\",{currentStep,stepData:savedGuideData.GuideStep[currentStep-1]});// Small delay to allow DOM to settle after step change\nconst positioningTimeout=setTimeout(()=>{const currentStepData=savedGuideData.GuideStep[currentStep-1];const elementPath=currentStepData===null||currentStepData===void 0?void 0:currentStepData.ElementPath;if(elementPath){console.log(\"🔍 Finding element for step positioning:\",elementPath);// Use polling to ensure element is found\npollForElement(elementPath,\"\",// No fallback path\nasync foundElement=>{var _toolTipGuideMetaData;console.log(\"✅ Element found for step positioning, updating hotspot position\");// Update target element state\nsetTargetElement(foundElement);// Get hotspot properties for current step\nconst hotspotPropData=(toolTipGuideMetaData===null||toolTipGuideMetaData===void 0?void 0:(_toolTipGuideMetaData=toolTipGuideMetaData[currentStep-1])===null||_toolTipGuideMetaData===void 0?void 0:_toolTipGuideMetaData.hotspots)||{XPosition:\"4\",YPosition:\"4\",Size:\"30\"};const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const currentHotspotSize=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size)||\"30\");// Update hotspot size\nsetHotspotSize(currentHotspotSize);// Calculate correct position\nconst rect=foundElement.getBoundingClientRect();const popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);// Update existing hotspot position immediately\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){const left=rect.x+xOffset;const top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));existingHotspot.style.left=`${left}px`;existingHotspot.style.top=`${top}px`;existingHotspot.style.width=`${currentHotspotSize}px`;existingHotspot.style.height=`${currentHotspotSize}px`;console.log(\"✅ Hotspot position updated immediately:\",{left,top,size:currentHotspotSize});}// Auto-scroll to element if needed\ntry{await scrollToTargetElement(foundElement,\"top\");console.log(\"✅ Auto-scroll completed for step change\");}catch(error){console.log(\"❌ Auto-scroll failed for step change:\",error);}},15,// Max attempts\n50,// Initial interval\n`Step ${currentStep} hotspot element`,()=>{console.log(`❌ Element not found for step ${currentStep}, hotspot may not position correctly`);});}},100);// Allow DOM to settle\nreturn()=>{clearTimeout(positioningTimeout);};}},[currentStep,selectedTemplateTour,savedGuideData,toolTipGuideMetaData,pollForElement,scrollToTargetElement]);let xpath;if(savedGuideData)xpath=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide2=savedGuideData.GuideStep)===null||_savedGuideData$Guide2===void 0?void 0:(_savedGuideData$Guide3=_savedGuideData$Guide2[0])===null||_savedGuideData$Guide3===void 0?void 0:_savedGuideData$Guide3.ElementPath;const getElementPosition=xpath=>{const element=getElementByXPath(xpath||\"\");if(element){const rect=element.getBoundingClientRect();return{top:rect.top,//+ window.scrollY + yOffset, // Adjust for vertical scroll\nleft:rect.left// + window.scrollX + xOffset, // Adjust for horizontal scroll\n};}return null;};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);// Function to calculate popup position below the hotspot\nconst calculatePopupPosition=(elementRect,hotspotSize,xOffset,yOffset)=>{const hotspotLeft=elementRect.x+xOffset;const hotspotTop=elementRect.y+yOffset;// Position popup below the hotspot for better user experience\nconst dynamicOffsetX=hotspotSize+5;// Align horizontally with hotspot\nconst dynamicOffsetY=hotspotSize+10;// Position below hotspot with spacing\nreturn{top:hotspotTop+window.scrollY+dynamicOffsetY,left:hotspotLeft+window.scrollX+dynamicOffsetX};};useEffect(()=>{const element=getElementByXPath(xpath);if(element){const rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,// Account for scrolling\nleft:rect.left+window.scrollX});}},[xpath]);useEffect(()=>{if(typeof window!==undefined){const position=getElementPosition(xpath||\"\");if(position){setPopupPosition(position);}}},[xpath]);useEffect(()=>{const element=getElementByXPath(xpath);// setTargetElement(element);\nif(element){}},[savedGuideData]);useEffect(()=>{var _guideStep;const element=getElementByXPath(guideStep===null||guideStep===void 0?void 0:(_guideStep=guideStep[currentStep-1])===null||_guideStep===void 0?void 0:_guideStep.ElementPath);setTargetElement(element);if(element){element.style.backgroundColor=\"red !important\";// Update popup position when target element changes\nconst rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,left:rect.left+window.scrollX});}},[guideStep,currentStep]);// Smart auto-scroll for current hotspot step changes - only when element is not visible\nuseEffect(()=>{var _savedGuideData$Guide4;const currentStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide4=savedGuideData.GuideStep)===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4[currentStep-1];if(!(currentStepData!==null&&currentStepData!==void 0&&currentStepData.ElementPath)){return;}// Small delay to allow DOM to settle\nconst timeoutId=setTimeout(()=>{const currentElement=getElementByXPath(currentStepData.ElementPath||\"\");if(currentElement){const rect=currentElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Check if element is completely out of view\nconst isCompletelyOutOfView=rect.bottom<0||// Completely above viewport\nrect.top>viewportHeight||// Completely below viewport\nrect.right<0||// Completely left of viewport\nrect.left>viewportWidth// Completely right of viewport\n;if(isCompletelyOutOfView){console.log(\"🔄 Current hotspot step element is out of view, gentle auto-scroll\");try{currentElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});}catch(error){console.log(\"Current hotspot step auto-scroll failed:\",error);}}}},100);return()=>clearTimeout(timeoutId);},[currentStep,savedGuideData]);// Hotspot styles are applied directly in the applyHotspotStyles function\n// State for overlay value\nconst[,setOverlayValue]=useState(false);const handleContinue=async()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){var _savedGuideData$Guide5;// Enhanced auto-scroll: Check if next element needs scrolling before navigation\nconst nextStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:_savedGuideData$Guide5[currentStep];// currentStep is 0-based for next step\nif(nextStepData!==null&&nextStepData!==void 0&&nextStepData.ElementPath){console.log(\"🔍 Enhanced checking auto-scroll for next hotspot element:\",{xpath:nextStepData.ElementPath,stepIndex:currentStep+1,stepTitle:`Step ${currentStep+1}`});// Use advanced polling and scrolling\nconst scrollPromise=new Promise(resolve=>{pollForElement(nextStepData.ElementPath||\"\",\"\",// No fallback path for hotspots\nasync foundElement=>{console.log(\"🎯 Next hotspot element found, checking visibility\");const rect=foundElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Enhanced visibility check\nconst isReasonablyVisible=rect.top>=-50&&rect.top<=viewportHeight-100&&rect.left>=-50&&rect.left<=viewportWidth-100&&rect.bottom>100&&rect.right>100;if(isReasonablyVisible){console.log(\"✅ Next hotspot element is reasonably visible, minimal adjustment\");// Element is mostly visible, just ensure it's well positioned\ntry{foundElement.scrollIntoView({behavior:'smooth',block:'nearest',// Don't force center if already visible\ninline:'nearest'});}catch(error){console.log(\"Minimal hotspot scroll adjustment failed:\",error);}}else{console.log(\"🎯 Next hotspot element not visible, performing enhanced auto-scroll\");// Element is not visible, use advanced scrolling\ntry{await scrollToTargetElement(foundElement,\"top\");console.log(\"✅ Enhanced hotspot auto-scroll completed successfully\");}catch(scrollError){console.error(\"❌ Enhanced hotspot auto-scroll failed:\",scrollError);}}resolve();},20,// Reasonable maxAttempts\n30,// Reasonable initial interval\n\"Next hotspot element\",()=>{console.log(\"❌ Next hotspot element not found after polling, continuing without scroll\");resolve();});});try{await scrollPromise;console.log(\"✅ Hotspot element finding and scroll check completed\");}catch(error){console.error(\"❌ Hotspot scroll promise failed:\",error);}}setCurrentStep(currentStep+1);onContinue();renderNextPopup(currentStep<totalSteps);}}else{var _savedGuideData$Guide6;// Enhanced Tour template logic\nconst nextStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide6=savedGuideData.GuideStep)===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6[currentStep];// currentStep is 0-based for next step\nif(nextStepData!==null&&nextStepData!==void 0&&nextStepData.ElementPath){console.log(\"🔍 Enhanced checking auto-scroll for next tour hotspot element:\",{xpath:nextStepData.ElementPath,stepIndex:currentStep+1,stepTitle:`Step ${currentStep+1}`});// Use advanced polling and scrolling for tour hotspots too\nconst scrollPromise=new Promise(resolve=>{pollForElement(nextStepData.ElementPath||\"\",\"\",// No fallback path for hotspots\nasync foundElement=>{console.log(\"🎯 Next tour hotspot element found, checking visibility\");const rect=foundElement.getBoundingClientRect();const viewportHeight=window.innerHeight;const viewportWidth=window.innerWidth;// Enhanced visibility check\nconst isReasonablyVisible=rect.top>=-50&&rect.top<=viewportHeight-100&&rect.left>=-50&&rect.left<=viewportWidth-100&&rect.bottom>100&&rect.right>100;if(isReasonablyVisible){console.log(\"✅ Next tour hotspot element is reasonably visible, minimal adjustment\");// Element is mostly visible, just ensure it's well positioned\ntry{foundElement.scrollIntoView({behavior:'smooth',block:'nearest',// Don't force center if already visible\ninline:'nearest'});}catch(error){console.log(\"Minimal tour hotspot scroll adjustment failed:\",error);}}else{console.log(\"🎯 Next tour hotspot element not visible, performing enhanced auto-scroll\");// Element is not visible, use advanced scrolling\ntry{await scrollToTargetElement(foundElement,\"top\");console.log(\"✅ Enhanced tour hotspot auto-scroll completed successfully\");}catch(scrollError){console.error(\"❌ Enhanced tour hotspot auto-scroll failed:\",scrollError);}}resolve();},20,// Reasonable maxAttempts\n30,// Reasonable initial interval\n\"Next tour hotspot element\",()=>{console.log(\"❌ Next tour hotspot element not found after polling, continuing without scroll\");resolve();});});try{await scrollPromise;console.log(\"✅ Tour hotspot element finding and scroll check completed\");}catch(error){console.error(\"❌ Tour hotspot scroll promise failed:\",error);}}setCurrentStep(currentStep+1);const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}}};const renderNextPopup=shouldRenderNextPopup=>{var _savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11,_savedGuideData$Guide12,_savedGuideData$Guide13,_savedGuideData$Guide14,_savedGuideData$Guide15,_savedGuideData$Guide16;return shouldRenderNextPopup?/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:isHotspotPopupOpen,showHotspotenduser:showHotspotenduser,handleHotspotHover:handleHotspotHover,handleHotspotClick:handleHotspotClick,anchorEl:anchorEl,savedGuideData:savedGuideData,guideStep:guideStep,onClose:onClose,onPrevious:handlePrevious,onContinue:handleContinue,title:title,text:text,imageUrl:imageUrl,currentStep:currentStep+1,totalSteps:totalSteps,onDontShowAgain:onDontShowAgain,progress:progress,textFieldProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[currentStep])===null||_savedGuideData$Guide8===void 0?void 0:_savedGuideData$Guide8.TextFieldProperties,imageProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide9=savedGuideData.GuideStep)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9[currentStep])===null||_savedGuideData$Guide10===void 0?void 0:_savedGuideData$Guide10.ImageProperties,customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide11=savedGuideData.GuideStep)===null||_savedGuideData$Guide11===void 0?void 0:(_savedGuideData$Guide12=_savedGuideData$Guide11[currentStep])===null||_savedGuideData$Guide12===void 0?void 0:(_savedGuideData$Guide13=_savedGuideData$Guide12.ButtonSection)===null||_savedGuideData$Guide13===void 0?void 0:(_savedGuideData$Guide14=_savedGuideData$Guide13.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide14===void 0?void 0:_savedGuideData$Guide14.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:modalProperties,canvasProperties:canvasProperties,htmlSnippet:htmlSnippet,OverlayValue:OverlayValue,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide15=savedGuideData.GuideStep)===null||_savedGuideData$Guide15===void 0?void 0:(_savedGuideData$Guide16=_savedGuideData$Guide15[currentStep-1])===null||_savedGuideData$Guide16===void 0?void 0:_savedGuideData$Guide16.Hotspot)||{}}):null;};const handlePrevious=async()=>{if(currentStep>1){console.log(\"🔙 Navigating to previous hotspot step:\",{currentStep:currentStep,prevStep:currentStep-1});// Smart previous navigation scrolling\nif(currentStep-1===1){console.log(\"HandlePrevious: Going back to first hotspot step, scroll to top\");try{window.scrollTo({top:0,behavior:'smooth'});}catch(error){console.log(\"HandlePrevious: Scroll to top failed:\",error);}}else{var _savedGuideData$Guide17;// For other steps, check if previous step element needs scrolling\nconst prevStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide17=savedGuideData.GuideStep)===null||_savedGuideData$Guide17===void 0?void 0:_savedGuideData$Guide17[currentStep-2];// currentStep - 2 for 0-based array\nif(prevStepData!==null&&prevStepData!==void 0&&prevStepData.ElementPath){console.log(\"HandlePrevious: Checking if previous hotspot element needs scrolling\");setTimeout(()=>{const prevElement=getElementByXPath(prevStepData.ElementPath||\"\");if(prevElement){const rect=prevElement.getBoundingClientRect();const isOutOfView=rect.bottom<0||rect.top>window.innerHeight||rect.right<0||rect.left>window.innerWidth;if(isOutOfView){console.log(\"HandlePrevious: Previous hotspot element out of view, scrolling\");try{prevElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});}catch(error){console.log(\"HandlePrevious: Hotspot element scroll failed:\",error);}}}},100);}}setCurrentStep(currentStep-1);onPrevious();}};useEffect(()=>{if(OverlayValue){setOverlayValue(true);}else{setOverlayValue(false);}},[OverlayValue]);// Image fit is used directly in the component\nconst getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};const{anchorOrigin,transformOrigin}=getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};// Image styles are applied directly in the component\nconst renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(_match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Helper function to check if popup has only buttons (no text or images)\nconst hasOnlyButtons=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasButtons&&!hasImage&&!hasText;};// Helper function to check if popup has only text (no buttons or images)\nconst hasOnlyText=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasText&&!hasImage&&!hasButtons;};// Function to calculate the optimal width based on content and buttons\nconst calculateOptimalWidth=()=>{var _contentRef$current,_buttonContainerRef$c;// If we have a fixed width from canvas settings and not a compact popup, use that\nif(canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width&&!hasOnlyButtons()&&!hasOnlyText()){return`${canvasProperties.Width}px`;}// For popups with only buttons or only text, use auto width\nif(hasOnlyButtons()||hasOnlyText()){return\"auto\";}// Get the width of content and button container\nconst contentWidth=((_contentRef$current=contentRef.current)===null||_contentRef$current===void 0?void 0:_contentRef$current.scrollWidth)||0;const buttonWidth=((_buttonContainerRef$c=buttonContainerRef.current)===null||_buttonContainerRef$c===void 0?void 0:_buttonContainerRef$c.scrollWidth)||0;// Use the larger of the two, with some minimum and maximum constraints\nconst optimalWidth=Math.max(contentWidth,buttonWidth);// Add some padding to ensure text has room to wrap naturally\nconst paddedWidth=optimalWidth+20;// 10px padding on each side\n// Ensure width is between reasonable bounds\nconst minWidth=250;// Minimum width\nconst maxWidth=800;// Maximum width\nconst finalWidth=Math.max(minWidth,Math.min(paddedWidth,maxWidth));return`${finalWidth}px`;};// Update dynamic width when content or buttons change\nuseEffect(()=>{// Use requestAnimationFrame to ensure DOM has been updated\nrequestAnimationFrame(()=>{const newWidth=calculateOptimalWidth();setDynamicWidth(newWidth);});},[textFieldProperties,imageProperties,customButton,currentStep]);// Recalculate popup position when hotspot size changes\nuseEffect(()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData2;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData2=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[hotspotSize,xpath,toolTipGuideMetaData]);// Recalculate popup position on window resize\nuseEffect(()=>{const handleResize=()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData3;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData3=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData3===void 0?void 0:_toolTipGuideMetaData3.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[xpath,hotspotSize,toolTipGuideMetaData]);const groupedButtons=customButton.reduce((acc,button)=>{const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Radius)||\"4px\",borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"black\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",maxWidth:hasOnlyButtons()||hasOnlyText()?\"none !important\":dynamicWidth?`${dynamicWidth} !important`:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width?`${canvasProperties.Width}px !important`:\"800px\",width:hasOnlyButtons()||hasOnlyText()?\"auto !important\":dynamicWidth?`${dynamicWidth} !important`:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width?`${canvasProperties.Width}px !important`:\"300px\"};const sectionHeight=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.SectionHeight)||\"auto\";const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"Previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide18,_savedGuideData$Guide19;// Reset to the first step\nsetCurrentStep(1);console.log(\"🔄 Restarting hotspot tour, checking first step element\");// Enhanced restart scrolling logic\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide18=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide18!==void 0&&(_savedGuideData$Guide19=_savedGuideData$Guide18[0])!==null&&_savedGuideData$Guide19!==void 0&&_savedGuideData$Guide19.ElementPath){// Check if first step element exists and use gentle scroll\nconst firstElement=getElementByXPath(savedGuideData.GuideStep[0].ElementPath);if(firstElement){try{firstElement.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});console.log(\"✅ Gentle restart scroll to first hotspot completed\");}catch(error){console.log(\"❌ Gentle restart scroll failed, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}else{console.log(\"❌ First hotspot step element not found, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}else{// No xpath available, just scroll to top\nconsole.log(\"ℹ️ No xpath for first hotspot step, scrolling to top\");window.scrollTo({top:0,behavior:'smooth'});}}}setOverlayValue(false);};useEffect(()=>{var _guideStep2,_guideStep2$Hotspot;if(guideStep!==null&&guideStep!==void 0&&(_guideStep2=guideStep[currentStep-1])!==null&&_guideStep2!==void 0&&(_guideStep2$Hotspot=_guideStep2.Hotspot)!==null&&_guideStep2$Hotspot!==void 0&&_guideStep2$Hotspot.ShowByDefault){// Show tooltip by default\nsetOpenTooltip(true);}},[guideStep===null||guideStep===void 0?void 0:guideStep[currentStep-1],currentStep,setOpenTooltip]);// Add effect to handle isHotspotPopupOpen prop changes\nuseEffect(()=>{if(isHotspotPopupOpen){var _toolTipGuideMetaData4,_toolTipGuideMetaData5,_savedGuideData$Guide20,_savedGuideData$Guide21,_savedGuideData$Guide22,_savedGuideData$Guide23;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData4=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData4!==void 0&&_toolTipGuideMetaData4.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData5=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData5===void 0?void 0:_toolTipGuideMetaData5.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide20=savedGuideData.GuideStep)===null||_savedGuideData$Guide20===void 0?void 0:(_savedGuideData$Guide21=_savedGuideData$Guide20[currentStep-1])===null||_savedGuideData$Guide21===void 0?void 0:_savedGuideData$Guide21.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide22=savedGuideData.GuideStep)===null||_savedGuideData$Guide22===void 0?void 0:(_savedGuideData$Guide23=_savedGuideData$Guide22[0])===null||_savedGuideData$Guide23===void 0?void 0:_savedGuideData$Guide23.Hotspot;// Only show tooltip by default if ShowByDefault is true\n// For \"Hovering Hotspot\", we'll wait for the hover event\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[isHotspotPopupOpen,toolTipGuideMetaData]);// Add effect to handle showHotspotenduser prop changes\nuseEffect(()=>{if(showHotspotenduser){var _toolTipGuideMetaData6,_toolTipGuideMetaData7,_savedGuideData$Guide24,_savedGuideData$Guide25,_savedGuideData$Guide26,_savedGuideData$Guide27;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData6=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData6!==void 0&&_toolTipGuideMetaData6.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData7=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData7===void 0?void 0:_toolTipGuideMetaData7.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide24=savedGuideData.GuideStep)===null||_savedGuideData$Guide24===void 0?void 0:(_savedGuideData$Guide25=_savedGuideData$Guide24[currentStep-1])===null||_savedGuideData$Guide25===void 0?void 0:_savedGuideData$Guide25.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide26=savedGuideData.GuideStep)===null||_savedGuideData$Guide26===void 0?void 0:(_savedGuideData$Guide27=_savedGuideData$Guide26[0])===null||_savedGuideData$Guide27===void 0?void 0:_savedGuideData$Guide27.Hotspot;// Only show tooltip by default if ShowByDefault is true\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[showHotspotenduser,toolTipGuideMetaData]);// Add a global click handler to detect clicks outside the hotspot to close the tooltip\nuseEffect(()=>{const handleGlobalClick=e=>{const hotspotElement=document.getElementById(\"hotspotBlink\");// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\nif(hotspotElement&&hotspotElement.contains(e.target)){return;}// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on clicks outside anymore\n};document.addEventListener(\"click\",handleGlobalClick);return()=>{document.removeEventListener(\"click\",handleGlobalClick);};},[toolTipGuideMetaData]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);// We no longer need the persistent monitoring effect since we want the tooltip\n// to close when the mouse leaves the hotspot\nfunction getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const getCanvasPosition=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";switch(position){case\"bottom-left\":return{top:\"auto !important\"};case\"bottom-right\":return{top:\"auto !important\"};case\"bottom-center\":return{top:\"auto !important\"};case\"center-center\":return{top:\"25% !important\"};case\"left-center\":return{top:imageUrl===\"\"?\"40% !important\":\"20% !important\"};case\"right-center\":return{top:\"10% !important\"};case\"top-left\":return{top:\"10% !important\"};case\"top-right\":return{top:\"10% !important\"};case\"top-center\":return{top:\"9% !important\"};default:return{top:\"25% !important\"};}};// function to get the correct property value based on tour vs normal hotspot\nconst getHotspotProperty=(propName,hotspotPropData,hotspotData)=>{if(selectedTemplateTour===\"Hotspot\"){// For tour hotspots, use saved data first, fallback to metadata\nswitch(propName){case'PulseAnimation':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.PulseAnimation)!==undefined?hotspotData.PulseAnimation:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.PulseAnimation;case'StopAnimation':// Always use stopAnimationUponInteraction for consistency\nreturn(hotspotData===null||hotspotData===void 0?void 0:hotspotData.stopAnimationUponInteraction)!==undefined?hotspotData.stopAnimationUponInteraction:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;case'ShowUpon':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowUpon)!==undefined?hotspotData.ShowUpon:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon;case'ShowByDefault':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowByDefault)!==undefined?hotspotData.ShowByDefault:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowByDefault;default:return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}}else{// For normal hotspots, use metadata\nif(propName==='StopAnimation'){return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;}return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}};const applyHotspotStyles=(hotspot,hotspotPropData,hotspotData,left,top)=>{hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;// Default size if not provided\nhotspot.style.height=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;hotspot.style.backgroundColor=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Color;hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"auto !important\";// Increased z-index\nhotspot.style.transition=\"none\";hotspot.style.pointerEvents=\"auto\";// Ensure clicks are registered\nhotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";hotspot.appendChild(textSpan);}// Apply animation class if needed\n// Track if pulse has been stopped by hover\nconst pulseAnimationEnabled=getHotspotProperty('PulseAnimation',hotspotPropData,hotspotData);const shouldPulse=selectedTemplateTour===\"Hotspot\"?pulseAnimationEnabled!==false&&!hotspot._pulseStopped:hotspotPropData&&pulseAnimationsH&&!hotspot._pulseStopped;if(shouldPulse){hotspot.classList.add(\"pulse-animation\");hotspot.classList.remove(\"pulse-animation-removed\");}else{hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");}// Ensure the hotspot is visible and clickable\nhotspot.style.display=\"flex\";hotspot.style.pointerEvents=\"auto\";// No need for separate animation control functions here\n// Animation will be controlled directly in the event handlers\n// Set initial state of openTooltip based on ShowByDefault and ShowUpon\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{// If not showing by default, only show based on interaction type\n//setOpenTooltip(false);\n}// Only clone and replace if the hotspot doesn't have event listeners already\n// This prevents losing the _pulseStopped state unnecessarily\nif(!hotspot.hasAttribute('data-listeners-attached')){const newHotspot=hotspot.cloneNode(true);// Copy the _pulseStopped property if it exists\nif(hotspot._pulseStopped!==undefined){newHotspot._pulseStopped=hotspot._pulseStopped;}if(hotspot.parentNode){hotspot.parentNode.replaceChild(newHotspot,hotspot);hotspot=newHotspot;}}// Ensure pointer events are enabled\nhotspot.style.pointerEvents=\"auto\";// Define combined event handlers that handle both animation and tooltip\nconst showUpon=getHotspotProperty('ShowUpon',hotspotPropData,hotspotData);const handleHover=e=>{e.stopPropagation();console.log(\"Hover detected on hotspot\");// Show tooltip if ShowUpon is \"Hovering Hotspot\"\nif(showUpon===\"Hovering Hotspot\"){// Set openTooltip to true when hovering\nsetOpenTooltip(true);// Call the passed hover handler if it exists\nif(typeof handleHotspotHover===\"function\"){handleHotspotHover();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};const handleMouseOut=e=>{e.stopPropagation();// Hide tooltip when mouse leaves the hotspot\n// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showUpon===\"Hovering Hotspot\"&&!showByDefault){// setOpenTooltip(false);\n}};const handleClick=e=>{e.stopPropagation();console.log(\"Click detected on hotspot\");// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\nif(showUpon===\"Clicking Hotspot\"||!showUpon){// Toggle the tooltip state\nsetOpenTooltip(!openTooltip);// Call the passed click handler if it exists\nif(typeof handleHotspotClick===\"function\"){handleHotspotClick();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};// Add appropriate event listeners based on ShowUpon property\nif(!hotspot.hasAttribute('data-listeners-attached')){if(showUpon===\"Hovering Hotspot\"){// For hover interaction\nhotspot.addEventListener(\"mouseover\",handleHover);hotspot.addEventListener(\"mouseout\",handleMouseOut);// Also add click handler for better user experience\nhotspot.addEventListener(\"click\",handleClick);}else{// For click interaction (default)\nhotspot.addEventListener(\"click\",handleClick);}// Mark that listeners have been attached\nhotspot.setAttribute('data-listeners-attached','true');}};useEffect(()=>{let element;let steps;const fetchGuideDetails=async()=>{try{var _savedGuideData$Guide28,_savedGuideData$Guide29,_steps,_steps$;//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\nsteps=(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideStep)||[];// For tour hotspots, use the current step's element path\nconst elementPath=selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide28=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide28!==void 0&&(_savedGuideData$Guide29=_savedGuideData$Guide28[currentStep-1])!==null&&_savedGuideData$Guide29!==void 0&&_savedGuideData$Guide29.ElementPath?savedGuideData.GuideStep[currentStep-1].ElementPath:((_steps=steps)===null||_steps===void 0?void 0:(_steps$=_steps[0])===null||_steps$===void 0?void 0:_steps$.ElementPath)||\"\";console.log(\"🎯 Fetching element for hotspot positioning:\",{elementPath,selectedTemplateTour,currentStep,isHotspotStep:selectedTemplateTour===\"Hotspot\"});element=getElementByXPath(elementPath||\"\");setTargetElement(element);// If element not found immediately, try polling (especially important for step transitions)\nif(!element&&elementPath){console.log(\"🔍 Element not found immediately, starting polling...\");pollForElement(elementPath,\"\",// No fallback path\nfoundElement=>{console.log(\"✅ Element found via polling, updating state\");element=foundElement;setTargetElement(foundElement);// Trigger a re-render by updating a state that will cause the positioning logic to run again\nsetTimeout(()=>{const rect=foundElement.getBoundingClientRect();console.log(\"📍 Element rect after polling:\",rect);},50);},10,// Max attempts\n100,// Initial interval\n\"Main hotspot element\",()=>{console.log(\"❌ Element not found after polling in main logic\");});}if(element){// element.style.outline = \"2px solid red\";\n}// Check if this is a hotspot scenario (normal or tour)\nconst isHotspotScenario=selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"||title===\"Hotspot\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Hotspot\";if(isHotspotScenario){var _toolTipGuideMetaData8,_toolTipGuideMetaData9,_hotspotPropData,_hotspotPropData2,_hotspotPropData3,_hotspotPropData4;// Get hotspot properties - prioritize tour data for tour hotspots\nlet hotspotPropData;let hotspotData;if(selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData8=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData8!==void 0&&_toolTipGuideMetaData8.hotspots){var _savedGuideData$Guide30,_savedGuideData$Guide31;// Tour hotspot - use current step metadata\nhotspotPropData=toolTipGuideMetaData[currentStep-1].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide30=savedGuideData.GuideStep)===null||_savedGuideData$Guide30===void 0?void 0:(_savedGuideData$Guide31=_savedGuideData$Guide30[currentStep-1])===null||_savedGuideData$Guide31===void 0?void 0:_savedGuideData$Guide31.Hotspot;}else if(toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData9=toolTipGuideMetaData[0])!==null&&_toolTipGuideMetaData9!==void 0&&_toolTipGuideMetaData9.hotspots){var _savedGuideData$Guide32,_savedGuideData$Guide33;// Normal hotspot - use first metadata entry\nhotspotPropData=toolTipGuideMetaData[0].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide32=savedGuideData.GuideStep)===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32[0])===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.Hotspot;}else{var _savedGuideData$Guide34,_savedGuideData$Guide35;// Fallback to default values for tour hotspots without metadata\nhotspotPropData={XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};hotspotData=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:(_savedGuideData$Guide35=_savedGuideData$Guide34[currentStep-1])===null||_savedGuideData$Guide35===void 0?void 0:_savedGuideData$Guide35.Hotspot)||{};}const xOffset=parseFloat(((_hotspotPropData=hotspotPropData)===null||_hotspotPropData===void 0?void 0:_hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat(((_hotspotPropData2=hotspotPropData)===null||_hotspotPropData2===void 0?void 0:_hotspotPropData2.YPosition)||\"4\");const currentHotspotSize=parseFloat(((_hotspotPropData3=hotspotPropData)===null||_hotspotPropData3===void 0?void 0:_hotspotPropData3.Size)||\"30\");// Update hotspot size state\nsetHotspotSize(currentHotspotSize);let left,top;if(element){const rect=element.getBoundingClientRect();// Ensure element has valid dimensions (not hidden or not yet rendered)\nif(rect.width>0&&rect.height>0){left=rect.x+xOffset;top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));// Calculate popup position below the hotspot\nconst popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);console.log(\"✅ Hotspot positioned successfully:\",{elementRect:rect,hotspotPosition:{left,top},popupPosition:popupPos});}else{console.log(\"⚠️ Element found but has no dimensions, may be hidden:\",rect);// Set default position to prevent hotspot from appearing at (0,0)\nleft=100;top=100;}}else{console.log(\"❌ No element found for hotspot positioning\");// Set default position to prevent hotspot from appearing at (0,0)\nleft=100;top=100;}// Check if hotspot already exists, preserve it to maintain _pulseStopped state\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){hotspot=existingHotspot;// Don't reset _pulseStopped if it already exists\n}else{// Create new hotspot only if it doesn't exist\nhotspot=document.createElement(\"div\");hotspot.id=\"hotspotBlink\";// Fixed ID for easier reference\nhotspot._pulseStopped=false;// Set only on creation\ndocument.body.appendChild(hotspot);}hotspot.style.cursor=\"pointer\";hotspot.style.pointerEvents=\"auto\";// Ensure it can receive mouse events\n// Make sure the hotspot is visible and clickable\nhotspot.style.zIndex=\"9999\";// If ShowByDefault is true, set openTooltip to true immediately\nif((_hotspotPropData4=hotspotPropData)!==null&&_hotspotPropData4!==void 0&&_hotspotPropData4.ShowByDefault){setOpenTooltip(true);}// Set styles first\napplyHotspotStyles(hotspot,hotspotPropData,hotspotData,left,top);// Set initial tooltip visibility based on ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{//setOpenTooltip(false);\n}// We don't need to add event listeners here as they're already added in applyHotspotStyles\n}}catch(error){console.error(\"Error in fetchGuideDetails:\",error);}};fetchGuideDetails();return()=>{const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.onclick=null;existingHotspot.onmouseover=null;existingHotspot.onmouseout=null;}};},[savedGuideData,toolTipGuideMetaData,isHotspotPopupOpen,showHotspotenduser,selectedTemplateTour,currentStep// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n]);const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide36=savedGuideData.GuideStep)===null||_savedGuideData$Guide36===void 0?void 0:(_savedGuideData$Guide37=_savedGuideData$Guide36[0])===null||_savedGuideData$Guide37===void 0?void 0:(_savedGuideData$Guide38=_savedGuideData$Guide37.Tooltip)===null||_savedGuideData$Guide38===void 0?void 0:_savedGuideData$Guide38.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide39,_savedGuideData$Guide40,_savedGuideData$Guide41;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide39=savedGuideData.GuideStep)===null||_savedGuideData$Guide39===void 0?void 0:(_savedGuideData$Guide40=_savedGuideData$Guide39[0])===null||_savedGuideData$Guide40===void 0?void 0:(_savedGuideData$Guide41=_savedGuideData$Guide40.Tooltip)===null||_savedGuideData$Guide41===void 0?void 0:_savedGuideData$Guide41.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",position:\"inherit !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:index===currentStep-1?ProgressColor:\"#e0e0e0\",// Active color and inactive color\nborderRadius:\"100px\"}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",\"& .MuiLinearProgress-bar\":{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsxs(_Fragment,{children:[targetElement&&/*#__PURE__*/_jsx(\"div\",{children:openTooltip&&/*#__PURE__*/_jsxs(Popover,{open:Boolean(popupPosition)||Boolean(anchorEl),anchorEl:anchorEl,onClose:()=>{// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on Popover close events\n},anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,anchorReference:\"anchorPosition\",anchorPosition:popupPosition?{top:popupPosition.top+10+(parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\"))),left:popupPosition.left+10+parseFloat(tooltipXaxis||\"0\")}:undefined,sx:{// \"& .MuiBackdrop-root\": {\n//     position: 'relative !important', // Ensures higher specificity\n// },\n\"pointer-events\":anchorEl?\"auto\":\"auto\",'& .MuiPaper-root:not(.MuiMobileStepper-root)':{zIndex:1000,// borderRadius: \"1px\",\n...canvasStyle,//...getAnchorAndTransformOrigins,\n//top: \"16% !important\",\n// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n...getCanvasPosition((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\"),top:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.top)||0)+(tooltipYaxis&&tooltipYaxis!='undefined'?parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\")):0)}px !important`,left:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.left)||0)+(tooltipXaxis&&tooltipXaxis!='undefined'?parseFloat(tooltipXaxis)||0:0)}px !important`,overflow:\"hidden\"}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{onClick:()=>{// Only close if explicitly requested by user clicking the close button\n//setOpenTooltip(false);\n},sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\",overflow:hasOnlyButtons()||hasOnlyText()?\"visible\":\"hidden auto\",width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:/*#__PURE__*/_jsxs(Box,{style:{padding:hasOnlyButtons()?\"0\":hasOnlyText()?\"0\":(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding)||\"10px\",height:hasOnlyButtons()?\"auto\":sectionHeight,width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:[/*#__PURE__*/_jsxs(Box,{ref:contentRef,display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",sx:{width:hasOnlyText()?\"auto\":\"100%\",padding:hasOnlyText()?\"0\":undefined},children:[imageProperties===null||imageProperties===void 0?void 0:imageProperties.map(imageProp=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",//  width: \"500px\",\nheight:`${customImg.SectionHeight||250}px`,background:customImg.BackgroundColor||\"#ffffff\",margin:\"10px 0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`))),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper,_textField$TextProper2;return textField.Text&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview\",// Use a unique key, either Id or index\nsx:{textAlign:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextFormat)||textStyle.textAlign,color:((_textField$TextProper2=textField.TextProperties)===null||_textField$TextProper2===void 0?void 0:_textField$TextProper2.TextColor)||textStyle.color,whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",padding:\"0 5px\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);})]}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:hasOnlyButtons()?0:\"5px 0\",backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:hasOnlyButtons()?\"4px\":\"5px 0\",width:hasOnlyButtons()?\"auto\":\"100%\",borderRadius:hasOnlyButtons()?\"15px\":undefined},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6,_button$ButtonPropert7;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{marginRight:hasOnlyButtons()?\"5px\":\"13px\",margin:hasOnlyButtons()?\"4px\":\"0 5px 5px 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"15px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:hasOnlyButtons()?\"var(--button-padding) !important\":\"4px 8px\",lineHeight:hasOnlyButtons()?\"var(--button-lineheight)\":\"normal\",textTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",minWidth:hasOnlyButtons()?\"fit-content\":undefined,boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:((_button$ButtonPropert7=button.ButtonProperties)===null||_button$ButtonPropert7===void 0?void 0:_button$ButtonPropert7.ButtonBackgroundColor)||\"#007bff\",// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:button.ButtonName},index);})},containerId);})]})})},`scrollbar-${needsScrolling}`),enableProgress&&totalSteps>1&&selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `})]});};export default HotspotPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HotspotPreview", "_ref", "_savedGuideData$Guide2", "_savedGuideData$Guide3", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide36", "_savedGuideData$Guide37", "_savedGuideData$Guide38", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "dynamicWidth", "setDynamicWidth", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "smoothScrollTo", "element", "targetTop", "duration", "arguments", "length", "undefined", "maxScroll", "scrollHeight", "clientHeight", "clampedTargetTop", "Math", "max", "min", "scrollTo", "top", "behavior", "error", "console", "log", "startTop", "scrollTop", "distance", "startTime", "performance", "now", "animateScroll", "currentTime", "elapsed", "easeInOutCubic", "t", "easedProgress", "requestAnimationFrame", "universalScrollTo", "options", "isWindow", "window", "documentElement", "left", "scrollLeft", "pollForElement", "possibleElementPath", "onElementFound", "maxAttempts", "initialIntervalMs", "description", "onTimeout", "attempts", "currentInterval", "timeoutId", "poll", "jitter", "random", "nextInterval", "setTimeout", "cleanup", "clearTimeout", "scrollToTargetElement", "placement", "tagName", "className", "id", "scrollOperationQueue", "Promise", "resolve", "then", "rect", "getBoundingClientRect", "viewportHeight", "innerHeight", "viewportWidth", "innerWidth", "targetScrollTop", "scrollY", "targetScrollLeft", "scrollX", "maxScrollTop", "maxScrollLeft", "scrollWidth", "currentScrollY", "currentScrollX", "elementRect", "scrollSuccess", "body", "_savedGuideData$Guide", "GuideStep", "stepData", "positioningTimeout", "currentStepData", "elementPath", "<PERSON>ement<PERSON><PERSON>", "foundElement", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "XPosition", "YPosition", "Size", "xOffset", "parseFloat", "yOffset", "currentHotspotSize", "popupPos", "calculatePopupPosition", "existingHotspot", "getElementById", "x", "y", "abs", "style", "width", "height", "size", "getElementPosition", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "hotspotLeft", "hotspotTop", "dynamicOffsetX", "dynamicOffsetY", "position", "_guideStep", "backgroundColor", "_savedGuideData$Guide4", "currentElement", "isCompletelyOutOfView", "bottom", "right", "scrollIntoView", "block", "inline", "setOverlayValue", "handleContinue", "_savedGuideData$Guide5", "nextStepData", "stepIndex", "step<PERSON>itle", "scrollPromise", "isReasonablyVisible", "scrollError", "renderNextPopup", "_savedGuideData$Guide6", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "prevStep", "_savedGuideData$Guide17", "prevStepData", "prevElement", "isOutOfView", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "calculateOptimalWidth", "_contentRef$current", "_buttonContainerRef$c", "<PERSON><PERSON><PERSON>", "contentWidth", "current", "buttonWidth", "optimalWidth", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "newWidth", "_toolTipGuideMetaData2", "handleResize", "_toolTipGuideMetaData3", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide18", "_savedGuideData$Guide19", "firstElement", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData4", "_toolTipGuideMetaData5", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "_savedGuideData$Guide23", "hotspotData", "_toolTipGuideMetaData6", "_toolTipGuideMetaData7", "_savedGuideData$Guide24", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_steps", "_steps$", "isHotspotStep", "isHotspotScenario", "_toolTipGuideMetaData8", "_toolTipGuideMetaData9", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide30", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "hotspotPosition", "cursor", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide39", "_savedGuideData$Guide40", "_savedGuideData$Guide41", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "imageProp", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tif (!xpath) return null;\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\r\n\tconst smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {\r\n\t\t// Ensure targetTop is within valid bounds\r\n\t\tconst maxScroll = element.scrollHeight - element.clientHeight;\r\n\t\tconst clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\r\n\r\n\t\t// Method 1: Try native smooth scrolling first\r\n\t\ttry {\r\n\t\t\tif ('scrollTo' in element && typeof element.scrollTo === 'function') {\r\n\t\t\t\telement.scrollTo({\r\n\t\t\t\t\ttop: clampedTargetTop,\r\n\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Native smooth scrollTo failed, trying animation fallback\");\r\n\t\t}\r\n\r\n\t\t// Method 2: Manual animation fallback\r\n\t\ttry {\r\n\t\t\tconst startTop = element.scrollTop;\r\n\t\t\tconst distance = clampedTargetTop - startTop;\r\n\t\t\tconst startTime = performance.now();\r\n\r\n\t\t\tconst animateScroll = (currentTime: number) => {\r\n\t\t\t\tconst elapsed = currentTime - startTime;\r\n\t\t\t\tconst progress = Math.min(elapsed / duration, 1);\r\n\r\n\t\t\t\t// Easing function for smooth animation\r\n\t\t\t\tconst easeInOutCubic = (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\r\n\t\t\t\tconst easedProgress = easeInOutCubic(progress);\r\n\r\n\t\t\t\telement.scrollTop = startTop + (distance * easedProgress);\r\n\r\n\t\t\t\tif (progress < 1) {\r\n\t\t\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"RequestAnimationFrame failed, using direct assignment\");\r\n\t\t\t// Method 3: Direct assignment as final fallback\r\n\t\t\telement.scrollTop = clampedTargetTop;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced cross-environment scrolling function\r\n\tconst universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {\r\n\t\tconst isWindow = element === window;\r\n\t\tconst targetElement = isWindow ? document.documentElement : element as HTMLElement;\r\n\r\n\t\t// Method 1: Try native scrollTo if available and not blocked\r\n\t\tif (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {\r\n\t\t\ttry {\r\n\t\t\t\t(element as any).scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Native scrollTo blocked or failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 2: Try window.scrollTo for window element\r\n\t\tif (isWindow && options.behavior === 'smooth') {\r\n\t\t\ttry {\r\n\t\t\t\twindow.scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Window scrollTo failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 3: Try smooth scrolling with custom animation\r\n\t\tif (options.behavior === 'smooth' && options.top !== undefined) {\r\n\t\t\ttry {\r\n\t\t\t\tsmoothScrollTo(targetElement, options.top);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Smooth scroll animation failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 4: Direct property assignment (final fallback)\r\n\t\ttry {\r\n\t\t\tif (options.top !== undefined) {\r\n\t\t\t\ttargetElement.scrollTop = options.top;\r\n\t\t\t}\r\n\t\t\tif (options.left !== undefined) {\r\n\t\t\t\ttargetElement.scrollLeft = options.left;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Direct property assignment failed:\", error);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced reusable element polling function with exponential backoff and better timing\r\n\tconst pollForElement = useCallback((\r\n\t\txpath: string,\r\n\t\tpossibleElementPath: string,\r\n\t\tonElementFound: (element: HTMLElement) => void,\r\n\t\tmaxAttempts: number = 30,\r\n\t\tinitialIntervalMs: number = 16, // Start with one frame\r\n\t\tdescription: string = \"Element\",\r\n\t\tonTimeout?: () => void\r\n\t) => {\r\n\t\tlet attempts = 0;\r\n\t\tlet currentInterval = initialIntervalMs;\r\n\t\tlet timeoutId: NodeJS.Timeout;\r\n\r\n\t\tconst poll = () => {\r\n\t\t\tattempts++;\r\n\t\t\tconsole.log(`🔍 ${description} polling attempt ${attempts}/${maxAttempts}`);\r\n\r\n\t\t\t// Try primary xpath first, then fallback\r\n\t\t\tlet element = getElementByXPath(xpath);\r\n\t\t\tif (!element && possibleElementPath) {\r\n\t\t\t\telement = getElementByXPath(possibleElementPath);\r\n\t\t\t}\r\n\r\n\t\t\tif (element) {\r\n\t\t\t\tconsole.log(`✅ ${description} found after ${attempts} attempts`);\r\n\t\t\t\tonElementFound(element);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (attempts >= maxAttempts) {\r\n\t\t\t\tconsole.log(`❌ ${description} not found after ${maxAttempts} attempts`);\r\n\t\t\t\tif (onTimeout) {\r\n\t\t\t\t\tonTimeout();\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Exponential backoff with jitter to avoid thundering herd\r\n\t\t\tconst jitter = Math.random() * 0.1 * currentInterval;\r\n\t\t\tconst nextInterval = Math.min(currentInterval * 1.2 + jitter, 200); // Cap at 200ms\r\n\t\t\tcurrentInterval = nextInterval;\r\n\r\n\t\t\ttimeoutId = setTimeout(poll, currentInterval);\r\n\t\t};\r\n\r\n\t\t// Start polling\r\n\t\tpoll();\r\n\r\n\t\t// Return cleanup function\r\n\t\tconst cleanup = () => {\r\n\t\t\tif (timeoutId) {\r\n\t\t\t\tclearTimeout(timeoutId);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Return cleanup function\r\n\t\treturn cleanup;\r\n\t}, []);\r\n\r\n\tconst scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: \"top\" | \"left\" | \"right\" | \"bottom\" = \"top\") => {\r\n\t\tif (!targetElement) {\r\n\t\t\tconsole.log(\"ScrollToTargetElement: No target element provided\");\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconsole.log(\"🎯 Starting enhanced auto-scroll to target hotspot element:\", {\r\n\t\t\telement: targetElement,\r\n\t\t\ttagName: targetElement.tagName,\r\n\t\t\tclassName: targetElement.className,\r\n\t\t\tid: targetElement.id,\r\n\t\t\tplacement: placement\r\n\t\t});\r\n\r\n\t\ttry {\r\n\t\t\t// Queue scroll operations to prevent conflicts\r\n\t\t\tconst scrollOperationQueue = Promise.resolve().then(async () => {\r\n\t\t\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Calculate optimal scroll position based on placement and viewport\r\n\t\t\t\tlet targetScrollTop = window.scrollY;\r\n\t\t\t\tlet targetScrollLeft = window.scrollX;\r\n\r\n\t\t\t\t// Enhanced positioning logic based on hotspot placement\r\n\t\t\t\tswitch (placement) {\r\n\t\t\t\t\tcase \"top\":\r\n\t\t\t\t\t\t// Position element in lower third of viewport to leave room for hotspot above\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\t\t// Position element in upper third of viewport to leave room for hotspot below\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"left\":\r\n\t\t\t\t\t\t// Position element towards right side to leave room for hotspot on left\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"right\":\r\n\t\t\t\t\t\t// Position element towards left side to leave room for hotspot on right\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t// Default: center the element vertically\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Ensure scroll positions are within valid bounds\r\n\t\t\t\tconst maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\r\n\t\t\t\tconst maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\r\n\r\n\t\t\t\ttargetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\r\n\t\t\t\ttargetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\r\n\r\n\t\t\t\tconsole.log(\"📍 Calculated hotspot scroll position:\", {\r\n\t\t\t\t\ttargetScrollTop,\r\n\t\t\t\t\ttargetScrollLeft,\r\n\t\t\t\t\tcurrentScrollY: window.scrollY,\r\n\t\t\t\t\tcurrentScrollX: window.scrollX,\r\n\t\t\t\t\telementRect: rect\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Perform the scroll with multiple fallback methods\r\n\t\t\t\tlet scrollSuccess = false;\r\n\r\n\t\t\t\t// Method 1: Try smooth scrolling\r\n\t\t\t\ttry {\r\n\t\t\t\t\tscrollSuccess = universalScrollTo(window, {\r\n\t\t\t\t\t\ttop: targetScrollTop,\r\n\t\t\t\t\t\tleft: targetScrollLeft,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(\"✅ Universal hotspot scroll success:\", scrollSuccess);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"❌ Universal hotspot scroll failed:\", error);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 2: Fallback to immediate scroll if smooth scroll failed\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo(targetScrollLeft, targetScrollTop);\r\n\t\t\t\t\t\tscrollSuccess = true;\r\n\t\t\t\t\t\tconsole.log(\"✅ Fallback hotspot scroll successful\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Fallback hotspot scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 3: Final fallback using direct property assignment\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tdocument.documentElement.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.documentElement.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tdocument.body.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.body.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tconsole.log(\"✅ Direct property assignment completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Direct property assignment failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Small delay to allow scroll to complete\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n\t\t\t\tconsole.log(\"🏁 Auto-scroll operation completed\");\r\n\t\t\t});\r\n\r\n\t\t\tawait scrollOperationQueue;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"❌ ScrollToTargetElement error:\", error);\r\n\t\t}\r\n\t}, [smoothScrollTo, universalScrollTo]);\r\n\r\n\t// Enhanced useEffect for step changes - ensures proper hotspot positioning\r\n\tuseEffect(() => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]) {\r\n\t\t\tconsole.log(\"🎯 Step change detected for hotspot, ensuring proper positioning:\", {\r\n\t\t\t\tcurrentStep,\r\n\t\t\t\tstepData: savedGuideData.GuideStep[currentStep - 1]\r\n\t\t\t});\r\n\r\n\t\t\t// Small delay to allow DOM to settle after step change\r\n\t\t\tconst positioningTimeout = setTimeout(() => {\r\n\t\t\t\tconst currentStepData = savedGuideData.GuideStep[currentStep - 1];\r\n\t\t\t\tconst elementPath = currentStepData?.ElementPath;\r\n\r\n\t\t\t\tif (elementPath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Finding element for step positioning:\", elementPath);\r\n\r\n\t\t\t\t\t// Use polling to ensure element is found\r\n\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\telementPath,\r\n\t\t\t\t\t\t\"\", // No fallback path\r\n\t\t\t\t\t\tasync (foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Element found for step positioning, updating hotspot position\");\r\n\r\n\t\t\t\t\t\t\t// Update target element state\r\n\t\t\t\t\t\t\tsetTargetElement(foundElement);\r\n\r\n\t\t\t\t\t\t\t// Get hotspot properties for current step\r\n\t\t\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData?.[currentStep - 1]?.hotspots || {\r\n\t\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\t\tSize: \"30\"\r\n\t\t\t\t\t\t\t};\r\n\r\n\t\t\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t\t\t// Update hotspot size\r\n\t\t\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\t\t\t// Calculate correct position\r\n\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\r\n\t\t\t\t\t\t\t// Update existing hotspot position immediately\r\n\t\t\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\t\t\tconst left = rect.x + xOffset;\r\n\t\t\t\t\t\t\t\tconst top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t\t\texistingHotspot.style.left = `${left}px`;\r\n\t\t\t\t\t\t\t\texistingHotspot.style.top = `${top}px`;\r\n\t\t\t\t\t\t\t\texistingHotspot.style.width = `${currentHotspotSize}px`;\r\n\t\t\t\t\t\t\t\texistingHotspot.style.height = `${currentHotspotSize}px`;\r\n\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot position updated immediately:\", { left, top, size: currentHotspotSize });\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t// Auto-scroll to element if needed\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\tawait scrollToTargetElement(foundElement, \"top\");\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Auto-scroll completed for step change\");\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"❌ Auto-scroll failed for step change:\", error);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t15, // Max attempts\r\n\t\t\t\t\t\t50, // Initial interval\r\n\t\t\t\t\t\t`Step ${currentStep} hotspot element`,\r\n\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\tconsole.log(`❌ Element not found for step ${currentStep}, hotspot may not position correctly`);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\t\t\t}, 100); // Allow DOM to settle\r\n\r\n\t\t\treturn () => {\r\n\t\t\t\tclearTimeout(positioningTimeout);\r\n\t\t\t};\r\n\t\t}\r\n\t}, [currentStep, selectedTemplateTour, savedGuideData, toolTipGuideMetaData, pollForElement, scrollToTargetElement]);\r\n\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Smart auto-scroll for current hotspot step changes - only when element is not visible\r\n\tuseEffect(() => {\r\n\t\tconst currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n\t\tif (!currentStepData?.ElementPath) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Small delay to allow DOM to settle\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\tconst currentElement = getElementByXPath(currentStepData.ElementPath || \"\");\r\n\r\n\t\t\tif (currentElement) {\r\n\t\t\t\tconst rect = currentElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Check if element is completely out of view\r\n\t\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (isCompletelyOutOfView) {\r\n\t\t\t\t\tconsole.log(\"🔄 Current hotspot step element is out of view, gentle auto-scroll\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tcurrentElement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"Current hotspot step auto-scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 100);\r\n\r\n\t\treturn () => clearTimeout(timeoutId);\r\n\t}, [currentStep, savedGuideData]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\t// Enhanced auto-scroll: Check if next element needs scrolling before navigation\r\n\t\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[currentStep]; // currentStep is 0-based for next step\r\n\t\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Enhanced checking auto-scroll for next hotspot element:\", {\r\n\t\t\t\t\t\txpath: nextStepData.ElementPath,\r\n\t\t\t\t\t\tstepIndex: currentStep + 1,\r\n\t\t\t\t\t\tstepTitle: `Step ${currentStep + 1}`\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Use advanced polling and scrolling\r\n\t\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\t\tnextStepData.ElementPath || \"\",\r\n\t\t\t\t\t\t\t\"\", // No fallback path for hotspots\r\n\t\t\t\t\t\t\tasync (foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next hotspot element found, checking visibility\");\r\n\r\n\t\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t\t// Enhanced visibility check\r\n\t\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\t\trect.top >= -50 && rect.top <= viewportHeight - 100 &&\r\n\t\t\t\t\t\t\t\t\trect.left >= -50 && rect.left <= viewportWidth - 100 &&\r\n\t\t\t\t\t\t\t\t\trect.bottom > 100 && rect.right > 100\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Next hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal hotspot scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next hotspot element not visible, performing enhanced auto-scroll\");\r\n\t\t\t\t\t\t\t\t\t// Element is not visible, use advanced scrolling\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tawait scrollToTargetElement(foundElement, \"top\");\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Enhanced hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Enhanced hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\t\"Next hotspot element\",\r\n\t\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"❌ Next hotspot element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\t\tconsole.log(\"✅ Hotspot element finding and scroll check completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(\"❌ Hotspot scroll promise failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// Enhanced Tour template logic\r\n\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[currentStep]; // currentStep is 0-based for next step\r\n\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\tconsole.log(\"🔍 Enhanced checking auto-scroll for next tour hotspot element:\", {\r\n\t\t\t\t\txpath: nextStepData.ElementPath,\r\n\t\t\t\t\tstepIndex: currentStep + 1,\r\n\t\t\t\t\tstepTitle: `Step ${currentStep + 1}`\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Use advanced polling and scrolling for tour hotspots too\r\n\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\tnextStepData.ElementPath || \"\",\r\n\t\t\t\t\t\t\"\", // No fallback path for hotspots\r\n\t\t\t\t\t\tasync (foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\tconsole.log(\"🎯 Next tour hotspot element found, checking visibility\");\r\n\r\n\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t// Enhanced visibility check\r\n\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\trect.top >= -50 && rect.top <= viewportHeight - 100 &&\r\n\t\t\t\t\t\t\t\trect.left >= -50 && rect.left <= viewportWidth - 100 &&\r\n\t\t\t\t\t\t\t\trect.bottom > 100 && rect.right > 100\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Next tour hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal tour hotspot scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next tour hotspot element not visible, performing enhanced auto-scroll\");\r\n\t\t\t\t\t\t\t\t// Element is not visible, use advanced scrolling\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tawait scrollToTargetElement(foundElement, \"top\");\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Enhanced tour hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Enhanced tour hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\"Next tour hotspot element\",\r\n\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Next tour hotspot element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\tconsole.log(\"✅ Tour hotspot element finding and scroll check completed\");\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"❌ Tour hotspot scroll promise failed:\", error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = async () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tconsole.log(\"🔙 Navigating to previous hotspot step:\", {\r\n\t\t\t\tcurrentStep: currentStep,\r\n\t\t\t\tprevStep: currentStep - 1\r\n\t\t\t});\r\n\r\n\t\t\t// Smart previous navigation scrolling\r\n\t\t\tif (currentStep - 1 === 1) {\r\n\t\t\t\tconsole.log(\"HandlePrevious: Going back to first hotspot step, scroll to top\");\r\n\t\t\t\ttry {\r\n\t\t\t\t\twindow.scrollTo({\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Scroll to top failed:\", error);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// For other steps, check if previous step element needs scrolling\r\n\t\t\t\tconst prevStepData = savedGuideData?.GuideStep?.[currentStep - 2]; // currentStep - 2 for 0-based array\r\n\t\t\t\tif (prevStepData?.ElementPath) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Checking if previous hotspot element needs scrolling\");\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst prevElement = getElementByXPath(prevStepData.ElementPath || \"\");\r\n\t\t\t\t\t\tif (prevElement) {\r\n\t\t\t\t\t\t\tconst rect = prevElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\tconst isOutOfView = (\r\n\t\t\t\t\t\t\t\trect.bottom < 0 ||\r\n\t\t\t\t\t\t\t\trect.top > window.innerHeight ||\r\n\t\t\t\t\t\t\t\trect.right < 0 ||\r\n\t\t\t\t\t\t\t\trect.left > window.innerWidth\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (isOutOfView) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Previous hotspot element out of view, scrolling\");\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tprevElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Hotspot element scroll failed:\", error);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\tconst calculateOptimalWidth = () => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\treturn `${canvasProperties.Width}px`;\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, use auto width\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\treturn \"auto\";\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = 800; // Maximum width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn `${finalWidth}px`;\r\n\t};\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newWidth = calculateOptimalWidth();\r\n\t\t\tsetDynamicWidth(newWidth);\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep]);\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t\t  canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"800px\",\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t   canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"300px\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\r\n\t\t\t\tconsole.log(\"🔄 Restarting hotspot tour, checking first step element\");\r\n\r\n\t\t\t\t// Enhanced restart scrolling logic\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\t// Check if first step element exists and use gentle scroll\r\n\t\t\t\t\tconst firstElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstElement) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tfirstElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Gentle restart scroll to first hotspot completed\");\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Gentle restart scroll failed, scrolling to top\");\r\n\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(\"❌ First hotspot step element not found, scrolling to top\");\r\n\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// No xpath available, just scroll to top\r\n\t\t\t\t\tconsole.log(\"ℹ️ No xpath for first hotspot step, scrolling to top\");\r\n\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\tconsole.log(\"🎯 Fetching element for hotspot positioning:\", {\r\n\t\t\t\t\telementPath,\r\n\t\t\t\t\tselectedTemplateTour,\r\n\t\t\t\t\tcurrentStep,\r\n\t\t\t\t\tisHotspotStep: selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t});\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\t// If element not found immediately, try polling (especially important for step transitions)\r\n\t\t\t\tif (!element && elementPath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Element not found immediately, starting polling...\");\r\n\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\telementPath,\r\n\t\t\t\t\t\t\"\", // No fallback path\r\n\t\t\t\t\t\t(foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Element found via polling, updating state\");\r\n\t\t\t\t\t\t\telement = foundElement;\r\n\t\t\t\t\t\t\tsetTargetElement(foundElement);\r\n\r\n\t\t\t\t\t\t\t// Trigger a re-render by updating a state that will cause the positioning logic to run again\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconsole.log(\"📍 Element rect after polling:\", rect);\r\n\t\t\t\t\t\t\t}, 50);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t10, // Max attempts\r\n\t\t\t\t\t\t100, // Initial interval\r\n\t\t\t\t\t\t\"Main hotspot element\",\r\n\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Element not found after polling in main logic\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\r\n\t\t\t\t\t\t// Ensure element has valid dimensions (not hidden or not yet rendered)\r\n\t\t\t\t\t\tif (rect.width > 0 && rect.height > 0) {\r\n\t\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot positioned successfully:\", {\r\n\t\t\t\t\t\t\t\telementRect: rect,\r\n\t\t\t\t\t\t\t\thotspotPosition: { left, top },\r\n\t\t\t\t\t\t\t\tpopupPosition: popupPos\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log(\"⚠️ Element found but has no dimensions, may be hidden:\", rect);\r\n\t\t\t\t\t\t\t// Set default position to prevent hotspot from appearing at (0,0)\r\n\t\t\t\t\t\t\tleft = 100;\r\n\t\t\t\t\t\t\ttop = 100;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(\"❌ No element found for hotspot positioning\");\r\n\t\t\t\t\t\t// Set default position to prevent hotspot from appearing at (0,0)\r\n\t\t\t\t\t\tleft = 100;\r\n\t\t\t\t\t\ttop = 100;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top +10+ (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left +10+ parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,CAAEC,WAAW,KAAQ,OAAO,CACvE,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,cAAc,CAAEC,aAAa,CAAEC,OAAO,CAAiBC,UAAU,KAAQ,eAAe,CAE1H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE;AACA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA6GrD,KAAM,CAAAC,cAAoC,CAAGC,IAAA,EA8BvC,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IA9BwC,CAC1CC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,kBAAkB,CAClBC,kBAAkB,CAClBC,kBAAkB,CACnBC,kBAEH,CAAC,CAAAvC,IAAA,CACA,KAAM,CACLwC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,aACD,CAAC,CAAG9D,cAAc,CAAE+D,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG5E,QAAQ,CAAqB,IAAI,CAAC,CAC5E;AACA;AACA,KAAM,CAAC6E,aAAa,CAAEC,gBAAgB,CAAC,CAAG9E,QAAQ,CAAuC,IAAI,CAAC,CAC9F,KAAM,CAAC+E,YAAY,CAAEC,eAAe,CAAC,CAAGhF,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACiF,WAAW,CAAEC,cAAc,CAAC,CAAGlF,QAAQ,CAAS,EAAE,CAAC,CAAE;AAC5D,KAAM,CAAAmF,UAAU,CAAGlF,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAAmF,kBAAkB,CAAGnF,MAAM,CAAiB,IAAI,CAAC,CACvD,GAAI,CAAAoF,OAAY,CAChB,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAChE,GAAI,CAACA,KAAK,CAAE,MAAO,KAAI,CACvB,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,CAAEE,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CACnC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CAChC,MAAO,CAAAF,IAAI,CACZ,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAC/B,MAAO,CAAAH,IAAI,CAACG,aAAa,CAAE;AAC5B,CAAC,IAAM,CACN,MAAO,KAAI,CACZ,CACD,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAG,QAAAA,CAACC,OAAoB,CAAEC,SAAiB,CAA6B,IAA3B,CAAAC,QAAgB,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,GAAG,CACtF;AACA,KAAM,CAAAG,SAAS,CAAGN,OAAO,CAACO,YAAY,CAAGP,OAAO,CAACQ,YAAY,CAC7D,KAAM,CAAAC,gBAAgB,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAACX,SAAS,CAAEK,SAAS,CAAC,CAAC,CAEpE;AACA,GAAI,CACH,GAAI,UAAU,EAAI,CAAAN,OAAO,EAAI,MAAO,CAAAA,OAAO,CAACa,QAAQ,GAAK,UAAU,CAAE,CACpEb,OAAO,CAACa,QAAQ,CAAC,CAChBC,GAAG,CAAEL,gBAAgB,CACrBM,QAAQ,CAAE,QACX,CAAC,CAAC,CACF,OACD,CACD,CAAE,MAAOC,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACxE,CAEA;AACA,GAAI,CACH,KAAM,CAAAC,QAAQ,CAAGnB,OAAO,CAACoB,SAAS,CAClC,KAAM,CAAAC,QAAQ,CAAGZ,gBAAgB,CAAGU,QAAQ,CAC5C,KAAM,CAAAG,SAAS,CAAGC,WAAW,CAACC,GAAG,CAAC,CAAC,CAEnC,KAAM,CAAAC,aAAa,CAAIC,WAAmB,EAAK,CAC9C,KAAM,CAAAC,OAAO,CAAGD,WAAW,CAAGJ,SAAS,CACvC,KAAM,CAAA5E,QAAQ,CAAGgE,IAAI,CAACE,GAAG,CAACe,OAAO,CAAGzB,QAAQ,CAAE,CAAC,CAAC,CAEhD;AACA,KAAM,CAAA0B,cAAc,CAAIC,CAAS,EAAKA,CAAC,CAAG,GAAG,CAAG,CAAC,CAAGA,CAAC,CAAGA,CAAC,CAAGA,CAAC,CAAG,CAACA,CAAC,CAAG,CAAC,GAAK,CAAC,CAAGA,CAAC,CAAG,CAAC,CAAC,EAAI,CAAC,CAAGA,CAAC,CAAG,CAAC,CAAC,CAAG,CAAC,CACvG,KAAM,CAAAC,aAAa,CAAGF,cAAc,CAAClF,QAAQ,CAAC,CAE9CsD,OAAO,CAACoB,SAAS,CAAGD,QAAQ,CAAIE,QAAQ,CAAGS,aAAc,CAEzD,GAAIpF,QAAQ,CAAG,CAAC,CAAE,CACjBqF,qBAAqB,CAACN,aAAa,CAAC,CACrC,CACD,CAAC,CAEDM,qBAAqB,CAACN,aAAa,CAAC,CACrC,CAAE,MAAOT,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpE;AACAlB,OAAO,CAACoB,SAAS,CAAGX,gBAAgB,CACrC,CACD,CAAC,CAED;AACA,KAAM,CAAAuB,iBAAiB,CAAGA,CAAChC,OAA6B,CAAEiC,OAAsE,GAAK,CACpI,KAAM,CAAAC,QAAQ,CAAGlC,OAAO,GAAKmC,MAAM,CACnC,KAAM,CAAA1D,aAAa,CAAGyD,QAAQ,CAAG3C,QAAQ,CAAC6C,eAAe,CAAGpC,OAAsB,CAElF;AACA,GAAI,CAACkC,QAAQ,EAAI,UAAU,EAAI,CAAAlC,OAAO,EAAI,MAAQ,CAAAA,OAAO,CAASa,QAAQ,GAAK,UAAU,CAAE,CAC1F,GAAI,CACFb,OAAO,CAASa,QAAQ,CAACoB,OAAO,CAAC,CAClC,MAAO,KAAI,CACZ,CAAE,MAAOjB,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEF,KAAK,CAAC,CACzD,CACD,CAEA;AACA,GAAIkB,QAAQ,EAAID,OAAO,CAAClB,QAAQ,GAAK,QAAQ,CAAE,CAC9C,GAAI,CACHoB,MAAM,CAACtB,QAAQ,CAACoB,OAAO,CAAC,CACxB,MAAO,KAAI,CACZ,CAAE,MAAOjB,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAEF,KAAK,CAAC,CAC9C,CACD,CAEA;AACA,GAAIiB,OAAO,CAAClB,QAAQ,GAAK,QAAQ,EAAIkB,OAAO,CAACnB,GAAG,GAAKT,SAAS,CAAE,CAC/D,GAAI,CACHN,cAAc,CAACtB,aAAa,CAAEwD,OAAO,CAACnB,GAAG,CAAC,CAC1C,MAAO,KAAI,CACZ,CAAE,MAAOE,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAEF,KAAK,CAAC,CACtD,CACD,CAEA;AACA,GAAI,CACH,GAAIiB,OAAO,CAACnB,GAAG,GAAKT,SAAS,CAAE,CAC9B5B,aAAa,CAAC2C,SAAS,CAAGa,OAAO,CAACnB,GAAG,CACtC,CACA,GAAImB,OAAO,CAACI,IAAI,GAAKhC,SAAS,CAAE,CAC/B5B,aAAa,CAAC6D,UAAU,CAAGL,OAAO,CAACI,IAAI,CACxC,CACA,MAAO,KAAI,CACZ,CAAE,MAAOrB,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEF,KAAK,CAAC,CACxD,MAAO,MAAK,CACb,CACD,CAAC,CAED;AACA,KAAM,CAAAuB,cAAc,CAAGvI,WAAW,CAAC,SAClCqF,KAAa,CACbmD,mBAA2B,CAC3BC,cAA8C,CAK1C,IAJJ,CAAAC,WAAmB,CAAAvC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IACxB,CAAAwC,iBAAyB,CAAAxC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,IAC9B,CAAAyC,WAAmB,CAAAzC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,SAAS,IAC/B,CAAA0C,SAAsB,CAAA1C,SAAA,CAAAC,MAAA,GAAAD,SAAA,IAAAE,SAAA,CAEtB,GAAI,CAAAyC,QAAQ,CAAG,CAAC,CAChB,GAAI,CAAAC,eAAe,CAAGJ,iBAAiB,CACvC,GAAI,CAAAK,SAAyB,CAE7B,KAAM,CAAAC,IAAI,CAAGA,CAAA,GAAM,CAClBH,QAAQ,EAAE,CACV7B,OAAO,CAACC,GAAG,CAAC,MAAM0B,WAAW,oBAAoBE,QAAQ,IAAIJ,WAAW,EAAE,CAAC,CAE3E;AACA,GAAI,CAAA1C,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACtC,GAAI,CAACW,OAAO,EAAIwC,mBAAmB,CAAE,CACpCxC,OAAO,CAAGZ,iBAAiB,CAACoD,mBAAmB,CAAC,CACjD,CAEA,GAAIxC,OAAO,CAAE,CACZiB,OAAO,CAACC,GAAG,CAAC,KAAK0B,WAAW,gBAAgBE,QAAQ,WAAW,CAAC,CAChEL,cAAc,CAACzC,OAAO,CAAC,CACvB,OACD,CAEA,GAAI8C,QAAQ,EAAIJ,WAAW,CAAE,CAC5BzB,OAAO,CAACC,GAAG,CAAC,KAAK0B,WAAW,oBAAoBF,WAAW,WAAW,CAAC,CACvE,GAAIG,SAAS,CAAE,CACdA,SAAS,CAAC,CAAC,CACZ,CACA,OACD,CAEA;AACA,KAAM,CAAAK,MAAM,CAAGxC,IAAI,CAACyC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAGJ,eAAe,CACpD,KAAM,CAAAK,YAAY,CAAG1C,IAAI,CAACE,GAAG,CAACmC,eAAe,CAAG,GAAG,CAAGG,MAAM,CAAE,GAAG,CAAC,CAAE;AACpEH,eAAe,CAAGK,YAAY,CAE9BJ,SAAS,CAAGK,UAAU,CAACJ,IAAI,CAAEF,eAAe,CAAC,CAC9C,CAAC,CAED;AACAE,IAAI,CAAC,CAAC,CAEN;AACA,KAAM,CAAAK,OAAO,CAAGA,CAAA,GAAM,CACrB,GAAIN,SAAS,CAAE,CACdO,YAAY,CAACP,SAAS,CAAC,CACxB,CACD,CAAC,CAED;AACA,MAAO,CAAAM,OAAO,CACf,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAE,qBAAqB,CAAGxJ,WAAW,CAAC,eAAOyE,aAA0B,CAA6D,IAA3D,CAAAgF,SAA8C,CAAAtD,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CAClI,GAAI,CAAC1B,aAAa,CAAE,CACnBwC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChE,OACD,CAEAD,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAE,CAC1ElB,OAAO,CAAEvB,aAAa,CACtBiF,OAAO,CAAEjF,aAAa,CAACiF,OAAO,CAC9BC,SAAS,CAAElF,aAAa,CAACkF,SAAS,CAClCC,EAAE,CAAEnF,aAAa,CAACmF,EAAE,CACpBH,SAAS,CAAEA,SACZ,CAAC,CAAC,CAEF,GAAI,CACH;AACA,KAAM,CAAAI,oBAAoB,CAAGC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,SAAY,CAC/D,KAAM,CAAAC,IAAI,CAAGxF,aAAa,CAACyF,qBAAqB,CAAC,CAAC,CAClD,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAACiC,WAAW,CACzC,KAAM,CAAAC,aAAa,CAAGlC,MAAM,CAACmC,UAAU,CAEvC;AACA,GAAI,CAAAC,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CACpC,GAAI,CAAAC,gBAAgB,CAAGtC,MAAM,CAACuC,OAAO,CAErC;AACA,OAAQjB,SAAS,EAChB,IAAK,KAAK,CACT;AACAc,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CAAGP,IAAI,CAACnD,GAAG,CAAIqD,cAAc,CAAG,GAAI,CACpE,MACD,IAAK,QAAQ,CACZ;AACAI,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CAAGP,IAAI,CAACnD,GAAG,CAAIqD,cAAc,CAAG,GAAI,CACpE,MACD,IAAK,MAAM,CACV;AACAI,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CAAGP,IAAI,CAACnD,GAAG,CAAIqD,cAAc,CAAG,GAAI,CACpEM,gBAAgB,CAAGtC,MAAM,CAACuC,OAAO,CAAGT,IAAI,CAAC5B,IAAI,CAAIgC,aAAa,CAAG,GAAI,CACrE,MACD,IAAK,OAAO,CACX;AACAE,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CAAGP,IAAI,CAACnD,GAAG,CAAIqD,cAAc,CAAG,GAAI,CACpEM,gBAAgB,CAAGtC,MAAM,CAACuC,OAAO,CAAGT,IAAI,CAAC5B,IAAI,CAAIgC,aAAa,CAAG,GAAI,CACrE,MACD,QACC;AACAE,eAAe,CAAGpC,MAAM,CAACqC,OAAO,CAAGP,IAAI,CAACnD,GAAG,CAAIqD,cAAc,CAAG,GAAI,CACtE,CAEA;AACA,KAAM,CAAAQ,YAAY,CAAGjE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEpB,QAAQ,CAAC6C,eAAe,CAAC7B,YAAY,CAAG4D,cAAc,CAAC,CACxF,KAAM,CAAAS,aAAa,CAAGlE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAEpB,QAAQ,CAAC6C,eAAe,CAACyC,WAAW,CAAGR,aAAa,CAAC,CAEvFE,eAAe,CAAG7D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC2D,eAAe,CAAEI,YAAY,CAAC,CAAC,CACtEF,gBAAgB,CAAG/D,IAAI,CAACC,GAAG,CAAC,CAAC,CAAED,IAAI,CAACE,GAAG,CAAC6D,gBAAgB,CAAEG,aAAa,CAAC,CAAC,CAEzE3D,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAE,CACrDqD,eAAe,CACfE,gBAAgB,CAChBK,cAAc,CAAE3C,MAAM,CAACqC,OAAO,CAC9BO,cAAc,CAAE5C,MAAM,CAACuC,OAAO,CAC9BM,WAAW,CAAEf,IACd,CAAC,CAAC,CAEF;AACA,GAAI,CAAAgB,aAAa,CAAG,KAAK,CAEzB;AACA,GAAI,CACHA,aAAa,CAAGjD,iBAAiB,CAACG,MAAM,CAAE,CACzCrB,GAAG,CAAEyD,eAAe,CACpBlC,IAAI,CAAEoC,gBAAgB,CACtB1D,QAAQ,CAAE,QACX,CAAC,CAAC,CACFE,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAE+D,aAAa,CAAC,CAClE,CAAE,MAAOjE,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAEF,KAAK,CAAC,CACzD,CAEA;AACA,GAAI,CAACiE,aAAa,CAAE,CACnB,GAAI,CACH9C,MAAM,CAACtB,QAAQ,CAAC4D,gBAAgB,CAAEF,eAAe,CAAC,CAClDU,aAAa,CAAG,IAAI,CACpBhE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC,CACpD,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAEF,KAAK,CAAC,CACxD,CACD,CAEA;AACA,GAAI,CAACiE,aAAa,CAAE,CACnB,GAAI,CACH1F,QAAQ,CAAC6C,eAAe,CAAChB,SAAS,CAAGmD,eAAe,CACpDhF,QAAQ,CAAC6C,eAAe,CAACE,UAAU,CAAGmC,gBAAgB,CACtDlF,QAAQ,CAAC2F,IAAI,CAAC9D,SAAS,CAAGmD,eAAe,CACzChF,QAAQ,CAAC2F,IAAI,CAAC5C,UAAU,CAAGmC,gBAAgB,CAC3CxD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC,CACtD,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAEF,KAAK,CAAC,CAC3D,CACD,CAEA;AACA,KAAM,IAAI,CAAA8C,OAAO,CAACC,OAAO,EAAIV,UAAU,CAACU,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD9C,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAClD,CAAC,CAAC,CAEF,KAAM,CAAA2C,oBAAoB,CAC3B,CAAE,MAAO7C,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACvD,CACD,CAAC,CAAE,CAACjB,cAAc,CAAEiC,iBAAiB,CAAC,CAAC,CAEvC;AACAnI,SAAS,CAAC,IAAM,KAAAsL,qBAAA,CACf,GAAI9G,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAA+H,qBAAA,CAAd/H,cAAc,CAAEgI,SAAS,UAAAD,qBAAA,WAAzBA,qBAAA,CAA4B5I,WAAW,CAAG,CAAC,CAAC,CAAE,CACvF0E,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAE,CAChF3E,WAAW,CACX8I,QAAQ,CAAEjI,cAAc,CAACgI,SAAS,CAAC7I,WAAW,CAAG,CAAC,CACnD,CAAC,CAAC,CAEF;AACA,KAAM,CAAA+I,kBAAkB,CAAGjC,UAAU,CAAC,IAAM,CAC3C,KAAM,CAAAkC,eAAe,CAAGnI,cAAc,CAACgI,SAAS,CAAC7I,WAAW,CAAG,CAAC,CAAC,CACjE,KAAM,CAAAiJ,WAAW,CAAGD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEE,WAAW,CAEhD,GAAID,WAAW,CAAE,CAChBvE,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAEsE,WAAW,CAAC,CAEpE;AACAjD,cAAc,CACbiD,WAAW,CACX,EAAE,CAAE;AACJ,KAAO,CAAAE,YAAyB,EAAK,KAAAC,qBAAA,CACpC1E,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC,CAE9E;AACAxC,gBAAgB,CAACgH,YAAY,CAAC,CAE9B;AACA,KAAM,CAAAE,eAAe,CAAG,CAAAhI,oBAAoB,SAApBA,oBAAoB,kBAAA+H,qBAAA,CAApB/H,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAoJ,qBAAA,iBAAvCA,qBAAA,CAAyCE,QAAQ,GAAI,CAC5EC,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,IAAI,CAAE,IACP,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGC,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEE,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,OAAO,CAAGD,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,kBAAkB,CAAGF,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,IAAI,GAAI,IAAI,CAAC,CAEpE;AACAhH,cAAc,CAACoH,kBAAkB,CAAC,CAElC;AACA,KAAM,CAAAnC,IAAI,CAAGyB,YAAY,CAACxB,qBAAqB,CAAC,CAAC,CACjD,KAAM,CAAAmC,QAAQ,CAAGC,sBAAsB,CAACrC,IAAI,CAAEmC,kBAAkB,CAAEH,OAAO,CAAEE,OAAO,CAAC,CACnFvH,gBAAgB,CAACyH,QAAQ,CAAC,CAE1B;AACA,KAAM,CAAAE,eAAe,CAAGhH,QAAQ,CAACiH,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpB,KAAM,CAAAlE,IAAI,CAAG4B,IAAI,CAACwC,CAAC,CAAGR,OAAO,CAC7B,KAAM,CAAAnF,GAAG,CAAGmD,IAAI,CAACyC,CAAC,EAAIP,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAGzF,IAAI,CAACiG,GAAG,CAACR,OAAO,CAAC,CAAC,CAEjEI,eAAe,CAACK,KAAK,CAACvE,IAAI,CAAG,GAAGA,IAAI,IAAI,CACxCkE,eAAe,CAACK,KAAK,CAAC9F,GAAG,CAAG,GAAGA,GAAG,IAAI,CACtCyF,eAAe,CAACK,KAAK,CAACC,KAAK,CAAG,GAAGT,kBAAkB,IAAI,CACvDG,eAAe,CAACK,KAAK,CAACE,MAAM,CAAG,GAAGV,kBAAkB,IAAI,CAExDnF,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAE,CAAEmB,IAAI,CAAEvB,GAAG,CAAEiG,IAAI,CAAEX,kBAAmB,CAAC,CAAC,CAChG,CAEA;AACA,GAAI,CACH,KAAM,CAAA5C,qBAAqB,CAACkC,YAAY,CAAE,KAAK,CAAC,CAChDzE,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACvD,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEF,KAAK,CAAC,CAC5D,CACD,CAAC,CACD,EAAE,CAAE;AACJ,EAAE,CAAE;AACJ,QAAQzE,WAAW,kBAAkB,CACrC,IAAM,CACL0E,OAAO,CAACC,GAAG,CAAC,gCAAgC3E,WAAW,sCAAsC,CAAC,CAC/F,CACD,CAAC,CACF,CACD,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAM,CACZgH,YAAY,CAAC+B,kBAAkB,CAAC,CACjC,CAAC,CACF,CACD,CAAC,CAAE,CAAC/I,WAAW,CAAE8B,oBAAoB,CAAEjB,cAAc,CAAEQ,oBAAoB,CAAE2E,cAAc,CAAEiB,qBAAqB,CAAC,CAAC,CAEpH,GAAI,CAAAnE,KAAU,CACd,GAAIjC,cAAc,CAAEiC,KAAK,CAAGjC,cAAc,SAAdA,cAAc,kBAAAjC,sBAAA,CAAdiC,cAAc,CAAEgI,SAAS,UAAAjK,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,iBAA9BA,sBAAA,CAAgCqK,WAAW,CACvE,KAAM,CAAAuB,kBAAkB,CAAI3H,KAAyB,EAAK,CACzD,KAAM,CAAAW,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,EAAI,EAAE,CAAC,CAC9C,GAAIW,OAAO,CAAE,CACZ,KAAM,CAAAiE,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAC5C,MAAO,CACNpD,GAAG,CAAEmD,IAAI,CAACnD,GAAG,CAAE;AACfuB,IAAI,CAAE4B,IAAI,CAAC5B,IAAM;AAClB,CAAC,CACF,CACA,MAAO,KAAI,CACZ,CAAC,CACC;AACA,KAAM,CAAC4E,cAAc,CAAEC,iBAAiB,CAAC,CAAGpN,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAqN,YAAY,CAAGpN,MAAM,CAAM,IAAI,CAAC,CACxC;AACA,KAAM,CAAAuM,sBAAsB,CAAGA,CAACtB,WAAoB,CAAEjG,WAAmB,CAAEkH,OAAe,CAAEE,OAAe,GAAK,CAC/G,KAAM,CAAAiB,WAAW,CAAGpC,WAAW,CAACyB,CAAC,CAAGR,OAAO,CAC3C,KAAM,CAAAoB,UAAU,CAAGrC,WAAW,CAAC0B,CAAC,CAAGP,OAAO,CAE1C;AACA,KAAM,CAAAmB,cAAc,CAAGvI,WAAW,CAAG,CAAC,CAAE;AACxC,KAAM,CAAAwI,cAAc,CAAGxI,WAAW,CAAG,EAAE,CAAE;AAEzC,MAAO,CACN+B,GAAG,CAAEuG,UAAU,CAAGlF,MAAM,CAACqC,OAAO,CAAG+C,cAAc,CACjDlF,IAAI,CAAE+E,WAAW,CAAGjF,MAAM,CAACuC,OAAO,CAAG4C,cACtC,CAAC,CACF,CAAC,CACDzN,SAAS,CAAC,IAAM,CACf,KAAM,CAAAmG,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,CACZ,KAAM,CAAAiE,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAC5CtF,gBAAgB,CAAC,CAChBkC,GAAG,CAAEmD,IAAI,CAACnD,GAAG,CAAGqB,MAAM,CAACqC,OAAO,CAAE;AAChCnC,IAAI,CAAE4B,IAAI,CAAC5B,IAAI,CAAGF,MAAM,CAACuC,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACrF,KAAK,CAAC,CAAC,CACXxF,SAAS,CAAC,IAAM,CACf,GAAI,MAAO,CAAAsI,MAAM,GAAK9B,SAAS,CAAE,CAChC,KAAM,CAAAmH,QAAQ,CAAGR,kBAAkB,CAAC3H,KAAK,EAAI,EAAE,CAAC,CAChD,GAAImI,QAAQ,CAAE,CACb5I,gBAAgB,CAAC4I,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAACnI,KAAK,CAAC,CAAC,CACXxF,SAAS,CAAC,IAAM,CACf,KAAM,CAAAmG,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC;AACA,GAAIW,OAAO,CAAE,CACb,CACD,CAAC,CAAE,CAAC5C,cAAc,CAAC,CAAC,CAEpBvD,SAAS,CAAC,IAAM,KAAA4N,UAAA,CACf,KAAM,CAAAzH,OAAO,CAAGZ,iBAAiB,CAACrD,SAAS,SAATA,SAAS,kBAAA0L,UAAA,CAAT1L,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAkL,UAAA,iBAA5BA,UAAA,CAA8BhC,WAAW,CAAC,CAC5E/G,gBAAgB,CAACsB,OAAO,CAAC,CACzB,GAAIA,OAAO,CAAE,CACZA,OAAO,CAAC4G,KAAK,CAACc,eAAe,CAAG,gBAAgB,CAEhD;AACA,KAAM,CAAAzD,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAC5CtF,gBAAgB,CAAC,CAChBkC,GAAG,CAAEmD,IAAI,CAACnD,GAAG,CAAGqB,MAAM,CAACqC,OAAO,CAC9BnC,IAAI,CAAE4B,IAAI,CAAC5B,IAAI,CAAGF,MAAM,CAACuC,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAAC3I,SAAS,CAAEQ,WAAW,CAAC,CAAC,CAE5B;AACA1C,SAAS,CAAC,IAAM,KAAA8N,sBAAA,CACf,KAAM,CAAApC,eAAe,CAAGnI,cAAc,SAAdA,cAAc,kBAAAuK,sBAAA,CAAdvK,cAAc,CAAEgI,SAAS,UAAAuC,sBAAA,iBAAzBA,sBAAA,CAA4BpL,WAAW,CAAG,CAAC,CAAC,CACpE,GAAI,EAACgJ,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEE,WAAW,EAAE,CAClC,OACD,CAEA;AACA,KAAM,CAAAzC,SAAS,CAAGK,UAAU,CAAC,IAAM,CAClC,KAAM,CAAAuE,cAAc,CAAGxI,iBAAiB,CAACmG,eAAe,CAACE,WAAW,EAAI,EAAE,CAAC,CAE3E,GAAImC,cAAc,CAAE,CACnB,KAAM,CAAA3D,IAAI,CAAG2D,cAAc,CAAC1D,qBAAqB,CAAC,CAAC,CACnD,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAACiC,WAAW,CACzC,KAAM,CAAAC,aAAa,CAAGlC,MAAM,CAACmC,UAAU,CAEvC;AACA,KAAM,CAAAuD,qBAAqB,CAC1B5D,IAAI,CAAC6D,MAAM,CAAG,CAAC,EAAI;AACnB7D,IAAI,CAACnD,GAAG,CAAGqD,cAAc,EAAI;AAC7BF,IAAI,CAAC8D,KAAK,CAAG,CAAC,EAAI;AAClB9D,IAAI,CAAC5B,IAAI,CAAGgC,aAAc;AAC1B,CAED,GAAIwD,qBAAqB,CAAE,CAC1B5G,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC,CACjF,GAAI,CACH0G,cAAc,CAACI,cAAc,CAAC,CAC7BjH,QAAQ,CAAE,QAAQ,CAClBkH,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOlH,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAEF,KAAK,CAAC,CAC/D,CACD,CACD,CACD,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMuC,YAAY,CAACP,SAAS,CAAC,CACrC,CAAC,CAAE,CAACzG,WAAW,CAAEa,cAAc,CAAC,CAAC,CAEjC;AACA;AACA,KAAM,EAAG+K,eAAe,CAAC,CAAGrO,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAAsO,cAAc,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAIzK,gBAAgB,GAAK,MAAM,CAAE,CAChC,GAAIpB,WAAW,CAAGC,UAAU,CAAE,KAAA6L,sBAAA,CAC7B;AACA,KAAM,CAAAC,YAAY,CAAGlL,cAAc,SAAdA,cAAc,kBAAAiL,sBAAA,CAAdjL,cAAc,CAAEgI,SAAS,UAAAiD,sBAAA,iBAAzBA,sBAAA,CAA4B9L,WAAW,CAAC,CAAE;AAC/D,GAAI+L,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE7C,WAAW,CAAE,CAC9BxE,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAE,CACzE7B,KAAK,CAAEiJ,YAAY,CAAC7C,WAAW,CAC/B8C,SAAS,CAAEhM,WAAW,CAAG,CAAC,CAC1BiM,SAAS,CAAE,QAAQjM,WAAW,CAAG,CAAC,EACnC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAkM,aAAa,CAAG,GAAI,CAAA3E,OAAO,CAAQC,OAAO,EAAK,CACpDxB,cAAc,CACb+F,YAAY,CAAC7C,WAAW,EAAI,EAAE,CAC9B,EAAE,CAAE;AACJ,KAAO,CAAAC,YAAyB,EAAK,CACpCzE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CAEjE,KAAM,CAAA+C,IAAI,CAAGyB,YAAY,CAACxB,qBAAqB,CAAC,CAAC,CACjD,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAACiC,WAAW,CACzC,KAAM,CAAAC,aAAa,CAAGlC,MAAM,CAACmC,UAAU,CAEvC;AACA,KAAM,CAAAoE,mBAAmB,CACxBzE,IAAI,CAACnD,GAAG,EAAI,CAAC,EAAE,EAAImD,IAAI,CAACnD,GAAG,EAAIqD,cAAc,CAAG,GAAG,EACnDF,IAAI,CAAC5B,IAAI,EAAI,CAAC,EAAE,EAAI4B,IAAI,CAAC5B,IAAI,EAAIgC,aAAa,CAAG,GAAG,EACpDJ,IAAI,CAAC6D,MAAM,CAAG,GAAG,EAAI7D,IAAI,CAAC8D,KAAK,CAAG,GAClC,CAED,GAAIW,mBAAmB,CAAE,CACxBzH,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC,CAC/E;AACA,GAAI,CACHwE,YAAY,CAACsC,cAAc,CAAC,CAC3BjH,QAAQ,CAAE,QAAQ,CAClBkH,KAAK,CAAE,SAAS,CAAE;AAClBC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOlH,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAEF,KAAK,CAAC,CAChE,CACD,CAAC,IAAM,CACNC,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC,CACnF;AACA,GAAI,CACH,KAAM,CAAAsC,qBAAqB,CAACkC,YAAY,CAAE,KAAK,CAAC,CAChDzE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACrE,CAAE,MAAOyH,WAAW,CAAE,CACrB1H,OAAO,CAACD,KAAK,CAAC,wCAAwC,CAAE2H,WAAW,CAAC,CACrE,CACD,CAEA5E,OAAO,CAAC,CAAC,CACV,CAAC,CACD,EAAE,CAAE;AACJ,EAAE,CAAE;AACJ,sBAAsB,CACtB,IAAM,CACL9C,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC,CACxF6C,OAAO,CAAC,CAAC,CACV,CACD,CAAC,CACF,CAAC,CAAC,CAEF,GAAI,CACH,KAAM,CAAA0E,aAAa,CACnBxH,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC,CACpE,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACzD,CACD,CAEAtD,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BF,UAAU,CAAC,CAAC,CACZuM,eAAe,CAACrM,WAAW,CAAGC,UAAU,CAAC,CAC1C,CACD,CAAC,IAAM,KAAAqM,sBAAA,CACN;AACA,KAAM,CAAAP,YAAY,CAAGlL,cAAc,SAAdA,cAAc,kBAAAyL,sBAAA,CAAdzL,cAAc,CAAEgI,SAAS,UAAAyD,sBAAA,iBAAzBA,sBAAA,CAA4BtM,WAAW,CAAC,CAAE;AAC/D,GAAI+L,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE7C,WAAW,CAAE,CAC9BxE,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAE,CAC9E7B,KAAK,CAAEiJ,YAAY,CAAC7C,WAAW,CAC/B8C,SAAS,CAAEhM,WAAW,CAAG,CAAC,CAC1BiM,SAAS,CAAE,QAAQjM,WAAW,CAAG,CAAC,EACnC,CAAC,CAAC,CAEF;AACA,KAAM,CAAAkM,aAAa,CAAG,GAAI,CAAA3E,OAAO,CAAQC,OAAO,EAAK,CACpDxB,cAAc,CACb+F,YAAY,CAAC7C,WAAW,EAAI,EAAE,CAC9B,EAAE,CAAE;AACJ,KAAO,CAAAC,YAAyB,EAAK,CACpCzE,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CAEtE,KAAM,CAAA+C,IAAI,CAAGyB,YAAY,CAACxB,qBAAqB,CAAC,CAAC,CACjD,KAAM,CAAAC,cAAc,CAAGhC,MAAM,CAACiC,WAAW,CACzC,KAAM,CAAAC,aAAa,CAAGlC,MAAM,CAACmC,UAAU,CAEvC;AACA,KAAM,CAAAoE,mBAAmB,CACxBzE,IAAI,CAACnD,GAAG,EAAI,CAAC,EAAE,EAAImD,IAAI,CAACnD,GAAG,EAAIqD,cAAc,CAAG,GAAG,EACnDF,IAAI,CAAC5B,IAAI,EAAI,CAAC,EAAE,EAAI4B,IAAI,CAAC5B,IAAI,EAAIgC,aAAa,CAAG,GAAG,EACpDJ,IAAI,CAAC6D,MAAM,CAAG,GAAG,EAAI7D,IAAI,CAAC8D,KAAK,CAAG,GAClC,CAED,GAAIW,mBAAmB,CAAE,CACxBzH,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC,CACpF;AACA,GAAI,CACHwE,YAAY,CAACsC,cAAc,CAAC,CAC3BjH,QAAQ,CAAE,QAAQ,CAClBkH,KAAK,CAAE,SAAS,CAAE;AAClBC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOlH,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAEF,KAAK,CAAC,CACrE,CACD,CAAC,IAAM,CACNC,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC,CACxF;AACA,GAAI,CACH,KAAM,CAAAsC,qBAAqB,CAACkC,YAAY,CAAE,KAAK,CAAC,CAChDzE,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC,CAC1E,CAAE,MAAOyH,WAAW,CAAE,CACrB1H,OAAO,CAACD,KAAK,CAAC,6CAA6C,CAAE2H,WAAW,CAAC,CAC1E,CACD,CAEA5E,OAAO,CAAC,CAAC,CACV,CAAC,CACD,EAAE,CAAE;AACJ,EAAE,CAAE;AACJ,2BAA2B,CAC3B,IAAM,CACL9C,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC,CAC7F6C,OAAO,CAAC,CAAC,CACV,CACD,CAAC,CACF,CAAC,CAAC,CAEF,GAAI,CACH,KAAM,CAAA0E,aAAa,CACnBxH,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC,CACzE,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC9D,CACD,CAEAtD,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/B,KAAM,CAAAgK,eAAe,CAAGhH,QAAQ,CAACiH,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACK,KAAK,CAACkC,OAAO,CAAG,MAAM,CACtCvC,eAAe,CAACwC,MAAM,CAAC,CAAC,CACzB,CACD,CACD,CAAC,CAED,KAAM,CAAAH,eAAe,CAAII,qBAA8B,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC3D,MAAO,CAAAV,qBAAqB,cAC3BpO,IAAA,CAACK,cAAc,EACduC,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCH,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCzB,QAAQ,CAAEA,QAAS,CACnBsB,cAAc,CAAEA,cAAe,CAC/BrB,SAAS,CAAEA,SAAU,CACrBI,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEuN,cAAe,CAC3BtN,UAAU,CAAE+L,cAAe,CAC3BpM,KAAK,CAAEA,KAAM,CACbC,IAAI,CAAEA,IAAK,CACXC,QAAQ,CAAEA,QAAS,CACnBK,WAAW,CAAEA,WAAW,CAAG,CAAE,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,eAAe,CAAEA,eAAgB,CACjCC,QAAQ,CAAEA,QAAS,CACnBC,mBAAmB,CAAES,cAAc,SAAdA,cAAc,kBAAA6L,sBAAA,CAAd7L,cAAc,CAAEgI,SAAS,UAAA6D,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B1M,WAAW,CAAC,UAAA2M,sBAAA,iBAAxCA,sBAAA,CAA0CU,mBAAoB,CACnFhN,eAAe,CAAEQ,cAAc,SAAdA,cAAc,kBAAA+L,sBAAA,CAAd/L,cAAc,CAAEgI,SAAS,UAAA+D,sBAAA,kBAAAC,uBAAA,CAAzBD,sBAAA,CAA4B5M,WAAW,CAAC,UAAA6M,uBAAA,iBAAxCA,uBAAA,CAA0CS,eAAgB,CAC3EhN,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAAiM,uBAAA,CAAdjM,cAAc,CAAEgI,SAAS,UAAAiE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9M,WAAW,CAAC,UAAA+M,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CQ,aAAa,UAAAP,uBAAA,kBAAAC,uBAAA,CAAvDD,uBAAA,CAAyDQ,GAAG,CAAEC,OAAY,EACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CAC3C,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAAZ,uBAAA,iBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,CAAEC,IAAS,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACvE,CACDzN,eAAe,CAAEA,eAAgB,CACjCC,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBG,YAAY,CAAEA,YAAa,CAC3BE,iBAAiB,CAAE,CAAAD,cAAc,SAAdA,cAAc,kBAAAqM,uBAAA,CAAdrM,cAAc,CAAEgI,SAAS,UAAAqE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BlN,WAAW,CAAG,CAAC,CAAC,UAAAmN,uBAAA,iBAA5CA,uBAAA,CAA8Ce,OAAO,GAAI,CAAC,CAAE,CAC/E,CAAC,CACC,IAAI,CACT,CAAC,CAED,KAAM,CAAAd,cAAc,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAIpN,WAAW,CAAG,CAAC,CAAE,CACpB0E,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAE,CACtD3E,WAAW,CAAEA,WAAW,CACxBmO,QAAQ,CAAEnO,WAAW,CAAG,CACzB,CAAC,CAAC,CAEF;AACA,GAAIA,WAAW,CAAG,CAAC,GAAK,CAAC,CAAE,CAC1B0E,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC,CAC9E,GAAI,CACHiB,MAAM,CAACtB,QAAQ,CAAC,CACfC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,QACX,CAAC,CAAC,CACH,CAAE,MAAOC,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAEF,KAAK,CAAC,CAC5D,CACD,CAAC,IAAM,KAAA2J,uBAAA,CACN;AACA,KAAM,CAAAC,YAAY,CAAGxN,cAAc,SAAdA,cAAc,kBAAAuN,uBAAA,CAAdvN,cAAc,CAAEgI,SAAS,UAAAuF,uBAAA,iBAAzBA,uBAAA,CAA4BpO,WAAW,CAAG,CAAC,CAAC,CAAE;AACnE,GAAIqO,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAEnF,WAAW,CAAE,CAC9BxE,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC,CAEnFmC,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAwH,WAAW,CAAGzL,iBAAiB,CAACwL,YAAY,CAACnF,WAAW,EAAI,EAAE,CAAC,CACrE,GAAIoF,WAAW,CAAE,CAChB,KAAM,CAAA5G,IAAI,CAAG4G,WAAW,CAAC3G,qBAAqB,CAAC,CAAC,CAChD,KAAM,CAAA4G,WAAW,CAChB7G,IAAI,CAAC6D,MAAM,CAAG,CAAC,EACf7D,IAAI,CAACnD,GAAG,CAAGqB,MAAM,CAACiC,WAAW,EAC7BH,IAAI,CAAC8D,KAAK,CAAG,CAAC,EACd9D,IAAI,CAAC5B,IAAI,CAAGF,MAAM,CAACmC,UACnB,CAED,GAAIwG,WAAW,CAAE,CAChB7J,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC,CAC9E,GAAI,CACH2J,WAAW,CAAC7C,cAAc,CAAC,CAC1BjH,QAAQ,CAAE,QAAQ,CAClBkH,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAE,MAAOlH,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAEF,KAAK,CAAC,CACrE,CACD,CACD,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CACD,CAEAtD,cAAc,CAACnB,WAAW,CAAG,CAAC,CAAC,CAC/BH,UAAU,CAAC,CAAC,CACb,CACD,CAAC,CACDvC,SAAS,CAAC,IAAM,CACf,GAAIsD,YAAY,CAAE,CACjBgL,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACD,CAAC,CAAE,CAAChL,YAAY,CAAC,CAAC,CAClB;AACA,KAAM,CAAA4N,4BAA4B,CACjCvD,QAAgB,EACqD,CACrE,OAAQA,QAAQ,EACf,IAAK,UAAU,CACd,MAAO,CACNwD,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CACrDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,WAAW,CACf,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACtDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CACzD,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,YAAY,CAChB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACvDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,QACC,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAEF,YAAY,CAAEG,eAAgB,CAAC,CAAGJ,4BAA4B,CAAC,CAAAhO,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEqO,QAAQ,GAAI,eAAe,CAAC,CAErH,KAAM,CAAAC,SAAS,CAAG,CACjBC,UAAU,CAAE3O,mBAAmB,SAAnBA,mBAAmB,YAAAtB,qBAAA,CAAnBsB,mBAAmB,CAAE4O,cAAc,UAAAlQ,qBAAA,WAAnCA,qBAAA,CAAqCmQ,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAE9O,mBAAmB,SAAnBA,mBAAmB,YAAArB,sBAAA,CAAnBqB,mBAAmB,CAAE4O,cAAc,UAAAjQ,sBAAA,WAAnCA,sBAAA,CAAqCoQ,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAAhP,mBAAmB,SAAnBA,mBAAmB,kBAAApB,sBAAA,CAAnBoB,mBAAmB,CAAE4O,cAAc,UAAAhQ,sBAAA,iBAAnCA,sBAAA,CAAqCqQ,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAAlP,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEmP,SAAS,GAAI,MAC9C,CAAC,CAED;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC9C;AACA,MAAO,CACNC,MAAM,CAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,CAAE,CAACC,MAAM,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACtF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CACF,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAG5P,eAAe,EAAIA,eAAe,CAACwD,MAAM,CAAG,CAAC,EAC7DxD,eAAe,CAAC6P,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGnQ,mBAAmB,EAAIA,mBAAmB,CAACyD,MAAM,CAAG,CAAC,EACpEzD,mBAAmB,CAAC8P,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAGrQ,YAAY,EAAIA,YAAY,CAACuD,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAA8M,UAAU,EAAI,CAACV,QAAQ,EAAI,CAACM,OAAO,CAC3C,CAAC,CAED;AACA,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAX,QAAQ,CAAG5P,eAAe,EAAIA,eAAe,CAACwD,MAAM,CAAG,CAAC,EAC7DxD,eAAe,CAAC6P,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGnQ,mBAAmB,EAAIA,mBAAmB,CAACyD,MAAM,CAAG,CAAC,EACpEzD,mBAAmB,CAAC8P,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAGrQ,YAAY,EAAIA,YAAY,CAACuD,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAA0M,OAAO,EAAI,CAACN,QAAQ,EAAI,CAACU,UAAU,CAC3C,CAAC,CAED;AACA,KAAM,CAAAE,qBAAqB,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CAAAC,qBAAA,CACnC;AACA,GAAIvQ,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEwQ,KAAK,EAAI,CAAChB,cAAc,CAAC,CAAC,EAAI,CAACY,WAAW,CAAC,CAAC,CAAE,CACnE,MAAO,GAAGpQ,gBAAgB,CAACwQ,KAAK,IAAI,CACrC,CAEA;AACA,GAAIhB,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAE,CACtC,MAAO,MAAM,CACd,CAEA;AACA,KAAM,CAAAK,YAAY,CAAG,EAAAH,mBAAA,CAAApO,UAAU,CAACwO,OAAO,UAAAJ,mBAAA,iBAAlBA,mBAAA,CAAoBxI,WAAW,GAAI,CAAC,CACzD,KAAM,CAAA6I,WAAW,CAAG,EAAAJ,qBAAA,CAAApO,kBAAkB,CAACuO,OAAO,UAAAH,qBAAA,iBAA1BA,qBAAA,CAA4BzI,WAAW,GAAI,CAAC,CAEhE;AACA,KAAM,CAAA8I,YAAY,CAAGjN,IAAI,CAACC,GAAG,CAAC6M,YAAY,CAAEE,WAAW,CAAC,CAExD;AACA,KAAM,CAAAE,WAAW,CAAGD,YAAY,CAAG,EAAE,CAAE;AAEvC;AACA,KAAM,CAAAE,QAAQ,CAAG,GAAG,CAAE;AACtB,KAAM,CAAAC,QAAQ,CAAG,GAAG,CAAE;AAEtB,KAAM,CAAAC,UAAU,CAAGrN,IAAI,CAACC,GAAG,CAACkN,QAAQ,CAAEnN,IAAI,CAACE,GAAG,CAACgN,WAAW,CAAEE,QAAQ,CAAC,CAAC,CAEtE,MAAO,GAAGC,UAAU,IAAI,CACzB,CAAC,CAED;AACAlU,SAAS,CAAC,IAAM,CACf;AACAkI,qBAAqB,CAAC,IAAM,CAC3B,KAAM,CAAAiM,QAAQ,CAAGZ,qBAAqB,CAAC,CAAC,CACxCtO,eAAe,CAACkP,QAAQ,CAAC,CAC1B,CAAC,CAAC,CACH,CAAC,CAAE,CAACrR,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAEN,WAAW,CAAC,CAAC,CAErE;AACA1C,SAAS,CAAC,IAAM,CACf,GAAIwF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAiB,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,KAAAiO,sBAAA,CACZ,KAAM,CAAAhK,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA0B,eAAe,EAAAqI,sBAAA,CAAGrQ,oBAAoB,CAAC,CAAC,CAAC,UAAAqQ,sBAAA,iBAAvBA,sBAAA,CAAyBpI,QAAQ,CACzD,KAAM,CAAAI,OAAO,CAAGC,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEE,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,OAAO,CAAGD,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAM,QAAQ,CAAGC,sBAAsB,CAACrC,IAAI,CAAElF,WAAW,CAAEkH,OAAO,CAAEE,OAAO,CAAC,CAC5EvH,gBAAgB,CAACyH,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAACtH,WAAW,CAAEM,KAAK,CAAEzB,oBAAoB,CAAC,CAAC,CAE9C;AACA/D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAqU,YAAY,CAAGA,CAAA,GAAM,CAC1B,GAAI7O,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAiB,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,KAAAmO,sBAAA,CACZ,KAAM,CAAAlK,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA0B,eAAe,EAAAuI,sBAAA,CAAGvQ,oBAAoB,CAAC,CAAC,CAAC,UAAAuQ,sBAAA,iBAAvBA,sBAAA,CAAyBtI,QAAQ,CACzD,KAAM,CAAAI,OAAO,CAAGC,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEE,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,OAAO,CAAGD,UAAU,CAAC,CAAAN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAM,QAAQ,CAAGC,sBAAsB,CAACrC,IAAI,CAAElF,WAAW,CAAEkH,OAAO,CAAEE,OAAO,CAAC,CAC5EvH,gBAAgB,CAACyH,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAEDlE,MAAM,CAACiM,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAM/L,MAAM,CAACkM,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAChE,CAAC,CAAE,CAAC7O,KAAK,CAAEN,WAAW,CAAEnB,oBAAoB,CAAC,CAAC,CAE9C,KAAM,CAAA0Q,cAAc,CAAGzR,YAAY,CAACwN,MAAM,CAAC,CAACC,GAAQ,CAAEJ,MAAW,GAAK,CACrE,KAAM,CAAAqE,WAAW,CAAGrE,MAAM,CAACC,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACG,GAAG,CAACiE,WAAW,CAAC,CAAE,CACtBjE,GAAG,CAACiE,WAAW,CAAC,CAAG,EAAE,CACtB,CACAjE,GAAG,CAACiE,WAAW,CAAC,CAACC,IAAI,CAACtE,MAAM,CAAC,CAC7B,MAAO,CAAAI,GAAG,CACX,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAAmE,WAAW,CAAG,CACnBjH,QAAQ,CAAE,CAAAzK,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEqO,QAAQ,GAAI,eAAe,CACvDsD,YAAY,CAAE,CAAA3R,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4R,MAAM,GAAI,KAAK,CAC/CC,WAAW,CAAE,CAAA7R,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE8R,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAA/R,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgS,WAAW,GAAI,OAAO,CACrDC,WAAW,CAAE,OAAO,CACpBtH,eAAe,CAAE,CAAA3K,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkS,eAAe,GAAI,OAAO,CAC7DnB,QAAQ,CAAGvB,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC7DtO,YAAY,CAAG,GAAGA,YAAY,aAAa,CAC3C9B,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEwQ,KAAK,CAAG,GAAGxQ,gBAAgB,CAACwQ,KAAK,eAAe,CAAG,OAAO,CAChF1G,KAAK,CAAG0F,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC1DtO,YAAY,CAAG,GAAGA,YAAY,aAAa,CAC3C9B,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAEwQ,KAAK,CAAG,GAAGxQ,gBAAgB,CAACwQ,KAAK,eAAe,CAAG,OAC1E,CAAC,CACD,KAAM,CAAA2B,aAAa,CAAG,EAAA1T,gBAAA,CAAAoB,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAf,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkCmR,WAAW,UAAAlR,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDc,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAAhEA,sBAAA,CAAkEyT,aAAa,GAAI,MAAM,CAC/G,KAAM,CAAAC,kBAAkB,CAAIC,MAAW,EAAK,CAC3C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,CAC5F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAtN,MAAM,CAACuN,QAAQ,CAACC,IAAI,CAAGJ,SAAS,CACjC,CAAC,IAAM,CACN;AACApN,MAAM,CAACyN,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GACCF,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACI,WAAW,EAAI,UAAU,EAChCJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAC/B,CACD9F,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN0F,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACI,WAAW,EAAI,MAAM,EAC5BJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAC3B,CACDrH,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACNiH,MAAM,CAACC,MAAM,EAAI,SAAS,EAC1BD,MAAM,CAACI,WAAW,EAAI,SAAS,CAC9B,KAAAI,uBAAA,CAAAC,uBAAA,CACD;AACApS,cAAc,CAAC,CAAC,CAAC,CAEjBuD,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CAEtE;AACA,GAAI9D,cAAc,SAAdA,cAAc,YAAAyS,uBAAA,CAAdzS,cAAc,CAAEgI,SAAS,UAAAyK,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgCrK,WAAW,CAAE,CAChD;AACA,KAAM,CAAAsK,YAAY,CAAG3Q,iBAAiB,CAAChC,cAAc,CAACgI,SAAS,CAAC,CAAC,CAAC,CAACK,WAAW,CAAC,CAC/E,GAAIsK,YAAY,CAAE,CACjB,GAAI,CACHA,YAAY,CAAC/H,cAAc,CAAC,CAC3BjH,QAAQ,CAAE,QAAQ,CAClBkH,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACFjH,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC,CAClE,CAAE,MAAOF,KAAK,CAAE,CACfC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC,CAC/DiB,MAAM,CAACtB,QAAQ,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CAAC,IAAM,CACNE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACvEiB,MAAM,CAACtB,QAAQ,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CAAC,IAAM,CACN;AACAE,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC,CACnEiB,MAAM,CAACtB,QAAQ,CAAC,CAAEC,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAE,QAAS,CAAC,CAAC,CAChD,CACD,CACD,CACAoH,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACDtO,SAAS,CAAC,IAAM,KAAAmW,WAAA,CAAAC,mBAAA,CACf,GAAIlU,SAAS,SAATA,SAAS,YAAAiU,WAAA,CAATjU,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAAyT,WAAA,YAAAC,mBAAA,CAA5BD,WAAA,CAA8BvF,OAAO,UAAAwF,mBAAA,WAArCA,mBAAA,CAAuCC,aAAa,CAAE,CACzD;AACAjS,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAClC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,CAAEA,WAAW,CAAE0B,cAAc,CAAC,CAAC,CAE/D;AACApE,SAAS,CAAC,IAAM,CACf,GAAI2D,kBAAkB,CAAE,KAAA2S,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAA5K,eAAe,CAAGvH,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAuS,sBAAA,CAApBvS,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA4T,sBAAA,WAAvCA,sBAAA,CAAyCtK,QAAQ,CAC5GjI,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsJ,QAAQ,EAAAuK,sBAAA,CAC9CxS,oBAAoB,CAAC,CAAC,CAAC,UAAAwS,sBAAA,iBAAvBA,sBAAA,CAAyBvK,QAAQ,CACpC,KAAM,CAAA4K,WAAW,CAAGpS,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAiT,uBAAA,CAAdjT,cAAc,CAAEgI,SAAS,UAAAiL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9T,WAAW,CAAG,CAAC,CAAC,UAAA+T,uBAAA,iBAA5CA,uBAAA,CAA8C7F,OAAO,CACrDrN,cAAc,SAAdA,cAAc,kBAAAmT,uBAAA,CAAdnT,cAAc,CAAEgI,SAAS,UAAAmL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgC/F,OAAO,CAE1C;AACA;AACA,GAAI7E,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEsK,aAAa,CAAE,CACnC;AACAjS,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACT,kBAAkB,CAAEI,oBAAoB,CAAC,CAAC,CAE9C;AACA/D,SAAS,CAAC,IAAM,CACf,GAAI4D,kBAAkB,CAAE,KAAAiT,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAAnL,eAAe,CAAGvH,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAA8S,sBAAA,CAApB9S,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAmU,sBAAA,WAAvCA,sBAAA,CAAyC7K,QAAQ,CAC5GjI,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsJ,QAAQ,EAAA8K,sBAAA,CAC9C/S,oBAAoB,CAAC,CAAC,CAAC,UAAA+S,sBAAA,iBAAvBA,sBAAA,CAAyB9K,QAAQ,CACpC,KAAM,CAAA4K,WAAW,CAAGpS,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAwT,uBAAA,CAAdxT,cAAc,CAAEgI,SAAS,UAAAwL,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BrU,WAAW,CAAG,CAAC,CAAC,UAAAsU,uBAAA,iBAA5CA,uBAAA,CAA8CpG,OAAO,CACrDrN,cAAc,SAAdA,cAAc,kBAAA0T,uBAAA,CAAd1T,cAAc,CAAEgI,SAAS,UAAA0L,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCtG,OAAO,CAE1C;AACA,GAAI7E,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEsK,aAAa,CAAE,CACnC;AACAjS,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACR,kBAAkB,CAAEG,oBAAoB,CAAC,CAAC,CAE9C;AACA/D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAmX,iBAAiB,CAAIC,CAAa,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAG3R,QAAQ,CAACiH,cAAc,CAAC,cAAc,CAAC,CAE9D;AACA,GAAI0K,cAAc,EAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,CAAE,CAChE,OACD,CAEA;AACA;AACD,CAAC,CAED7R,QAAQ,CAAC6O,gBAAgB,CAAC,OAAO,CAAE4C,iBAAiB,CAAC,CAErD,MAAO,IAAM,CACZzR,QAAQ,CAAC8O,mBAAmB,CAAC,OAAO,CAAE2C,iBAAiB,CAAC,CACzD,CAAC,CACF,CAAC,CAAE,CAACpT,oBAAoB,CAAC,CAAC,CAC1B;AACA/D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAwX,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAIpS,UAAU,CAACwO,OAAO,CAAE,CACvB;AACAxO,UAAU,CAACwO,OAAO,CAAC7G,KAAK,CAACE,MAAM,CAAG,MAAM,CACxC,KAAM,CAAAwK,aAAa,CAAGrS,UAAU,CAACwO,OAAO,CAAClN,YAAY,CACrD,KAAM,CAAAgR,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGF,aAAa,CAAGC,eAAe,CAGpDrK,iBAAiB,CAACsK,YAAY,CAAC,CAE/B;AACA,GAAIrK,YAAY,CAACsG,OAAO,CAAE,CACzB;AACA,GAAItG,YAAY,CAACsG,OAAO,CAACgE,YAAY,CAAE,CACtCtK,YAAY,CAACsG,OAAO,CAACgE,YAAY,CAAC,CAAC,CACpC,CACA;AACApO,UAAU,CAAC,IAAM,CAChB,GAAI8D,YAAY,CAACsG,OAAO,EAAItG,YAAY,CAACsG,OAAO,CAACgE,YAAY,CAAE,CAC9DtK,YAAY,CAACsG,OAAO,CAACgE,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDJ,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAK,QAAQ,CAAG,CAChBrO,UAAU,CAACgO,iBAAiB,CAAE,EAAE,CAAC,CACjChO,UAAU,CAACgO,iBAAiB,CAAE,GAAG,CAAC,CAClChO,UAAU,CAACgO,iBAAiB,CAAE,GAAG,CAAC,CAClChO,UAAU,CAACgO,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAM,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI3S,UAAU,CAACwO,OAAO,EAAItL,MAAM,CAAC0P,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCxO,UAAU,CAACgO,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFM,cAAc,CAACG,OAAO,CAAC7S,UAAU,CAACwO,OAAO,CAAC,CAC3C,CAGA,GAAIxO,UAAU,CAACwO,OAAO,EAAItL,MAAM,CAAC4P,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7C1O,UAAU,CAACgO,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFO,gBAAgB,CAACE,OAAO,CAAC7S,UAAU,CAACwO,OAAO,CAAE,CAC5CuE,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAAC7O,YAAY,CAAC,CAC9B,GAAIoO,cAAc,CAAE,CACnBA,cAAc,CAACU,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIT,gBAAgB,CAAE,CACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAAC9V,WAAW,CAAC,CAAC,CACjB;AACA;AAEA,QAAS,CAAA+V,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CACA,KAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAAhL,QAAgB,CAAArH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,eAAe,CAC5D,OAAQqH,QAAQ,EACf,IAAK,aAAa,CACjB,MAAO,CAAE1G,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,cAAc,CAClB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,aAAa,CACjB,MAAO,CAAEA,GAAG,CAAE5E,QAAQ,GAAK,EAAE,CAAG,gBAAgB,CAAG,gBAAiB,CAAC,CACtE,IAAK,cAAc,CAClB,MAAO,CAAE4E,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,UAAU,CACd,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,WAAW,CACf,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,YAAY,CAChB,MAAO,CAAEA,GAAG,CAAE,eAAgB,CAAC,CAChC,QACC,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CAClC,CACD,CAAC,CAEA;AACD,KAAM,CAAA2R,kBAAkB,CAAGA,CAACC,QAAgB,CAAE9M,eAAoB,CAAE6K,WAAgB,GAAK,CACxF,GAAIpS,oBAAoB,GAAK,SAAS,CAAE,CACvC;AACA,OAAQqU,QAAQ,EACf,IAAK,gBAAgB,CACpB,MAAO,CAAAjC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEkC,cAAc,IAAKtS,SAAS,CAAGoQ,WAAW,CAACkC,cAAc,CAAG/M,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+M,cAAc,CAChH,IAAK,eAAe,CACnB;AACA,MAAO,CAAAlC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEmC,4BAA4B,IAAKvS,SAAS,CAAGoQ,WAAW,CAACmC,4BAA4B,CAAGhN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgN,4BAA4B,CAC1J,IAAK,UAAU,CACd,MAAO,CAAAnC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEoC,QAAQ,IAAKxS,SAAS,CAAGoQ,WAAW,CAACoC,QAAQ,CAAGjN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiN,QAAQ,CAC9F,IAAK,eAAe,CACnB,MAAO,CAAApC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEP,aAAa,IAAK7P,SAAS,CAAGoQ,WAAW,CAACP,aAAa,CAAGtK,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsK,aAAa,CAC7G,QACC,MAAO,CAAAtK,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG8M,QAAQ,CAAC,CACpC,CACD,CAAC,IAAM,CACN;AACA,GAAIA,QAAQ,GAAK,eAAe,CAAE,CACjC,MAAO,CAAA9M,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEgN,4BAA4B,CACrD,CACA,MAAO,CAAAhN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAG8M,QAAQ,CAAC,CACnC,CACD,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAGA,CAAC3T,OAAY,CAAEyG,eAAoB,CAAE6K,WAAgB,CAAEpO,IAAS,CAAEvB,GAAQ,GAAK,CACzG3B,OAAO,CAACyH,KAAK,CAACY,QAAQ,CAAG,UAAU,CACnCrI,OAAO,CAACyH,KAAK,CAACvE,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChClD,OAAO,CAACyH,KAAK,CAAC9F,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC9B3B,OAAO,CAACyH,KAAK,CAACC,KAAK,CAAG,GAAGjB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,IAAI,IAAI,CAAE;AACpD7G,OAAO,CAACyH,KAAK,CAACE,MAAM,CAAG,GAAGlB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,IAAI,IAAI,CACnD7G,OAAO,CAACyH,KAAK,CAACc,eAAe,CAAG9B,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmN,KAAK,CACtD5T,OAAO,CAACyH,KAAK,CAAC8H,YAAY,CAAG,KAAK,CAClCvP,OAAO,CAACyH,KAAK,CAACoM,MAAM,CAAG,iBAAiB,CAAE;AAC1C7T,OAAO,CAACyH,KAAK,CAACqM,UAAU,CAAG,MAAM,CACjC9T,OAAO,CAACyH,KAAK,CAACsM,aAAa,CAAG,MAAM,CAAE;AACtC/T,OAAO,CAACgU,SAAS,CAAG,EAAE,CAEtB,GAAI,CAAAvN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwN,IAAI,IAAK,MAAM,EAAI,CAAAxN,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwN,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAG9T,QAAQ,CAAC+T,aAAa,CAAC,MAAM,CAAC,CAC/CD,QAAQ,CAACE,SAAS,CAAG3N,eAAe,CAACwN,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAACzM,KAAK,CAAC+E,KAAK,CAAG,OAAO,CAC9B0H,QAAQ,CAACzM,KAAK,CAAC4M,QAAQ,CAAG,MAAM,CAChCH,QAAQ,CAACzM,KAAK,CAAC0E,UAAU,CAAG,MAAM,CAClC+H,QAAQ,CAACzM,KAAK,CAAC6E,SAAS,CAAG7F,eAAe,CAACwN,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAACzM,KAAK,CAACkC,OAAO,CAAG,MAAM,CAC/BuK,QAAQ,CAACzM,KAAK,CAAC6M,UAAU,CAAG,QAAQ,CACpCJ,QAAQ,CAACzM,KAAK,CAAC8M,cAAc,CAAG,QAAQ,CACxCL,QAAQ,CAACzM,KAAK,CAACC,KAAK,CAAG,MAAM,CAC7BwM,QAAQ,CAACzM,KAAK,CAACE,MAAM,CAAG,MAAM,CAC9B3H,OAAO,CAACwU,WAAW,CAACN,QAAQ,CAAC,CAC9B,CAEA;AACA;AACA,KAAM,CAAAO,qBAAqB,CAAGnB,kBAAkB,CAAC,gBAAgB,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CAChG,KAAM,CAAAoD,WAAW,CAAGxV,oBAAoB,GAAK,SAAS,CAClDuV,qBAAqB,GAAK,KAAK,EAAI,CAACzU,OAAO,CAAC2U,aAAa,CACzDlO,eAAe,EAAIzH,gBAAgB,EAAI,CAACgB,OAAO,CAAC2U,aAAc,CAElE,GAAID,WAAW,CAAE,CACP1U,OAAO,CAAC4U,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CACxC7U,OAAO,CAAC4U,SAAS,CAAChL,MAAM,CAAC,yBAAyB,CAAC,CACvD,CAAC,IAAM,CACH5J,OAAO,CAAC4U,SAAS,CAAChL,MAAM,CAAC,iBAAiB,CAAC,CAC3C5J,OAAO,CAAC4U,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACpD,CAEN;AACA7U,OAAO,CAACyH,KAAK,CAACkC,OAAO,CAAG,MAAM,CAC9B3J,OAAO,CAACyH,KAAK,CAACsM,aAAa,CAAG,MAAM,CAEpC;AACA;AACA;AACA,KAAM,CAAAe,aAAa,CAAGxB,kBAAkB,CAAC,eAAe,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CACvF,GAAIwD,aAAa,CAAE,CAClBhW,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACA;AAAA,CAGD;AACA;AACA,GAAI,CAACkB,OAAO,CAAC+U,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,KAAM,CAAAC,UAAU,CAAGhV,OAAO,CAACiV,SAAS,CAAC,IAAI,CAAgB,CACzD;AACA,GAAIjV,OAAO,CAAC2U,aAAa,GAAKzT,SAAS,CAAE,CAC9B8T,UAAU,CAASL,aAAa,CAAG3U,OAAO,CAAC2U,aAAa,CAC7D,CACN,GAAI3U,OAAO,CAACkV,UAAU,CAAE,CACvBlV,OAAO,CAACkV,UAAU,CAACC,YAAY,CAACH,UAAU,CAAEhV,OAAO,CAAC,CACpDA,OAAO,CAAGgV,UAAU,CACrB,CACD,CAEA;AACAhV,OAAO,CAACyH,KAAK,CAACsM,aAAa,CAAG,MAAM,CAEpC;AACA,KAAM,CAAAqB,QAAQ,CAAG9B,kBAAkB,CAAC,UAAU,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CAC7E,KAAM,CAAA+D,WAAW,CAAIvD,CAAQ,EAAK,CACjCA,CAAC,CAACwD,eAAe,CAAC,CAAC,CACnBxT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIqT,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACAtW,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAAoX,oBAAoB,CAAGjC,kBAAkB,CAAC,eAAe,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CAC9F,GAAIiE,oBAAoB,CAAE,CACzBvV,OAAO,CAAC4U,SAAS,CAAChL,MAAM,CAAC,iBAAiB,CAAC,CAC3C5J,OAAO,CAAC4U,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChD7U,OAAO,CAAC2U,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED,KAAM,CAAAa,cAAc,CAAI1D,CAAQ,EAAK,CACpCA,CAAC,CAACwD,eAAe,CAAC,CAAC,CAEnB;AACA;AACA,KAAM,CAAAR,aAAa,CAAGxB,kBAAkB,CAAC,eAAe,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CACvF,GAAI8D,QAAQ,GAAK,kBAAkB,EAAI,CAACN,aAAa,CAAE,CACtD;AAAA,CAEF,CAAC,CAED,KAAM,CAAAW,WAAW,CAAI3D,CAAQ,EAAK,CACjCA,CAAC,CAACwD,eAAe,CAAC,CAAC,CACnBxT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIqT,QAAQ,GAAK,kBAAkB,EAAI,CAACA,QAAQ,CAAE,CACjD;AACAtW,cAAc,CAAC,CAACC,WAAW,CAAC,CAE5B;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACJ,KAAM,CAAAmX,oBAAoB,CAAGjC,kBAAkB,CAAC,eAAe,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CAC1F,GAAIiE,oBAAoB,CAAE,CACzBvV,OAAO,CAAC4U,SAAS,CAAChL,MAAM,CAAC,iBAAiB,CAAC,CAC3C5J,OAAO,CAAC4U,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChD7U,OAAO,CAAC2U,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED;AACA,GAAI,CAAC3U,OAAO,CAAC+U,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,GAAIK,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACApV,OAAO,CAACiP,gBAAgB,CAAC,WAAW,CAAEoG,WAAW,CAAC,CAClDrV,OAAO,CAACiP,gBAAgB,CAAC,UAAU,CAAEuG,cAAc,CAAC,CAEpD;AACAxV,OAAO,CAACiP,gBAAgB,CAAC,OAAO,CAAEwG,WAAW,CAAC,CAC/C,CAAC,IAAM,CACN;AACAzV,OAAO,CAACiP,gBAAgB,CAAC,OAAO,CAAEwG,WAAW,CAAC,CAC/C,CAEA;AACAzV,OAAO,CAAC0V,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CACxD,CACD,CAAC,CACDhb,SAAS,CAAC,IAAM,CACf,GAAI,CAAAmG,OAAO,CACX,GAAI,CAAA8U,KAAK,CAET,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,MAAA,CAAAC,OAAA,CACH;AACAL,KAAK,CAAG,CAAA1X,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAEgI,SAAS,GAAI,EAAE,CAEvC;AACA,KAAM,CAAAI,WAAW,CAAGnH,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAA4X,uBAAA,CAAd5X,cAAc,CAAEgI,SAAS,UAAA4P,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4BzY,WAAW,CAAG,CAAC,CAAC,UAAA0Y,uBAAA,WAA5CA,uBAAA,CAA8CxP,WAAW,CAC/GrI,cAAc,CAACgI,SAAS,CAAC7I,WAAW,CAAG,CAAC,CAAC,CAASkJ,WAAW,CAC9D,EAAAyP,MAAA,CAAAJ,KAAK,UAAAI,MAAA,kBAAAC,OAAA,CAALD,MAAA,CAAQ,CAAC,CAAC,UAAAC,OAAA,iBAAVA,OAAA,CAAY1P,WAAW,GAAI,EAAE,CAEhCxE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAE,CAC3DsE,WAAW,CACXnH,oBAAoB,CACpB9B,WAAW,CACX6Y,aAAa,CAAE/W,oBAAoB,GAAK,SACzC,CAAC,CAAC,CAEF2B,OAAO,CAAGZ,iBAAiB,CAACoG,WAAW,EAAI,EAAE,CAAC,CAC9C9G,gBAAgB,CAACsB,OAAO,CAAC,CAEzB;AACA,GAAI,CAACA,OAAO,EAAIwF,WAAW,CAAE,CAC5BvE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpEqB,cAAc,CACbiD,WAAW,CACX,EAAE,CAAE;AACHE,YAAyB,EAAK,CAC9BzE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC,CAC1DlB,OAAO,CAAG0F,YAAY,CACtBhH,gBAAgB,CAACgH,YAAY,CAAC,CAE9B;AACArC,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAY,IAAI,CAAGyB,YAAY,CAACxB,qBAAqB,CAAC,CAAC,CACjDjD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAE+C,IAAI,CAAC,CACpD,CAAC,CAAE,EAAE,CAAC,CACP,CAAC,CACD,EAAE,CAAE;AACJ,GAAG,CAAE;AACL,sBAAsB,CACtB,IAAM,CACLhD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC,CAC/D,CACD,CAAC,CACF,CAEA,GAAIlB,OAAO,CAAE,CACZ;AAAA,CAGD;AACA,KAAM,CAAAqV,iBAAiB,CAAG1X,gBAAgB,GAAK,SAAS,EACvDU,oBAAoB,GAAK,SAAS,EAClCrC,KAAK,GAAK,SAAS,EAClB2B,gBAAgB,GAAK,MAAM,EAAIU,oBAAoB,GAAK,SAAU,CAEpE,GAAIgX,iBAAiB,CAAE,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAItB;AACA,GAAI,CAAA/P,eAAe,CACnB,GAAI,CAAA6K,WAAW,CAEf,GAAIpS,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAA0X,sBAAA,CAApB1X,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA+Y,sBAAA,WAAvCA,sBAAA,CAAyCzP,QAAQ,CAAE,KAAA+P,uBAAA,CAAAC,uBAAA,CAC5F;AACAjQ,eAAe,CAAGhI,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsJ,QAAQ,CAChE4K,WAAW,CAAGrT,cAAc,SAAdA,cAAc,kBAAAwY,uBAAA,CAAdxY,cAAc,CAAEgI,SAAS,UAAAwQ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BrZ,WAAW,CAAG,CAAC,CAAC,UAAAsZ,uBAAA,iBAA5CA,uBAAA,CAA8CpL,OAAO,CACpE,CAAC,IAAM,IAAI7M,oBAAoB,SAApBA,oBAAoB,YAAA2X,sBAAA,CAApB3X,oBAAoB,CAAG,CAAC,CAAC,UAAA2X,sBAAA,WAAzBA,sBAAA,CAA2B1P,QAAQ,CAAE,KAAAiQ,uBAAA,CAAAC,uBAAA,CAC/C;AACAnQ,eAAe,CAAGhI,oBAAoB,CAAC,CAAC,CAAC,CAACiI,QAAQ,CAClD4K,WAAW,CAAGrT,cAAc,SAAdA,cAAc,kBAAA0Y,uBAAA,CAAd1Y,cAAc,CAAEgI,SAAS,UAAA0Q,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCtL,OAAO,CACtD,CAAC,IAAM,KAAAuL,uBAAA,CAAAC,uBAAA,CACN;AACArQ,eAAe,CAAG,CACjBE,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdqN,IAAI,CAAE,UAAU,CAChBL,KAAK,CAAE,QAAQ,CACf/M,IAAI,CAAE,IAAI,CACV2M,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5B3C,aAAa,CAAE,KAChB,CAAC,CACDO,WAAW,CAAG,CAAArT,cAAc,SAAdA,cAAc,kBAAA4Y,uBAAA,CAAd5Y,cAAc,CAAEgI,SAAS,UAAA4Q,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BzZ,WAAW,CAAG,CAAC,CAAC,UAAA0Z,uBAAA,iBAA5CA,uBAAA,CAA8CxL,OAAO,GAAI,CAAC,CAAC,CAC1E,CACA,KAAM,CAAAxE,OAAO,CAAGC,UAAU,CAAC,EAAAsP,gBAAA,CAAA5P,eAAe,UAAA4P,gBAAA,iBAAfA,gBAAA,CAAiB1P,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,OAAO,CAAGD,UAAU,CAAC,EAAAuP,iBAAA,CAAA7P,eAAe,UAAA6P,iBAAA,iBAAfA,iBAAA,CAAiB1P,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAK,kBAAkB,CAAGF,UAAU,CAAC,EAAAwP,iBAAA,CAAA9P,eAAe,UAAA8P,iBAAA,iBAAfA,iBAAA,CAAiB1P,IAAI,GAAI,IAAI,CAAC,CAEpE;AACAhH,cAAc,CAACoH,kBAAkB,CAAC,CAElC,GAAI,CAAA/D,IAAI,CAAEvB,GAAG,CACb,GAAId,OAAO,CAAE,CACZ,KAAM,CAAAiE,IAAI,CAAGjE,OAAO,CAACkE,qBAAqB,CAAC,CAAC,CAE5C;AACA,GAAID,IAAI,CAAC4C,KAAK,CAAG,CAAC,EAAI5C,IAAI,CAAC6C,MAAM,CAAG,CAAC,CAAE,CACtCzE,IAAI,CAAG4B,IAAI,CAACwC,CAAC,CAAGR,OAAO,CACvBnF,GAAG,CAAGmD,IAAI,CAACyC,CAAC,EAAIP,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAGzF,IAAI,CAACiG,GAAG,CAACR,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAE,QAAQ,CAAGC,sBAAsB,CAACrC,IAAI,CAAEmC,kBAAkB,CAAEH,OAAO,CAAEE,OAAO,CAAC,CACnFvH,gBAAgB,CAACyH,QAAQ,CAAC,CAE1BpF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAE,CACjD8D,WAAW,CAAEf,IAAI,CACjBiS,eAAe,CAAE,CAAE7T,IAAI,CAAEvB,GAAI,CAAC,CAC9BnC,aAAa,CAAE0H,QAChB,CAAC,CAAC,CACH,CAAC,IAAM,CACNpF,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAE+C,IAAI,CAAC,CAC3E;AACA5B,IAAI,CAAG,GAAG,CACVvB,GAAG,CAAG,GAAG,CACV,CACD,CAAC,IAAM,CACNG,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC,CACzD;AACAmB,IAAI,CAAG,GAAG,CACVvB,GAAG,CAAG,GAAG,CACV,CAEA;AACA,KAAM,CAAAyF,eAAe,CAAGhH,QAAQ,CAACiH,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBpH,OAAO,CAAGoH,eAAe,CACzB;AACD,CAAC,IAAM,CACN;AACApH,OAAO,CAAGI,QAAQ,CAAC+T,aAAa,CAAC,KAAK,CAAC,CACvCnU,OAAO,CAACyE,EAAE,CAAG,cAAc,CAAE;AAC7BzE,OAAO,CAAC2U,aAAa,CAAG,KAAK,CAAE;AAC/BvU,QAAQ,CAAC2F,IAAI,CAACyO,WAAW,CAACxU,OAAO,CAAC,CACnC,CAEAA,OAAO,CAACyH,KAAK,CAACuP,MAAM,CAAG,SAAS,CAChChX,OAAO,CAACyH,KAAK,CAACsM,aAAa,CAAG,MAAM,CAAE;AAEtC;AACA/T,OAAO,CAACyH,KAAK,CAACoM,MAAM,CAAG,MAAM,CAE7B;AACA,IAAA2C,iBAAA,CAAI/P,eAAe,UAAA+P,iBAAA,WAAfA,iBAAA,CAAiBzF,aAAa,CAAE,CACnCjS,cAAc,CAAC,IAAI,CAAC,CACrB,CAEA;AACA6U,kBAAkB,CAAC3T,OAAO,CAAEyG,eAAe,CAAE6K,WAAW,CAAEpO,IAAI,CAAEvB,GAAG,CAAC,CAEpE;AACA,KAAM,CAAAmT,aAAa,CAAGxB,kBAAkB,CAAC,eAAe,CAAE7M,eAAe,CAAE6K,WAAW,CAAC,CACvF,GAAIwD,aAAa,CAAE,CAClBhW,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AAAA,CAGD;AACD,CACD,CAAE,MAAO+C,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD,CACD,CAAC,CAED+T,iBAAiB,CAAC,CAAC,CAEnB,MAAO,IAAM,CACZ,KAAM,CAAAxO,eAAe,CAAGhH,QAAQ,CAACiH,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAAC6P,OAAO,CAAG,IAAI,CAC9B7P,eAAe,CAAC8P,WAAW,CAAG,IAAI,CAClC9P,eAAe,CAAC+P,UAAU,CAAG,IAAI,CAClC,CACD,CAAC,CACF,CAAC,CAAE,CACFlZ,cAAc,CACdQ,oBAAoB,CACpBJ,kBAAkB,CAClBC,kBAAkB,CAClBY,oBAAoB,CACpB9B,WACA;AAAA,CACA,CAAC,CACF,KAAM,CAAAga,cAAc,CAAG,CAAAnZ,cAAc,SAAdA,cAAc,kBAAAzB,uBAAA,CAAdyB,cAAc,CAAEgI,SAAS,UAAAzJ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgC4a,OAAO,UAAA3a,uBAAA,iBAAvCA,uBAAA,CAAyC4a,cAAc,GAAI,KAAK,CAEvF,QAAS,CAAAC,mBAAmBA,CAACpY,cAAmB,CAAE,KAAAqY,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACjD,GAAIvY,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAlB,cAAc,SAAdA,cAAc,kBAAAuZ,uBAAA,CAAdvZ,cAAc,CAAEgI,SAAS,UAAAuR,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCJ,OAAO,UAAAK,uBAAA,iBAAvCA,uBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAACpY,cAAc,CAAC,CAC5D,KAAM,CAAA0Y,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACT,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAIQ,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACCnc,IAAA,CAACP,aAAa,EACb4c,OAAO,CAAC,MAAM,CACdnC,KAAK,CAAEtY,UAAW,CAClBgL,QAAQ,CAAC,QAAQ,CACjB0P,UAAU,CAAE3a,WAAW,CAAG,CAAE,CAC5B4a,EAAE,CAAE,CACHzP,eAAe,CAAE,aAAa,CAC9BF,QAAQ,CAAE,oBAAoB,CAC9B,+BAA+B,CAAE,CAChCE,eAAe,CAAEnJ,aAAe;AACjC,CACD,CAAE,CACF6Y,UAAU,cAAExc,IAAA,CAACV,MAAM,EAAC0M,KAAK,CAAE,CAAEyQ,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAE1c,IAAA,CAACV,MAAM,EAAC0M,KAAK,CAAE,CAAEyQ,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACA,GAAIN,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCnc,IAAA,CAACX,GAAG,EAACkd,EAAE,CAAE,CAAErO,OAAO,CAAE,MAAM,CAAE2K,UAAU,CAAE,QAAQ,CAAE8D,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAEC,OAAO,CAAE,KAAM,CAAE,CAAAC,QAAA,CAGrGC,KAAK,CAACC,IAAI,CAAC,CAAExX,MAAM,CAAE5D,UAAW,CAAC,CAAC,CAACuN,GAAG,CAAC,CAAC8N,CAAC,CAAEC,KAAK,gBAChDld,IAAA,QAECgM,KAAK,CAAE,CACNC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbY,eAAe,CAAEoQ,KAAK,GAAKvb,WAAW,CAAG,CAAC,CAAGgC,aAAa,CAAG,SAAS,CAAE;AACxEmQ,YAAY,CAAE,OACf,CAAE,EANGoJ,KAOL,CACD,CAAC,CACE,CAAC,CAER,CACA,GAAIf,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACCnc,IAAA,CAACX,GAAG,EAACkd,EAAE,CAAE,CAAErO,OAAO,CAAE,MAAM,CAAE2K,UAAU,CAAE,QAAQ,CAAE8D,YAAY,CAAE,YAAa,CAAE,CAAAG,QAAA,cAC9E5c,KAAA,CAACP,UAAU,EAAC4c,EAAE,CAAE,CAAEM,OAAO,CAAE,KAAK,CAAE9L,KAAK,CAAEpN,aAAc,CAAE,CAAAmZ,QAAA,EAAC,OACpD,CAACnb,WAAW,CAAC,MAAI,CAACC,UAAU,EACtB,CAAC,CACT,CAAC,CAER,CAEA,GAAIua,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACCnc,IAAA,CAACX,GAAG,EAAAyd,QAAA,cACH9c,IAAA,CAACL,UAAU,EAAC0c,OAAO,CAAC,OAAO,CAAAS,QAAA,cAC1B9c,IAAA,CAACR,cAAc,EACd6c,OAAO,CAAC,aAAa,CACrBc,KAAK,CAAErb,QAAS,CAChBya,EAAE,CAAE,CACHrQ,MAAM,CAAE,KAAK,CACX4H,YAAY,CAAE,MAAM,CACpBsJ,MAAM,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC3BtQ,eAAe,CAAEnJ,aAAe;AACjC,CACD,CAAE,CACF,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCzD,KAAA,CAAAE,SAAA,EAAA0c,QAAA,EACEjZ,aAAa,eACb7D,IAAA,QAAA8c,QAAA,CAcExZ,WAAW,eACXpD,KAAA,CAACR,OAAO,EACPsV,IAAI,CAAEqI,OAAO,CAACtZ,aAAa,CAAC,EAAIsZ,OAAO,CAACnc,QAAQ,CAAE,CAClDA,QAAQ,CAAEA,QAAS,CACnBK,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACF6O,YAAY,CAAEA,YAAa,CAC3BG,eAAe,CAAEA,eAAgB,CACjC+M,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CACbxZ,aAAa,CACV,CACAmC,GAAG,CAAEnC,aAAa,CAACmC,GAAG,CAAE,EAAE,EAAGoF,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAAG,CAACkI,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAAG0C,IAAI,CAACiG,GAAG,CAACT,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAAC,CAAC,CAChJqE,IAAI,CAAE1D,aAAa,CAAC0D,IAAI,CAAE,EAAE,CAAE6D,UAAU,CAACnI,YAAY,EAAI,GAAG,CAC5D,CAAC,CACDsC,SACH,CACD8W,EAAE,CAAE,CACH;AACA;AACA;AACA,gBAAgB,CAAErb,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CkX,MAAM,CAAE,IAAI,CACZ;AACA,GAAGvE,WAAW,CACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG+D,iBAAiB,CAAC,CAAAzV,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEqO,QAAQ,GAAI,eAAe,CAAC,CACnEtK,GAAG,CAAE,GAAG,CAAC,CAAAnC,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEmC,GAAG,GAAI,CAAC,GAC5B9C,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC3CkI,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAClC,CAACkI,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAChC0C,IAAI,CAACiG,GAAG,CAACT,UAAU,CAAClI,YAAY,EAAI,GAAG,CAAC,CAAC,CAC1C,CAAC,CAAC,eAAe,CACrBqE,IAAI,CAAE,GAAG,CAAC,CAAA1D,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE0D,IAAI,GAAI,CAAC,GAAKtE,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC9EmI,UAAU,CAACnI,YAAY,CAAC,EAAI,CAAC,CAC9B,CAAC,CAAE,eAAe,CACrBqa,QAAQ,CAAE,QACX,CACD,CAAE,CACFC,iBAAiB,CAAE,IAAK,CAAAX,QAAA,eAExB9c,IAAA,QAAKgM,KAAK,CAAE,CAAE2Q,YAAY,CAAE,KAAK,CAAEzO,OAAO,CAAE,MAAO,CAAE,CAAA4O,QAAA,CACnD,CAAA5a,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwb,aAAa,gBAC9B1d,IAAA,CAACT,UAAU,EACVoe,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFpB,EAAE,CAAE,CACH3P,QAAQ,CAAE,OAAO,CACjBgR,SAAS,CAAE,iCAAiC,CAC5CnW,IAAI,CAAE,MAAM,CACZ0F,KAAK,CAAE,MAAM,CACbiQ,MAAM,CAAE,OAAO,CACfS,UAAU,CAAE,iBAAiB,CAC7BC,MAAM,CAAE,gBAAgB,CACxB1F,MAAM,CAAE,QAAQ,CAChBtE,YAAY,CAAE,MAAM,CACpB+I,OAAO,CAAE,gBACV,CAAE,CAAAC,QAAA,cAEF9c,IAAA,CAACJ,SAAS,EAAC2c,EAAE,CAAE,CAAEwB,IAAI,CAAE,CAAC,CAAEhN,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClC,CACZ,CACG,CAAC,cACN/Q,IAAA,CAACF,gBAAgB,EAEpBke,GAAG,CAAEzR,YAAa,CAClBP,KAAK,CAAE,CAAEiS,SAAS,CAAEtM,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAQ,CAAE,CAC3ElL,OAAO,CAAE,CACR6W,eAAe,CAAE,CAAC7R,cAAc,CAChC8R,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAA1B,QAAA,cAEC9c,IAAA,QAAKgM,KAAK,CAAE,CACXiS,SAAS,CAAEtM,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAO,CAC/DiL,QAAQ,CAAE7L,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,SAAS,CAAG,aAAa,CACvEtG,KAAK,CAAE0F,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG9M,SAAS,CAC7D2X,MAAM,CAAEzL,cAAc,CAAC,CAAC,CAAG,GAAG,CAAGlM,SAClC,CAAE,CAAAqX,QAAA,cACD5c,KAAA,CAACb,GAAG,EAAC2M,KAAK,CAAE,CACX6Q,OAAO,CAAElL,cAAc,CAAC,CAAC,CAAG,GAAG,CAC7BY,WAAW,CAAC,CAAC,CAAG,GAAG,CAAI,CAAApQ,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEsc,OAAO,GAAI,MAAO,CAC7DvS,MAAM,CAAEyF,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG2C,aAAa,CACjDrI,KAAK,CAAE0F,cAAc,CAAC,CAAC,EAAIY,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG9M,SAAS,CAC7D2X,MAAM,CAAEzL,cAAc,CAAC,CAAC,CAAG,GAAG,CAAGlM,SAClC,CAAE,CAAAqX,QAAA,eACD5c,KAAA,CAACb,GAAG,EACH2e,GAAG,CAAE3Z,UAAW,CAChB6J,OAAO,CAAC,MAAM,CACdwQ,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACf7F,cAAc,CAAC,QAAQ,CACvByD,EAAE,CAAE,CACHtQ,KAAK,CAAEsG,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACtCsK,OAAO,CAAEtK,WAAW,CAAC,CAAC,CAAG,GAAG,CAAG9M,SAChC,CAAE,CAAAqX,QAAA,EAED9a,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmN,GAAG,CAAEyP,SAAc,EACpCA,SAAS,CAAC7M,WAAW,CAAC5C,GAAG,CAAC,CAAC0P,SAAc,CAAEC,QAAgB,gBAC1D9e,IAAA,CAACX,GAAG,EAEH0f,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAAC5M,GAAI,CACnBgN,GAAG,CAAEJ,SAAS,CAACK,OAAO,EAAI,OAAQ,CAClC3C,EAAE,CAAE,CACH0B,SAAS,CAAEW,SAAS,CAACO,cAAc,EAAIN,SAAS,CAACM,cAAc,EAAI,OAAO,CAC1ElO,SAAS,CAAE2N,SAAS,CAAC1N,SAAS,EAAI,QAAQ,CAC1CkO,SAAS,CAAEP,SAAS,CAACQ,GAAG,EAAI,SAAS,CACrC;AACAnT,MAAM,CAAE,GAAG2S,SAAS,CAACtK,aAAa,EAAI,GAAG,IAAI,CAC7CsJ,UAAU,CAAEgB,SAAS,CAACxK,eAAe,EAAI,SAAS,CAClD+I,MAAM,CAAE,QACT,CAAE,CACFO,OAAO,CAAEA,CAAA,GAAM,CACd,GAAIiB,SAAS,CAACU,SAAS,CAAE,CACxB,KAAM,CAAA3K,SAAS,CAAGiK,SAAS,CAACU,SAAS,CACrC/X,MAAM,CAACyN,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACF3I,KAAK,CAAE,CAAEuP,MAAM,CAAEqD,SAAS,CAACU,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAnB1D,GAAGV,SAAS,CAACpP,EAAE,IAAIsP,QAAQ,EAoBhC,CACD,CACF,CAAC,CAEA/c,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEoN,GAAG,CACxB,CAACoQ,SAAc,CAAErC,KAAU,QAAAsC,qBAAA,CAAAC,sBAAA,OAC1B,CAAAF,SAAS,CAACnN,IAAI,eACbpS,IAAA,CAACL,UAAU,EACVoJ,SAAS,CAAC,eAAe,CACG;AAC5BwT,EAAE,CAAE,CACHtL,SAAS,CAAE,EAAAuO,qBAAA,CAAAD,SAAS,CAAC5O,cAAc,UAAA6O,qBAAA,iBAAxBA,qBAAA,CAA0BE,UAAU,GAAIjP,SAAS,CAACQ,SAAS,CACtEF,KAAK,CAAE,EAAA0O,sBAAA,CAAAF,SAAS,CAAC5O,cAAc,UAAA8O,sBAAA,iBAAxBA,sBAAA,CAA0BzO,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7D4O,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvB/C,OAAO,CAAE,OACV,CAAE,CACFgD,uBAAuB,CAAE1O,iBAAiB,CAACoO,SAAS,CAACnN,IAAI,CAAG;AAAA,EARvDmN,SAAS,CAAC/P,EAAE,EAAI0N,KASrB,CACD,EACH,CAAC,EACG,CAAC,CAEL4C,MAAM,CAACC,IAAI,CAACrM,cAAc,CAAC,CAACvE,GAAG,CAAEwE,WAAW,OAAAqM,qBAAA,CAAAC,sBAAA,oBAC5CjgB,IAAA,CAACX,GAAG,EACH2e,GAAG,CAAE1Z,kBAAmB,CAExBiY,EAAE,CAAE,CACHrO,OAAO,CAAE,MAAM,CACf4K,cAAc,CAAEpB,YAAY,EAAAsI,qBAAA,CAACtM,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAqM,qBAAA,iBAA9BA,qBAAA,CAAgC9O,SAAS,CAAC,CACvEyN,QAAQ,CAAE,MAAM,CAChBvB,MAAM,CAAEzL,cAAc,CAAC,CAAC,CAAG,CAAC,CAAG,OAAO,CACtC7E,eAAe,EAAAmT,sBAAA,CAAEvM,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAsM,sBAAA,iBAA9BA,sBAAA,CAAgC5L,eAAe,CAChEwI,OAAO,CAAElL,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,OAAO,CAC3C1F,KAAK,CAAE0F,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACzCmC,YAAY,CAAEnC,cAAc,CAAC,CAAC,CAAG,MAAM,CAAGlM,SAC3C,CAAE,CAAAqX,QAAA,CAEDpJ,cAAc,CAACC,WAAW,CAAC,CAACxE,GAAG,CAAC,CAACG,MAAW,CAAE4N,KAAa,QAAAgD,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3DxgB,IAAA,CAACV,MAAM,EAENqe,OAAO,CAAEA,CAAA,GAAMnJ,kBAAkB,CAAClF,MAAM,CAACmR,YAAY,CAAE,CACvDpE,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHmE,WAAW,CAAE/O,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,MAAM,CAC9CyL,MAAM,CAAEzL,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,eAAe,CAClD7E,eAAe,CAAE,EAAAoT,qBAAA,CAAA5Q,MAAM,CAACqR,gBAAgB,UAAAT,qBAAA,iBAAvBA,qBAAA,CAAyBU,qBAAqB,GAAI,SAAS,CAC5E7P,KAAK,CAAE,EAAAoP,sBAAA,CAAA7Q,MAAM,CAACqR,gBAAgB,UAAAR,sBAAA,iBAAvBA,sBAAA,CAAyBU,eAAe,GAAI,MAAM,CACzD/C,MAAM,CAAE,EAAAsC,sBAAA,CAAA9Q,MAAM,CAACqR,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBU,iBAAiB,GAAI,aAAa,CACnElI,QAAQ,CAAE,EAAAyH,sBAAA,CAAA/Q,MAAM,CAACqR,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBU,QAAQ,GAAI,MAAM,CACrD9U,KAAK,CAAE,EAAAqU,sBAAA,CAAAhR,MAAM,CAACqR,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyB3N,KAAK,GAAI,MAAM,CAC/CkK,OAAO,CAAElL,cAAc,CAAC,CAAC,CAAG,kCAAkC,CAAG,SAAS,CAC1EqP,UAAU,CAAErP,cAAc,CAAC,CAAC,CAAG,0BAA0B,CAAG,QAAQ,CACpEsP,aAAa,CAAE,MAAM,CACrBnN,YAAY,CAAE,EAAAyM,sBAAA,CAAAjR,MAAM,CAACqR,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBW,YAAY,GAAI,KAAK,CAC5DjO,QAAQ,CAAEtB,cAAc,CAAC,CAAC,CAAG,aAAa,CAAGlM,SAAS,CACtDmY,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACV9Q,eAAe,CAAE,EAAA0T,sBAAA,CAAAlR,MAAM,CAACqR,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBI,qBAAqB,GAAI,SAAS,CAAE;AAC9EO,OAAO,CAAE,GAAG,CAAE;AACdvD,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAAd,QAAA,CAEDxN,MAAM,CAAC8R,UAAU,EAxBblE,KAyBE,CAAC,EACT,CAAC,EAxCGvJ,WAyCD,CAAC,EACN,CAAC,EACE,CAAC,CAGD,CAAC,EApIL,aAAatH,cAAc,EAqIV,CAAC,CAElBsP,cAAc,EAAI/Z,UAAU,CAAC,CAAC,EAAImB,gBAAgB,GAAK,MAAM,eAAI/C,IAAA,CAACX,GAAG,EAAAyd,QAAA,CAAEV,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EAC7F,CACT,CACG,CACL,cAEDpc,IAAA,UAAA8c,QAAA,CACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACC,CAAC,EACP,CAAC,CAEL,CAAC,CAED,cAAe,CAAAzc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}