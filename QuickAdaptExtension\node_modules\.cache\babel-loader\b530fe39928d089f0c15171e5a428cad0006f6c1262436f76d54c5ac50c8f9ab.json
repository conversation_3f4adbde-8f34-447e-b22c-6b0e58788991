{"ast": null, "code": "import React,{useEffect,useState,useRef}from\"react\";import{Box,Button,IconButton,LinearProgress,MobileStepper,Popover,Typography}from\"@mui/material\";import CloseIcon from\"@mui/icons-material/Close\";import useDrawerStore from\"../../store/drawerStore\";// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from'react-perfect-scrollbar';import'react-perfect-scrollbar/dist/css/styles.css';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const HotspotPreview=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_textFieldProperties$,_textFieldProperties$2,_textFieldProperties$3,_imageProperties,_imageProperties$Cust,_imageProperties$Cust2,_savedGuideData$Guide34,_savedGuideData$Guide35,_savedGuideData$Guide36;let{anchorEl,guideStep,title,text,imageUrl,onClose,onPrevious,onContinue,videoUrl,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData,hotspotProperties,handleHotspotHover,handleHotspotClick,isHotspotPopupOpen,showHotspotenduser}=_ref;const{setCurrentStep,selectedTemplate,toolTipGuideMetaData,elementSelected,axisData,tooltipXaxis,tooltipYaxis,setOpenTooltip,openTooltip,pulseAnimationsH,hotspotGuideMetaData,selectedTemplateTour,selectedOption,ProgressColor}=useDrawerStore(state=>state);const[targetElement,setTargetElement]=useState(null);// State to track if the popover should be shown\n// State for popup visibility is managed through openTooltip\nconst[popupPosition,setPopupPosition]=useState(null);const[dynamicWidth,setDynamicWidth]=useState(null);const[hotspotSize,setHotspotSize]=useState(30);// Track hotspot size for dynamic popup positioning\nconst contentRef=useRef(null);const buttonContainerRef=useRef(null);let hotspot;const getElementByXPath=xpath=>{if(!xpath)return null;const result=document.evaluate(xpath,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);const node=result.singleNodeValue;if(node instanceof HTMLElement){return node;}else if(node!==null&&node!==void 0&&node.parentElement){return node.parentElement;// Return parent if it's a text node\n}else{return null;}};// Helper function to scroll to element\nconst scrollToElement=element=>{// Check if element is visible in viewport\nconst rect=element.getBoundingClientRect();const isElementVisible=rect.top>=0&&rect.left>=0&&rect.bottom<=window.innerHeight&&rect.right<=window.innerWidth;if(!isElementVisible){console.log(\"🔄 Hotspot: Element not visible, scrolling to element\");element.scrollIntoView({behavior:'smooth',block:'center',inline:'nearest'});}else{console.log(\"✅ Hotspot: Element already visible\");}};let xpath;if(savedGuideData)xpath=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[0])===null||_savedGuideData$Guide2===void 0?void 0:_savedGuideData$Guide2.ElementPath;const getElementPosition=xpath=>{const element=getElementByXPath(xpath||\"\");if(element){const rect=element.getBoundingClientRect();return{top:rect.top,//+ window.scrollY + yOffset, // Adjust for vertical scroll\nleft:rect.left// + window.scrollX + xOffset, // Adjust for horizontal scroll\n};}return null;};// State to track if scrolling is needed\nconst[needsScrolling,setNeedsScrolling]=useState(false);const scrollbarRef=useRef(null);// Function to calculate popup position below the hotspot\nconst calculatePopupPosition=(elementRect,hotspotSize,xOffset,yOffset)=>{const hotspotLeft=elementRect.x+xOffset;const hotspotTop=elementRect.y+yOffset;// Position popup below the hotspot for better user experience\nconst dynamicOffsetX=hotspotSize+5;// Align horizontally with hotspot\nconst dynamicOffsetY=hotspotSize+10;// Position below hotspot with spacing\nreturn{top:hotspotTop+window.scrollY+dynamicOffsetY,left:hotspotLeft+window.scrollX+dynamicOffsetX};};useEffect(()=>{const element=getElementByXPath(xpath);if(element){const rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,// Account for scrolling\nleft:rect.left+window.scrollX});}},[xpath]);useEffect(()=>{if(typeof window!==undefined){const position=getElementPosition(xpath||\"\");if(position){setPopupPosition(position);}}},[xpath]);useEffect(()=>{const element=getElementByXPath(xpath);// setTargetElement(element);\nif(element){}},[savedGuideData]);useEffect(()=>{var _guideStep;const element=getElementByXPath(guideStep===null||guideStep===void 0?void 0:(_guideStep=guideStep[currentStep-1])===null||_guideStep===void 0?void 0:_guideStep.ElementPath);setTargetElement(element);if(element){element.style.backgroundColor=\"red !important\";// Update popup position when target element changes\nconst rect=element.getBoundingClientRect();setPopupPosition({top:rect.top+window.scrollY,left:rect.left+window.scrollX});}},[guideStep,currentStep]);// Hotspot styles are applied directly in the applyHotspotStyles function\n// State for overlay value\nconst[,setOverlayValue]=useState(false);const handleContinue=()=>{if(selectedTemplate!==\"Tour\"){if(currentStep<totalSteps){var _savedGuideData$Guide3;const nextStep=currentStep+1;setCurrentStep(nextStep);// Auto-scroll to next step element if it exists\nconst nextStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide3=savedGuideData.GuideStep)===null||_savedGuideData$Guide3===void 0?void 0:_savedGuideData$Guide3[nextStep-1];if(nextStepData!==null&&nextStepData!==void 0&&nextStepData.ElementPath){console.log(\"🎯 Hotspot: Navigating to next step\",nextStep,\"with ElementPath:\",nextStepData.ElementPath);setTimeout(()=>{const nextElement=getElementByXPath(nextStepData.ElementPath||\"\");if(nextElement){console.log(\"✅ Hotspot: Found next step element, scrolling into view\");scrollToElement(nextElement);}else{console.log(\"❌ Hotspot: Next step element not found for ElementPath:\",nextStepData.ElementPath);}},100);}onContinue();renderNextPopup(currentStep<totalSteps);}}else{var _savedGuideData$Guide4;const nextStep=currentStep+1;setCurrentStep(nextStep);// Auto-scroll to next step element for Tour template\nconst nextStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide4=savedGuideData.GuideStep)===null||_savedGuideData$Guide4===void 0?void 0:_savedGuideData$Guide4[nextStep-1];if(nextStepData!==null&&nextStepData!==void 0&&nextStepData.ElementPath){console.log(\"🎯 Hotspot Tour: Navigating to next step\",nextStep,\"with ElementPath:\",nextStepData.ElementPath);setTimeout(()=>{const nextElement=getElementByXPath(nextStepData.ElementPath||\"\");if(nextElement){console.log(\"✅ Hotspot Tour: Found next step element, scrolling into view\");scrollToElement(nextElement);}else{console.log(\"❌ Hotspot Tour: Next step element not found for ElementPath:\",nextStepData.ElementPath);}},100);}const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}}};const renderNextPopup=shouldRenderNextPopup=>{var _savedGuideData$Guide5,_savedGuideData$Guide6,_savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11,_savedGuideData$Guide12,_savedGuideData$Guide13,_savedGuideData$Guide14;return shouldRenderNextPopup?/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:isHotspotPopupOpen,showHotspotenduser:showHotspotenduser,handleHotspotHover:handleHotspotHover,handleHotspotClick:handleHotspotClick,anchorEl:anchorEl,savedGuideData:savedGuideData,guideStep:guideStep,onClose:onClose,onPrevious:handlePrevious,onContinue:handleContinue,title:title,text:text,imageUrl:imageUrl,currentStep:currentStep+1,totalSteps:totalSteps,onDontShowAgain:onDontShowAgain,progress:progress,textFieldProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide5=savedGuideData.GuideStep)===null||_savedGuideData$Guide5===void 0?void 0:(_savedGuideData$Guide6=_savedGuideData$Guide5[currentStep])===null||_savedGuideData$Guide6===void 0?void 0:_savedGuideData$Guide6.TextFieldProperties,imageProperties:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide7=savedGuideData.GuideStep)===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7[currentStep])===null||_savedGuideData$Guide8===void 0?void 0:_savedGuideData$Guide8.ImageProperties,customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide9=savedGuideData.GuideStep)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9[currentStep])===null||_savedGuideData$Guide10===void 0?void 0:(_savedGuideData$Guide11=_savedGuideData$Guide10.ButtonSection)===null||_savedGuideData$Guide11===void 0?void 0:(_savedGuideData$Guide12=_savedGuideData$Guide11.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide12===void 0?void 0:_savedGuideData$Guide12.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:modalProperties,canvasProperties:canvasProperties,htmlSnippet:htmlSnippet,OverlayValue:OverlayValue,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide13=savedGuideData.GuideStep)===null||_savedGuideData$Guide13===void 0?void 0:(_savedGuideData$Guide14=_savedGuideData$Guide13[currentStep-1])===null||_savedGuideData$Guide14===void 0?void 0:_savedGuideData$Guide14.Hotspot)||{}}):null;};const handlePrevious=()=>{if(currentStep>1){var _savedGuideData$Guide15;const prevStep=currentStep-1;setCurrentStep(prevStep);// Auto-scroll to previous step element if it exists\nconst prevStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide15=savedGuideData.GuideStep)===null||_savedGuideData$Guide15===void 0?void 0:_savedGuideData$Guide15[prevStep-1];if(prevStepData!==null&&prevStepData!==void 0&&prevStepData.ElementPath){console.log(\"🔙 Hotspot: Navigating to previous step\",prevStep,\"with ElementPath:\",prevStepData.ElementPath);setTimeout(()=>{const prevElement=getElementByXPath(prevStepData.ElementPath||\"\");if(prevElement){console.log(\"✅ Hotspot: Found previous step element, scrolling into view\");scrollToElement(prevElement);}else{console.log(\"❌ Hotspot: Previous step element not found for ElementPath:\",prevStepData.ElementPath);}},100);}onPrevious();}};useEffect(()=>{if(OverlayValue){setOverlayValue(true);}else{setOverlayValue(false);}},[OverlayValue]);// Image fit is used directly in the component\nconst getAnchorAndTransformOrigins=position=>{switch(position){case\"top-left\":return{anchorOrigin:{vertical:\"top\",horizontal:\"left\"},transformOrigin:{vertical:\"bottom\",horizontal:\"right\"}};case\"top-right\":return{anchorOrigin:{vertical:\"top\",horizontal:\"right\"},transformOrigin:{vertical:\"bottom\",horizontal:\"left\"}};case\"bottom-left\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"left\"},transformOrigin:{vertical:\"top\",horizontal:\"right\"}};case\"bottom-right\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};case\"center-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"top-center\":return{anchorOrigin:{vertical:\"top\",horizontal:\"center\"},transformOrigin:{vertical:\"bottom\",horizontal:\"center\"}};case\"left-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"left\"},transformOrigin:{vertical:\"center\",horizontal:\"right\"}};case\"bottom-center\":return{anchorOrigin:{vertical:\"bottom\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};case\"right-center\":return{anchorOrigin:{vertical:\"center\",horizontal:\"right\"},transformOrigin:{vertical:\"center\",horizontal:\"left\"}};default:return{anchorOrigin:{vertical:\"center\",horizontal:\"center\"},transformOrigin:{vertical:\"center\",horizontal:\"center\"}};}};const{anchorOrigin,transformOrigin}=getAnchorAndTransformOrigins((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center center\");const textStyle={fontWeight:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$=textFieldProperties.TextProperties)!==null&&_textFieldProperties$!==void 0&&_textFieldProperties$.Bold?\"bold\":\"normal\",fontStyle:textFieldProperties!==null&&textFieldProperties!==void 0&&(_textFieldProperties$2=textFieldProperties.TextProperties)!==null&&_textFieldProperties$2!==void 0&&_textFieldProperties$2.Italic?\"italic\":\"normal\",color:(textFieldProperties===null||textFieldProperties===void 0?void 0:(_textFieldProperties$3=textFieldProperties.TextProperties)===null||_textFieldProperties$3===void 0?void 0:_textFieldProperties$3.TextColor)||\"#000000\",textAlign:(textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.Alignment)||\"left\"};// Image styles are applied directly in the component\nconst renderHtmlSnippet=snippet=>{// Return the raw HTML snippet for rendering\nreturn{__html:snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g,(_match,p1,p2,p3)=>{return`${p1}${p2}\" target=\"_blank\"${p3}`;})};};// Helper function to check if popup has only buttons (no text or images)\nconst hasOnlyButtons=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasButtons&&!hasImage&&!hasText;};// Helper function to check if popup has only text (no buttons or images)\nconst hasOnlyText=()=>{const hasImage=imageProperties&&imageProperties.length>0&&imageProperties.some(prop=>prop.CustomImage&&prop.CustomImage.some(img=>img.Url));const hasText=textFieldProperties&&textFieldProperties.length>0&&textFieldProperties.some(field=>field.Text&&field.Text.trim()!==\"\");const hasButtons=customButton&&customButton.length>0;return hasText&&!hasImage&&!hasButtons;};// Function to calculate the optimal width based on content and buttons\nconst calculateOptimalWidth=()=>{var _contentRef$current,_buttonContainerRef$c;// If we have a fixed width from canvas settings and not a compact popup, use that\nif(canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width&&!hasOnlyButtons()&&!hasOnlyText()){return`${canvasProperties.Width}px`;}// For popups with only buttons or only text, use auto width\nif(hasOnlyButtons()||hasOnlyText()){return\"auto\";}// Get the width of content and button container\nconst contentWidth=((_contentRef$current=contentRef.current)===null||_contentRef$current===void 0?void 0:_contentRef$current.scrollWidth)||0;const buttonWidth=((_buttonContainerRef$c=buttonContainerRef.current)===null||_buttonContainerRef$c===void 0?void 0:_buttonContainerRef$c.scrollWidth)||0;// Use the larger of the two, with some minimum and maximum constraints\nconst optimalWidth=Math.max(contentWidth,buttonWidth);// Add some padding to ensure text has room to wrap naturally\nconst paddedWidth=optimalWidth+20;// 10px padding on each side\n// Ensure width is between reasonable bounds\nconst minWidth=250;// Minimum width\nconst maxWidth=800;// Maximum width\nconst finalWidth=Math.max(minWidth,Math.min(paddedWidth,maxWidth));return`${finalWidth}px`;};// Update dynamic width when content or buttons change\nuseEffect(()=>{// Use requestAnimationFrame to ensure DOM has been updated\nrequestAnimationFrame(()=>{const newWidth=calculateOptimalWidth();setDynamicWidth(newWidth);});},[textFieldProperties,imageProperties,customButton,currentStep]);// Recalculate popup position when hotspot size changes\nuseEffect(()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData===void 0?void 0:_toolTipGuideMetaData.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}},[hotspotSize,xpath,toolTipGuideMetaData]);// Recalculate popup position on window resize\nuseEffect(()=>{const handleResize=()=>{if(xpath&&hotspotSize){const element=getElementByXPath(xpath);if(element){var _toolTipGuideMetaData2;const rect=element.getBoundingClientRect();const hotspotPropData=(_toolTipGuideMetaData2=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData2===void 0?void 0:_toolTipGuideMetaData2.hotspots;const xOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.YPosition)||\"4\");const popupPos=calculatePopupPosition(rect,hotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}}};window.addEventListener('resize',handleResize);return()=>window.removeEventListener('resize',handleResize);},[xpath,hotspotSize,toolTipGuideMetaData]);const groupedButtons=customButton.reduce((acc,button)=>{const containerId=button.ContainerId||\"default\";// Use a ContainerId or fallback\nif(!acc[containerId]){acc[containerId]=[];}acc[containerId].push(button);return acc;},{});const canvasStyle={position:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\",borderRadius:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Radius)||\"4px\",borderWidth:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderSize)||\"0px\",borderColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BorderColor)||\"black\",borderStyle:\"solid\",backgroundColor:(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.BackgroundColor)||\"white\",maxWidth:hasOnlyButtons()||hasOnlyText()?\"none !important\":dynamicWidth?`${dynamicWidth} !important`:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width?`${canvasProperties.Width}px !important`:\"800px\",width:hasOnlyButtons()||hasOnlyText()?\"auto !important\":dynamicWidth?`${dynamicWidth} !important`:canvasProperties!==null&&canvasProperties!==void 0&&canvasProperties.Width?`${canvasProperties.Width}px !important`:\"300px\"};const sectionHeight=((_imageProperties=imageProperties[currentStep-1])===null||_imageProperties===void 0?void 0:(_imageProperties$Cust=_imageProperties.CustomImage)===null||_imageProperties$Cust===void 0?void 0:(_imageProperties$Cust2=_imageProperties$Cust[currentStep-1])===null||_imageProperties$Cust2===void 0?void 0:_imageProperties$Cust2.SectionHeight)||\"auto\";const handleButtonAction=action=>{if(action.Action===\"open-url\"||action.Action===\"open\"||action.Action===\"openurl\"){const targetUrl=action.TargetUrl;if(action.ActionValue===\"same-tab\"){// Open the URL in the same tab\nwindow.location.href=targetUrl;}else{// Open the URL in a new tab\nwindow.open(targetUrl,\"_blank\",\"noopener noreferrer\");}}else{if(action.Action==\"Previous\"||action.Action==\"previous\"||action.ActionValue==\"Previous\"||action.ActionValue==\"Previous\"){handlePrevious();}else if(action.Action==\"Next\"||action.Action==\"next\"||action.ActionValue==\"Next\"||action.ActionValue==\"next\"){handleContinue();}else if(action.Action==\"Restart\"||action.ActionValue==\"Restart\"){var _savedGuideData$Guide16,_savedGuideData$Guide17;// Reset to the first step\nconsole.log(\"🔄 Hotspot: Restarting tour to first step\");setCurrentStep(1);// If there's a specific URL for the first step, navigate to it\nif(savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide16=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide16!==void 0&&(_savedGuideData$Guide17=_savedGuideData$Guide16[0])!==null&&_savedGuideData$Guide17!==void 0&&_savedGuideData$Guide17.ElementPath){setTimeout(()=>{const firstStepElementPath=savedGuideData.GuideStep[0].ElementPath||\"\";const firstStepElement=getElementByXPath(firstStepElementPath);if(firstStepElement){console.log(\"✅ Hotspot: Found first step element, scrolling into view\");scrollToElement(firstStepElement);}else{console.log(\"❌ Hotspot: First step element not found\");}},100);}}}setOverlayValue(false);};useEffect(()=>{var _guideStep2,_guideStep2$Hotspot;if(guideStep!==null&&guideStep!==void 0&&(_guideStep2=guideStep[currentStep-1])!==null&&_guideStep2!==void 0&&(_guideStep2$Hotspot=_guideStep2.Hotspot)!==null&&_guideStep2$Hotspot!==void 0&&_guideStep2$Hotspot.ShowByDefault){// Show tooltip by default\nsetOpenTooltip(true);}},[guideStep===null||guideStep===void 0?void 0:guideStep[currentStep-1],currentStep,setOpenTooltip]);// Add effect to handle isHotspotPopupOpen prop changes\nuseEffect(()=>{if(isHotspotPopupOpen){var _toolTipGuideMetaData3,_toolTipGuideMetaData4,_savedGuideData$Guide18,_savedGuideData$Guide19,_savedGuideData$Guide20,_savedGuideData$Guide21;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData3=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData3!==void 0&&_toolTipGuideMetaData3.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData4=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData4===void 0?void 0:_toolTipGuideMetaData4.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide18=savedGuideData.GuideStep)===null||_savedGuideData$Guide18===void 0?void 0:(_savedGuideData$Guide19=_savedGuideData$Guide18[currentStep-1])===null||_savedGuideData$Guide19===void 0?void 0:_savedGuideData$Guide19.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide20=savedGuideData.GuideStep)===null||_savedGuideData$Guide20===void 0?void 0:(_savedGuideData$Guide21=_savedGuideData$Guide20[0])===null||_savedGuideData$Guide21===void 0?void 0:_savedGuideData$Guide21.Hotspot;// Only show tooltip by default if ShowByDefault is true\n// For \"Hovering Hotspot\", we'll wait for the hover event\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[isHotspotPopupOpen,toolTipGuideMetaData]);// Add effect to handle showHotspotenduser prop changes\nuseEffect(()=>{if(showHotspotenduser){var _toolTipGuideMetaData5,_toolTipGuideMetaData6,_savedGuideData$Guide22,_savedGuideData$Guide23,_savedGuideData$Guide24,_savedGuideData$Guide25;// Get the ShowUpon property\nconst hotspotPropData=selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData5=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData5!==void 0&&_toolTipGuideMetaData5.hotspots?toolTipGuideMetaData[currentStep-1].hotspots:(_toolTipGuideMetaData6=toolTipGuideMetaData[0])===null||_toolTipGuideMetaData6===void 0?void 0:_toolTipGuideMetaData6.hotspots;const hotspotData=selectedTemplateTour===\"Hotspot\"?savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide22=savedGuideData.GuideStep)===null||_savedGuideData$Guide22===void 0?void 0:(_savedGuideData$Guide23=_savedGuideData$Guide22[currentStep-1])===null||_savedGuideData$Guide23===void 0?void 0:_savedGuideData$Guide23.Hotspot:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide24=savedGuideData.GuideStep)===null||_savedGuideData$Guide24===void 0?void 0:(_savedGuideData$Guide25=_savedGuideData$Guide24[0])===null||_savedGuideData$Guide25===void 0?void 0:_savedGuideData$Guide25.Hotspot;// Only show tooltip by default if ShowByDefault is true\nif(hotspotPropData!==null&&hotspotPropData!==void 0&&hotspotPropData.ShowByDefault){// Set openTooltip to true\nsetOpenTooltip(true);}else{// Otherwise, initially hide the tooltip\nsetOpenTooltip(false);}}},[showHotspotenduser,toolTipGuideMetaData]);// Add a global click handler to detect clicks outside the hotspot to close the tooltip\nuseEffect(()=>{const handleGlobalClick=e=>{const hotspotElement=document.getElementById(\"hotspotBlink\");// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\nif(hotspotElement&&hotspotElement.contains(e.target)){return;}// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on clicks outside anymore\n};document.addEventListener(\"click\",handleGlobalClick);return()=>{document.removeEventListener(\"click\",handleGlobalClick);};},[toolTipGuideMetaData]);// Check if content needs scrolling with improved detection\nuseEffect(()=>{const checkScrollNeeded=()=>{if(contentRef.current){// Force a reflow to get accurate measurements\ncontentRef.current.style.height='auto';const contentHeight=contentRef.current.scrollHeight;const containerHeight=320;// max-height value\nconst shouldScroll=contentHeight>containerHeight;setNeedsScrolling(shouldScroll);// Force update scrollbar\nif(scrollbarRef.current){// Try multiple methods to update the scrollbar\nif(scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}// Force re-initialization if needed\nsetTimeout(()=>{if(scrollbarRef.current&&scrollbarRef.current.updateScroll){scrollbarRef.current.updateScroll();}},10);}}};checkScrollNeeded();const timeouts=[setTimeout(checkScrollNeeded,50),setTimeout(checkScrollNeeded,100),setTimeout(checkScrollNeeded,200),setTimeout(checkScrollNeeded,500)];let resizeObserver=null;let mutationObserver=null;if(contentRef.current&&window.ResizeObserver){resizeObserver=new ResizeObserver(()=>{setTimeout(checkScrollNeeded,10);});resizeObserver.observe(contentRef.current);}if(contentRef.current&&window.MutationObserver){mutationObserver=new MutationObserver(()=>{setTimeout(checkScrollNeeded,10);});mutationObserver.observe(contentRef.current,{childList:true,subtree:true,attributes:true,attributeFilter:['style','class']});}return()=>{timeouts.forEach(clearTimeout);if(resizeObserver){resizeObserver.disconnect();}if(mutationObserver){mutationObserver.disconnect();}};},[currentStep]);// We no longer need the persistent monitoring effect since we want the tooltip\n// to close when the mouse leaves the hotspot\nfunction getAlignment(alignment){switch(alignment){case\"start\":return\"flex-start\";case\"end\":return\"flex-end\";case\"center\":default:return\"center\";}}const getCanvasPosition=function(){let position=arguments.length>0&&arguments[0]!==undefined?arguments[0]:\"center-center\";switch(position){case\"bottom-left\":return{top:\"auto !important\"};case\"bottom-right\":return{top:\"auto !important\"};case\"bottom-center\":return{top:\"auto !important\"};case\"center-center\":return{top:\"25% !important\"};case\"left-center\":return{top:imageUrl===\"\"?\"40% !important\":\"20% !important\"};case\"right-center\":return{top:\"10% !important\"};case\"top-left\":return{top:\"10% !important\"};case\"top-right\":return{top:\"10% !important\"};case\"top-center\":return{top:\"9% !important\"};default:return{top:\"25% !important\"};}};// function to get the correct property value based on tour vs normal hotspot\nconst getHotspotProperty=(propName,hotspotPropData,hotspotData)=>{if(selectedTemplateTour===\"Hotspot\"){// For tour hotspots, use saved data first, fallback to metadata\nswitch(propName){case'PulseAnimation':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.PulseAnimation)!==undefined?hotspotData.PulseAnimation:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.PulseAnimation;case'StopAnimation':// Always use stopAnimationUponInteraction for consistency\nreturn(hotspotData===null||hotspotData===void 0?void 0:hotspotData.stopAnimationUponInteraction)!==undefined?hotspotData.stopAnimationUponInteraction:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;case'ShowUpon':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowUpon)!==undefined?hotspotData.ShowUpon:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowUpon;case'ShowByDefault':return(hotspotData===null||hotspotData===void 0?void 0:hotspotData.ShowByDefault)!==undefined?hotspotData.ShowByDefault:hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.ShowByDefault;default:return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}}else{// For normal hotspots, use metadata\nif(propName==='StopAnimation'){return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.stopAnimationUponInteraction;}return hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData[propName];}};const applyHotspotStyles=(hotspot,hotspotPropData,hotspotData,left,top)=>{hotspot.style.position=\"absolute\";hotspot.style.left=`${left}px`;hotspot.style.top=`${top}px`;hotspot.style.width=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;// Default size if not provided\nhotspot.style.height=`${hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Size}px`;hotspot.style.backgroundColor=hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Color;hotspot.style.borderRadius=\"50%\";hotspot.style.zIndex=\"auto !important\";// Increased z-index\nhotspot.style.transition=\"none\";hotspot.style.pointerEvents=\"auto\";// Ensure clicks are registered\nhotspot.innerHTML=\"\";if((hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Info\"||(hotspotPropData===null||hotspotPropData===void 0?void 0:hotspotPropData.Type)===\"Question\"){const textSpan=document.createElement(\"span\");textSpan.innerText=hotspotPropData.Type===\"Info\"?\"i\":\"?\";textSpan.style.color=\"white\";textSpan.style.fontSize=\"14px\";textSpan.style.fontWeight=\"bold\";textSpan.style.fontStyle=hotspotPropData.Type===\"Info\"?\"italic\":\"normal\";textSpan.style.display=\"flex\";textSpan.style.alignItems=\"center\";textSpan.style.justifyContent=\"center\";textSpan.style.width=\"100%\";textSpan.style.height=\"100%\";hotspot.appendChild(textSpan);}// Apply animation class if needed\n// Track if pulse has been stopped by hover\nconst pulseAnimationEnabled=getHotspotProperty('PulseAnimation',hotspotPropData,hotspotData);const shouldPulse=selectedTemplateTour===\"Hotspot\"?pulseAnimationEnabled!==false&&!hotspot._pulseStopped:hotspotPropData&&pulseAnimationsH&&!hotspot._pulseStopped;if(shouldPulse){hotspot.classList.add(\"pulse-animation\");hotspot.classList.remove(\"pulse-animation-removed\");}else{hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");}// Ensure the hotspot is visible and clickable\nhotspot.style.display=\"flex\";hotspot.style.pointerEvents=\"auto\";// No need for separate animation control functions here\n// Animation will be controlled directly in the event handlers\n// Set initial state of openTooltip based on ShowByDefault and ShowUpon\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{// If not showing by default, only show based on interaction type\n//setOpenTooltip(false);\n}// Only clone and replace if the hotspot doesn't have event listeners already\n// This prevents losing the _pulseStopped state unnecessarily\nif(!hotspot.hasAttribute('data-listeners-attached')){const newHotspot=hotspot.cloneNode(true);// Copy the _pulseStopped property if it exists\nif(hotspot._pulseStopped!==undefined){newHotspot._pulseStopped=hotspot._pulseStopped;}if(hotspot.parentNode){hotspot.parentNode.replaceChild(newHotspot,hotspot);hotspot=newHotspot;}}// Ensure pointer events are enabled\nhotspot.style.pointerEvents=\"auto\";// Define combined event handlers that handle both animation and tooltip\nconst showUpon=getHotspotProperty('ShowUpon',hotspotPropData,hotspotData);const handleHover=e=>{e.stopPropagation();console.log(\"Hover detected on hotspot\");// Show tooltip if ShowUpon is \"Hovering Hotspot\"\nif(showUpon===\"Hovering Hotspot\"){// Set openTooltip to true when hovering\nsetOpenTooltip(true);// Call the passed hover handler if it exists\nif(typeof handleHotspotHover===\"function\"){handleHotspotHover();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};const handleMouseOut=e=>{e.stopPropagation();// Hide tooltip when mouse leaves the hotspot\n// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showUpon===\"Hovering Hotspot\"&&!showByDefault){// setOpenTooltip(false);\n}};const handleClick=e=>{e.stopPropagation();console.log(\"Click detected on hotspot\");// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\nif(showUpon===\"Clicking Hotspot\"||!showUpon){// Toggle the tooltip state\nsetOpenTooltip(!openTooltip);// Call the passed click handler if it exists\nif(typeof handleHotspotClick===\"function\"){handleHotspotClick();}// Stop animation if configured to do so\nconst stopAnimationSetting=getHotspotProperty('StopAnimation',hotspotPropData,hotspotData);if(stopAnimationSetting){hotspot.classList.remove(\"pulse-animation\");hotspot.classList.add(\"pulse-animation-removed\");hotspot._pulseStopped=true;// Mark as stopped so it won't be re-applied\n}}};// Add appropriate event listeners based on ShowUpon property\nif(!hotspot.hasAttribute('data-listeners-attached')){if(showUpon===\"Hovering Hotspot\"){// For hover interaction\nhotspot.addEventListener(\"mouseover\",handleHover);hotspot.addEventListener(\"mouseout\",handleMouseOut);// Also add click handler for better user experience\nhotspot.addEventListener(\"click\",handleClick);}else{// For click interaction (default)\nhotspot.addEventListener(\"click\",handleClick);}// Mark that listeners have been attached\nhotspot.setAttribute('data-listeners-attached','true');}};useEffect(()=>{let element;let steps;const fetchGuideDetails=async()=>{try{var _savedGuideData$Guide26,_savedGuideData$Guide27,_steps,_steps$;//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\nsteps=(savedGuideData===null||savedGuideData===void 0?void 0:savedGuideData.GuideStep)||[];// For tour hotspots, use the current step's element path\nconst elementPath=selectedTemplateTour===\"Hotspot\"&&savedGuideData!==null&&savedGuideData!==void 0&&(_savedGuideData$Guide26=savedGuideData.GuideStep)!==null&&_savedGuideData$Guide26!==void 0&&(_savedGuideData$Guide27=_savedGuideData$Guide26[currentStep-1])!==null&&_savedGuideData$Guide27!==void 0&&_savedGuideData$Guide27.ElementPath?savedGuideData.GuideStep[currentStep-1].ElementPath:((_steps=steps)===null||_steps===void 0?void 0:(_steps$=_steps[0])===null||_steps$===void 0?void 0:_steps$.ElementPath)||\"\";element=getElementByXPath(elementPath||\"\");setTargetElement(element);if(element){// element.style.outline = \"2px solid red\";\n}// Check if this is a hotspot scenario (normal or tour)\nconst isHotspotScenario=selectedTemplate===\"Hotspot\"||selectedTemplateTour===\"Hotspot\"||title===\"Hotspot\"||selectedTemplate===\"Tour\"&&selectedTemplateTour===\"Hotspot\";if(isHotspotScenario){var _toolTipGuideMetaData7,_toolTipGuideMetaData8,_hotspotPropData,_hotspotPropData2,_hotspotPropData3,_hotspotPropData4;// Get hotspot properties - prioritize tour data for tour hotspots\nlet hotspotPropData;let hotspotData;if(selectedTemplateTour===\"Hotspot\"&&toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData7=toolTipGuideMetaData[currentStep-1])!==null&&_toolTipGuideMetaData7!==void 0&&_toolTipGuideMetaData7.hotspots){var _savedGuideData$Guide28,_savedGuideData$Guide29;// Tour hotspot - use current step metadata\nhotspotPropData=toolTipGuideMetaData[currentStep-1].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide28=savedGuideData.GuideStep)===null||_savedGuideData$Guide28===void 0?void 0:(_savedGuideData$Guide29=_savedGuideData$Guide28[currentStep-1])===null||_savedGuideData$Guide29===void 0?void 0:_savedGuideData$Guide29.Hotspot;}else if(toolTipGuideMetaData!==null&&toolTipGuideMetaData!==void 0&&(_toolTipGuideMetaData8=toolTipGuideMetaData[0])!==null&&_toolTipGuideMetaData8!==void 0&&_toolTipGuideMetaData8.hotspots){var _savedGuideData$Guide30,_savedGuideData$Guide31;// Normal hotspot - use first metadata entry\nhotspotPropData=toolTipGuideMetaData[0].hotspots;hotspotData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide30=savedGuideData.GuideStep)===null||_savedGuideData$Guide30===void 0?void 0:(_savedGuideData$Guide31=_savedGuideData$Guide30[0])===null||_savedGuideData$Guide31===void 0?void 0:_savedGuideData$Guide31.Hotspot;}else{var _savedGuideData$Guide32,_savedGuideData$Guide33;// Fallback to default values for tour hotspots without metadata\nhotspotPropData={XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Hovering Hotspot\",ShowByDefault:false};hotspotData=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide32=savedGuideData.GuideStep)===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32[currentStep-1])===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.Hotspot)||{};}const xOffset=parseFloat(((_hotspotPropData=hotspotPropData)===null||_hotspotPropData===void 0?void 0:_hotspotPropData.XPosition)||\"4\");const yOffset=parseFloat(((_hotspotPropData2=hotspotPropData)===null||_hotspotPropData2===void 0?void 0:_hotspotPropData2.YPosition)||\"4\");const currentHotspotSize=parseFloat(((_hotspotPropData3=hotspotPropData)===null||_hotspotPropData3===void 0?void 0:_hotspotPropData3.Size)||\"30\");// Update hotspot size state\nsetHotspotSize(currentHotspotSize);let left,top;if(element){const rect=element.getBoundingClientRect();left=rect.x+xOffset;top=rect.y+(yOffset>0?-yOffset:Math.abs(yOffset));// Calculate popup position below the hotspot\nconst popupPos=calculatePopupPosition(rect,currentHotspotSize,xOffset,yOffset);setPopupPosition(popupPos);}// Check if hotspot already exists, preserve it to maintain _pulseStopped state\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){hotspot=existingHotspot;// Don't reset _pulseStopped if it already exists\n}else{// Create new hotspot only if it doesn't exist\nhotspot=document.createElement(\"div\");hotspot.id=\"hotspotBlink\";// Fixed ID for easier reference\nhotspot._pulseStopped=false;// Set only on creation\ndocument.body.appendChild(hotspot);}hotspot.style.cursor=\"pointer\";hotspot.style.pointerEvents=\"auto\";// Ensure it can receive mouse events\n// Make sure the hotspot is visible and clickable\nhotspot.style.zIndex=\"9999\";// If ShowByDefault is true, set openTooltip to true immediately\nif((_hotspotPropData4=hotspotPropData)!==null&&_hotspotPropData4!==void 0&&_hotspotPropData4.ShowByDefault){setOpenTooltip(true);}// Set styles first\napplyHotspotStyles(hotspot,hotspotPropData,hotspotData,left,top);// Set initial tooltip visibility based on ShowByDefault\nconst showByDefault=getHotspotProperty('ShowByDefault',hotspotPropData,hotspotData);if(showByDefault){setOpenTooltip(true);}else{//setOpenTooltip(false);\n}// We don't need to add event listeners here as they're already added in applyHotspotStyles\n}}catch(error){console.error(\"Error in fetchGuideDetails:\",error);}};fetchGuideDetails();return()=>{const existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.onclick=null;existingHotspot.onmouseover=null;existingHotspot.onmouseout=null;}};},[savedGuideData,toolTipGuideMetaData,isHotspotPopupOpen,showHotspotenduser,selectedTemplateTour,currentStep// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n]);const enableProgress=(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:(_savedGuideData$Guide35=_savedGuideData$Guide34[0])===null||_savedGuideData$Guide35===void 0?void 0:(_savedGuideData$Guide36=_savedGuideData$Guide35.Tooltip)===null||_savedGuideData$Guide36===void 0?void 0:_savedGuideData$Guide36.EnableProgress)||false;function getProgressTemplate(selectedOption){var _savedGuideData$Guide37,_savedGuideData$Guide38,_savedGuideData$Guide39;if(selectedOption===1){return\"dots\";}else if(selectedOption===2){return\"linear\";}else if(selectedOption===3){return\"BreadCrumbs\";}else if(selectedOption===4){return\"breadcrumbs\";}return(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide37=savedGuideData.GuideStep)===null||_savedGuideData$Guide37===void 0?void 0:(_savedGuideData$Guide38=_savedGuideData$Guide37[0])===null||_savedGuideData$Guide38===void 0?void 0:(_savedGuideData$Guide39=_savedGuideData$Guide38.Tooltip)===null||_savedGuideData$Guide39===void 0?void 0:_savedGuideData$Guide39.ProgressTemplate)||\"dots\";}const progressTemplate=getProgressTemplate(selectedOption);const renderProgress=()=>{if(!enableProgress)return null;if(progressTemplate===\"dots\"){return/*#__PURE__*/_jsx(MobileStepper,{variant:\"dots\",steps:totalSteps,position:\"static\",activeStep:currentStep-1,sx:{backgroundColor:\"transparent\",position:\"inherit !important\",\"& .MuiMobileStepper-dotActive\":{backgroundColor:ProgressColor// Active dot\n}},backButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}}),nextButton:/*#__PURE__*/_jsx(Button,{style:{visibility:\"hidden\"}})});}if(progressTemplate===\"BreadCrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"center\",gap:\"5px\",padding:\"8px\"},children:Array.from({length:totalSteps}).map((_,index)=>/*#__PURE__*/_jsx(\"div\",{style:{width:\"14px\",height:\"4px\",backgroundColor:index===currentStep-1?ProgressColor:\"#e0e0e0\",// Active color and inactive color\nborderRadius:\"100px\"}},index))});}if(progressTemplate===\"breadcrumbs\"){return/*#__PURE__*/_jsx(Box,{sx:{display:\"flex\",alignItems:\"center\",placeContent:\"flex-start\"},children:/*#__PURE__*/_jsxs(Typography,{sx:{padding:\"8px\",color:ProgressColor},children:[\"Step \",currentStep,\" of \",totalSteps]})});}if(progressTemplate===\"linear\"){return/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:\"6px\",borderRadius:\"20px\",margin:\"6px 10px\",\"& .MuiLinearProgress-bar\":{backgroundColor:ProgressColor// progress bar color\n}}})})});}return null;};return/*#__PURE__*/_jsxs(_Fragment,{children:[targetElement&&/*#__PURE__*/_jsx(\"div\",{children:openTooltip&&/*#__PURE__*/_jsxs(Popover,{open:Boolean(popupPosition)||Boolean(anchorEl),anchorEl:anchorEl,onClose:()=>{// We want to keep the tooltip open once it's been displayed\n// So we're not closing it on Popover close events\n},anchorOrigin:anchorOrigin,transformOrigin:transformOrigin,anchorReference:\"anchorPosition\",anchorPosition:popupPosition?{top:popupPosition.top+10+(parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\"))),left:popupPosition.left+10+parseFloat(tooltipXaxis||\"0\")}:undefined,sx:{// \"& .MuiBackdrop-root\": {\n//     position: 'relative !important', // Ensures higher specificity\n// },\n\"pointer-events\":anchorEl?\"auto\":\"auto\",'& .MuiPaper-root:not(.MuiMobileStepper-root)':{zIndex:1000,// borderRadius: \"1px\",\n...canvasStyle,//...getAnchorAndTransformOrigins,\n//top: \"16% !important\",\n// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n...getCanvasPosition((canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Position)||\"center-center\"),top:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.top)||0)+(tooltipYaxis&&tooltipYaxis!='undefined'?parseFloat(tooltipYaxis||\"0\")>0?-parseFloat(tooltipYaxis||\"0\"):Math.abs(parseFloat(tooltipYaxis||\"0\")):0)}px !important`,left:`${((popupPosition===null||popupPosition===void 0?void 0:popupPosition.left)||0)+(tooltipXaxis&&tooltipXaxis!='undefined'?parseFloat(tooltipXaxis)||0:0)}px !important`,overflow:\"hidden\"}},disableScrollLock:true,children:[/*#__PURE__*/_jsx(\"div\",{style:{placeContent:\"end\",display:\"flex\"},children:(modalProperties===null||modalProperties===void 0?void 0:modalProperties.DismissOption)&&/*#__PURE__*/_jsx(IconButton,{onClick:()=>{// Only close if explicitly requested by user clicking the close button\n//setOpenTooltip(false);\n},sx:{position:\"fixed\",boxShadow:\"rgba(0, 0, 0, 0.06) 0px 4px 8px\",left:\"auto\",right:\"auto\",margin:\"-15px\",background:\"#fff !important\",border:\"1px solid #ccc\",zIndex:\"999999\",borderRadius:\"50px\",padding:\"5px !important\"},children:/*#__PURE__*/_jsx(CloseIcon,{sx:{zoom:1,color:\"#000\"}})})}),/*#__PURE__*/_jsx(PerfectScrollbar,{ref:scrollbarRef,style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\"},options:{suppressScrollY:!needsScrolling,suppressScrollX:true,wheelPropagation:false,swipeEasing:true,minScrollbarLength:20,scrollingThreshold:1000,scrollYMarginOffset:0},children:/*#__PURE__*/_jsx(\"div\",{style:{maxHeight:hasOnlyButtons()||hasOnlyText()?\"auto\":\"400px\",overflow:hasOnlyButtons()||hasOnlyText()?\"visible\":\"hidden auto\",width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:/*#__PURE__*/_jsxs(Box,{style:{padding:hasOnlyButtons()?\"0\":hasOnlyText()?\"0\":(canvasProperties===null||canvasProperties===void 0?void 0:canvasProperties.Padding)||\"10px\",height:hasOnlyButtons()?\"auto\":sectionHeight,width:hasOnlyButtons()||hasOnlyText()?\"auto\":undefined,margin:hasOnlyButtons()?\"0\":undefined},children:[/*#__PURE__*/_jsxs(Box,{ref:contentRef,display:\"flex\",flexDirection:\"column\",flexWrap:\"wrap\",justifyContent:\"center\",sx:{width:hasOnlyText()?\"auto\":\"100%\",padding:hasOnlyText()?\"0\":undefined},children:[imageProperties===null||imageProperties===void 0?void 0:imageProperties.map(imageProp=>imageProp.CustomImage.map((customImg,imgIndex)=>/*#__PURE__*/_jsx(Box,{component:\"img\",src:customImg.Url,alt:customImg.AltText||\"Image\",sx:{maxHeight:imageProp.MaxImageHeight||customImg.MaxImageHeight||\"500px\",textAlign:imageProp.Alignment||\"center\",objectFit:customImg.Fit||\"contain\",//  width: \"500px\",\nheight:`${customImg.SectionHeight||250}px`,background:customImg.BackgroundColor||\"#ffffff\",margin:\"10px 0\"},onClick:()=>{if(imageProp.Hyperlink){const targetUrl=imageProp.Hyperlink;window.open(targetUrl,\"_blank\",\"noopener noreferrer\");}},style:{cursor:imageProp.Hyperlink?\"pointer\":\"default\"}},`${imageProp.Id}-${imgIndex}`))),textFieldProperties===null||textFieldProperties===void 0?void 0:textFieldProperties.map((textField,index)=>{var _textField$TextProper,_textField$TextProper2;return textField.Text&&/*#__PURE__*/_jsx(Typography,{className:\"qadpt-preview\",// Use a unique key, either Id or index\nsx:{textAlign:((_textField$TextProper=textField.TextProperties)===null||_textField$TextProper===void 0?void 0:_textField$TextProper.TextFormat)||textStyle.textAlign,color:((_textField$TextProper2=textField.TextProperties)===null||_textField$TextProper2===void 0?void 0:_textField$TextProper2.TextColor)||textStyle.color,whiteSpace:\"pre-wrap\",wordBreak:\"break-word\",padding:\"0 5px\"},dangerouslySetInnerHTML:renderHtmlSnippet(textField.Text)// Render the raw HTML\n},textField.Id||index);})]}),Object.keys(groupedButtons).map(containerId=>{var _groupedButtons$conta,_groupedButtons$conta2;return/*#__PURE__*/_jsx(Box,{ref:buttonContainerRef,sx:{display:\"flex\",justifyContent:getAlignment((_groupedButtons$conta=groupedButtons[containerId][0])===null||_groupedButtons$conta===void 0?void 0:_groupedButtons$conta.Alignment),flexWrap:\"wrap\",margin:hasOnlyButtons()?0:\"5px 0\",backgroundColor:(_groupedButtons$conta2=groupedButtons[containerId][0])===null||_groupedButtons$conta2===void 0?void 0:_groupedButtons$conta2.BackgroundColor,padding:hasOnlyButtons()?\"4px\":\"5px 0\",width:hasOnlyButtons()?\"auto\":\"100%\",borderRadius:hasOnlyButtons()?\"15px\":undefined},children:groupedButtons[containerId].map((button,index)=>{var _button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3,_button$ButtonPropert4,_button$ButtonPropert5,_button$ButtonPropert6,_button$ButtonPropert7;return/*#__PURE__*/_jsx(Button,{onClick:()=>handleButtonAction(button.ButtonAction),variant:\"contained\",sx:{marginRight:hasOnlyButtons()?\"5px\":\"13px\",margin:hasOnlyButtons()?\"4px\":\"0 5px 5px 5px\",backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#007bff\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#fff\",border:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"transparent\",fontSize:((_button$ButtonPropert4=button.ButtonProperties)===null||_button$ButtonPropert4===void 0?void 0:_button$ButtonPropert4.FontSize)||\"15px\",width:((_button$ButtonPropert5=button.ButtonProperties)===null||_button$ButtonPropert5===void 0?void 0:_button$ButtonPropert5.Width)||\"auto\",padding:hasOnlyButtons()?\"var(--button-padding) !important\":\"4px 8px\",lineHeight:hasOnlyButtons()?\"var(--button-lineheight)\":\"normal\",textTransform:\"none\",borderRadius:((_button$ButtonPropert6=button.ButtonProperties)===null||_button$ButtonPropert6===void 0?void 0:_button$ButtonPropert6.BorderRadius)||\"8px\",minWidth:hasOnlyButtons()?\"fit-content\":undefined,boxShadow:\"none !important\",// Remove box shadow in normal state\n\"&:hover\":{backgroundColor:((_button$ButtonPropert7=button.ButtonProperties)===null||_button$ButtonPropert7===void 0?void 0:_button$ButtonPropert7.ButtonBackgroundColor)||\"#007bff\",// Keep the same background color on hover\nopacity:0.9,// Slightly reduce opacity on hover for visual feedback\nboxShadow:\"none !important\"// Remove box shadow in hover state\n}},children:button.ButtonName},index);})},containerId);})]})})},`scrollbar-${needsScrolling}`),enableProgress&&totalSteps>1&&selectedTemplate===\"Tour\"&&/*#__PURE__*/_jsx(Box,{children:renderProgress()}),\" \"]})}),/*#__PURE__*/_jsx(\"style\",{children:`\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `})]});};export default HotspotPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "HotspotPreview", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "dynamicWidth", "setDynamicWidth", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "scrollToElement", "element", "rect", "getBoundingClientRect", "isElementVisible", "top", "left", "bottom", "window", "innerHeight", "right", "innerWidth", "console", "log", "scrollIntoView", "behavior", "block", "inline", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "calculatePopupPosition", "elementRect", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "dynamicOffsetX", "dynamicOffsetY", "scrollY", "scrollX", "undefined", "position", "_guideStep", "style", "backgroundColor", "setOverlayValue", "handleContinue", "_savedGuideData$Guide3", "nextStep", "nextStepData", "setTimeout", "nextElement", "renderNextPopup", "_savedGuideData$Guide4", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "_savedGuideData$Guide15", "prevStep", "prevStepData", "prevElement", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "length", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "calculateOptimalWidth", "_contentRef$current", "_buttonContainerRef$c", "<PERSON><PERSON><PERSON>", "contentWidth", "current", "scrollWidth", "buttonWidth", "optimalWidth", "Math", "max", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "min", "requestAnimationFrame", "newWidth", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "handleResize", "_toolTipGuideMetaData2", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "width", "sectionHeight", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "firstStepElementPath", "firstStepElement", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide18", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide22", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_savedGuideData$Guide25", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "height", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "arguments", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "currentHotspotSize", "abs", "id", "body", "cursor", "error", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide37", "_savedGuideData$Guide38", "_savedGuideData$Guide39", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "imageProp", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tif (!xpath) return null;\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\r\n\r\n\t// Helper function to scroll to element\r\n\tconst scrollToElement = (element: HTMLElement) => {\r\n\t\t// Check if element is visible in viewport\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\tconst isElementVisible = (\r\n\t\t\trect.top >= 0 &&\r\n\t\t\trect.left >= 0 &&\r\n\t\t\trect.bottom <= window.innerHeight &&\r\n\t\t\trect.right <= window.innerWidth\r\n\t\t);\r\n\r\n\t\tif (!isElementVisible) {\r\n\t\t\tconsole.log(\"🔄 Hotspot: Element not visible, scrolling to element\");\r\n\t\t\telement.scrollIntoView({\r\n\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\tblock: 'center',\r\n\t\t\t\tinline: 'nearest'\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\tconsole.log(\"✅ Hotspot: Element already visible\");\r\n\t\t}\r\n\t};\r\n\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tconst nextStep = currentStep + 1;\r\n\t\t\t\tsetCurrentStep(nextStep);\r\n\r\n\t\t\t\t// Auto-scroll to next step element if it exists\r\n\t\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[nextStep - 1];\r\n\t\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\t\tconsole.log(\"🎯 Hotspot: Navigating to next step\", nextStep, \"with ElementPath:\", nextStepData.ElementPath);\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst nextElement = getElementByXPath(nextStepData.ElementPath || \"\");\r\n\t\t\t\t\t\tif (nextElement) {\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot: Found next step element, scrolling into view\");\r\n\t\t\t\t\t\t\tscrollToElement(nextElement);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Hotspot: Next step element not found for ElementPath:\", nextStepData.ElementPath);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconst nextStep = currentStep + 1;\r\n\t\t\tsetCurrentStep(nextStep);\r\n\r\n\t\t\t// Auto-scroll to next step element for Tour template\r\n\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[nextStep - 1];\r\n\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\tconsole.log(\"🎯 Hotspot Tour: Navigating to next step\", nextStep, \"with ElementPath:\", nextStepData.ElementPath);\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst nextElement = getElementByXPath(nextStepData.ElementPath || \"\");\r\n\t\t\t\t\tif (nextElement) {\r\n\t\t\t\t\t\tconsole.log(\"✅ Hotspot Tour: Found next step element, scrolling into view\");\r\n\t\t\t\t\t\tscrollToElement(nextElement);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(\"❌ Hotspot Tour: Next step element not found for ElementPath:\", nextStepData.ElementPath);\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 100);\r\n\t\t\t}\r\n\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tconst prevStep = currentStep - 1;\r\n\t\t\tsetCurrentStep(prevStep);\r\n\r\n\t\t\t// Auto-scroll to previous step element if it exists\r\n\t\t\tconst prevStepData = savedGuideData?.GuideStep?.[prevStep - 1];\r\n\t\t\tif (prevStepData?.ElementPath) {\r\n\t\t\t\tconsole.log(\"🔙 Hotspot: Navigating to previous step\", prevStep, \"with ElementPath:\", prevStepData.ElementPath);\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconst prevElement = getElementByXPath(prevStepData.ElementPath || \"\");\r\n\t\t\t\t\tif (prevElement) {\r\n\t\t\t\t\t\tconsole.log(\"✅ Hotspot: Found previous step element, scrolling into view\");\r\n\t\t\t\t\t\tscrollToElement(prevElement);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(\"❌ Hotspot: Previous step element not found for ElementPath:\", prevStepData.ElementPath);\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 100);\r\n\t\t\t}\r\n\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\tconst calculateOptimalWidth = () => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\treturn `${canvasProperties.Width}px`;\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, use auto width\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\treturn \"auto\";\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = 800; // Maximum width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn `${finalWidth}px`;\r\n\t};\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newWidth = calculateOptimalWidth();\r\n\t\t\tsetDynamicWidth(newWidth);\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep]);\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t\t  canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"800px\",\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t   canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"300px\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tconsole.log(\"🔄 Hotspot: Restarting tour to first step\");\r\n\t\t\t\tsetCurrentStep(1);\r\n\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst firstStepElementPath = savedGuideData.GuideStep[0].ElementPath || \"\";\r\n\t\t\t\t\t\tconst firstStepElement = getElementByXPath(firstStepElementPath);\r\n\r\n\t\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot: Found first step element, scrolling into view\");\r\n\t\t\t\t\t\t\tscrollToElement(firstStepElement);\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Hotspot: First step element not found\");\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top +10+ (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left +10+ parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,CAAEC,QAAQ,CAAEC,MAAM,KAAQ,OAAO,CAC1D,OAASC,GAAG,CAAEC,MAAM,CAAEC,UAAU,CAAEC,cAAc,CAAEC,aAAa,CAAEC,OAAO,CAAiBC,UAAU,KAAQ,eAAe,CAE1H,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CACrE;AACA,MAAO,CAAAC,gBAAgB,KAAM,yBAAyB,CACtD,MAAO,6CAA6C,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBA6GrD,KAAM,CAAAC,cAAoC,CAAGC,IAAA,EA8BvC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,IA9BwC,CAC1CC,QAAQ,CACRC,SAAS,CACTC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cAAc,CACdC,iBAAiB,CACjBC,kBAAkB,CAClBC,kBAAkB,CAClBC,kBAAkB,CACnBC,kBAEH,CAAC,CAAAvC,IAAA,CACA,KAAM,CACLwC,cAAc,CACdC,gBAAgB,CAChBC,oBAAoB,CACpBC,eAAe,CACfC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,cAAc,CACdC,WAAW,CACXC,gBAAgB,CAChBC,oBAAoB,CACpBC,oBAAoB,CACpBC,cAAc,CACdC,aACD,CAAC,CAAG9D,cAAc,CAAE+D,KAAkB,EAAKA,KAAK,CAAC,CACjD,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG3E,QAAQ,CAAqB,IAAI,CAAC,CAC5E;AACA;AACA,KAAM,CAAC4E,aAAa,CAAEC,gBAAgB,CAAC,CAAG7E,QAAQ,CAAuC,IAAI,CAAC,CAC9F,KAAM,CAAC8E,YAAY,CAAEC,eAAe,CAAC,CAAG/E,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAACgF,WAAW,CAAEC,cAAc,CAAC,CAAGjF,QAAQ,CAAS,EAAE,CAAC,CAAE;AAC5D,KAAM,CAAAkF,UAAU,CAAGjF,MAAM,CAAiB,IAAI,CAAC,CAC/C,KAAM,CAAAkF,kBAAkB,CAAGlF,MAAM,CAAiB,IAAI,CAAC,CACvD,GAAI,CAAAmF,OAAY,CAChB,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAyB,CAChE,GAAI,CAACA,KAAK,CAAE,MAAO,KAAI,CACvB,KAAM,CAAAC,MAAM,CAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,CAAEE,QAAQ,CAAE,IAAI,CAAEE,WAAW,CAACC,uBAAuB,CAAE,IAAI,CAAC,CAClG,KAAM,CAAAC,IAAI,CAAGL,MAAM,CAACM,eAAe,CACnC,GAAID,IAAI,WAAY,CAAAE,WAAW,CAAE,CAChC,MAAO,CAAAF,IAAI,CACZ,CAAC,IAAM,IAAIA,IAAI,SAAJA,IAAI,WAAJA,IAAI,CAAEG,aAAa,CAAE,CAC/B,MAAO,CAAAH,IAAI,CAACG,aAAa,CAAE;AAC5B,CAAC,IAAM,CACN,MAAO,KAAI,CACZ,CACD,CAAC,CAID;AACA,KAAM,CAAAC,eAAe,CAAIC,OAAoB,EAAK,CACjD;AACA,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAAC,gBAAgB,CACrBF,IAAI,CAACG,GAAG,EAAI,CAAC,EACbH,IAAI,CAACI,IAAI,EAAI,CAAC,EACdJ,IAAI,CAACK,MAAM,EAAIC,MAAM,CAACC,WAAW,EACjCP,IAAI,CAACQ,KAAK,EAAIF,MAAM,CAACG,UACrB,CAED,GAAI,CAACP,gBAAgB,CAAE,CACtBQ,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC,CACpEZ,OAAO,CAACa,cAAc,CAAC,CACtBC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,SACT,CAAC,CAAC,CACH,CAAC,IAAM,CACNL,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC,CAClD,CACD,CAAC,CAED,GAAI,CAAAvB,KAAU,CACd,GAAIjC,cAAc,CAAEiC,KAAK,CAAGjC,cAAc,SAAdA,cAAc,kBAAAjC,qBAAA,CAAdiC,cAAc,CAAE6D,SAAS,UAAA9F,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4B,CAAC,CAAC,UAAAC,sBAAA,iBAA9BA,sBAAA,CAAgC8F,WAAW,CACvE,KAAM,CAAAC,kBAAkB,CAAI9B,KAAyB,EAAK,CACzD,KAAM,CAAAW,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,EAAI,EAAE,CAAC,CAC9C,GAAIW,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,MAAO,CACNE,GAAG,CAAEH,IAAI,CAACG,GAAG,CAAE;AACfC,IAAI,CAAEJ,IAAI,CAACI,IAAM;AAClB,CAAC,CACF,CACA,MAAO,KAAI,CACZ,CAAC,CACC;AACA,KAAM,CAACe,cAAc,CAAEC,iBAAiB,CAAC,CAAGtH,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAAuH,YAAY,CAAGtH,MAAM,CAAM,IAAI,CAAC,CACxC;AACA,KAAM,CAAAuH,sBAAsB,CAAGA,CAACC,WAAoB,CAAEzC,WAAmB,CAAE0C,OAAe,CAAEC,OAAe,GAAK,CAC/G,KAAM,CAAAC,WAAW,CAAGH,WAAW,CAACI,CAAC,CAAGH,OAAO,CAC3C,KAAM,CAAAI,UAAU,CAAGL,WAAW,CAACM,CAAC,CAAGJ,OAAO,CAE1C;AACA,KAAM,CAAAK,cAAc,CAAGhD,WAAW,CAAG,CAAC,CAAE;AACxC,KAAM,CAAAiD,cAAc,CAAGjD,WAAW,CAAG,EAAE,CAAE;AAEzC,MAAO,CACNqB,GAAG,CAAEyB,UAAU,CAAGtB,MAAM,CAAC0B,OAAO,CAAGD,cAAc,CACjD3B,IAAI,CAAEsB,WAAW,CAAGpB,MAAM,CAAC2B,OAAO,CAAGH,cACtC,CAAC,CACF,CAAC,CACDjI,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBwB,GAAG,CAAEH,IAAI,CAACG,GAAG,CAAGG,MAAM,CAAC0B,OAAO,CAAE;AAChC5B,IAAI,CAAEJ,IAAI,CAACI,IAAI,CAAGE,MAAM,CAAC2B,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAAC7C,KAAK,CAAC,CAAC,CACXvF,SAAS,CAAC,IAAM,CACf,GAAI,MAAO,CAAAyG,MAAM,GAAK4B,SAAS,CAAE,CAChC,KAAM,CAAAC,QAAQ,CAAGjB,kBAAkB,CAAC9B,KAAK,EAAI,EAAE,CAAC,CAChD,GAAI+C,QAAQ,CAAE,CACbxD,gBAAgB,CAACwD,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAAC/C,KAAK,CAAC,CAAC,CACXvF,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkG,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC;AACA,GAAIW,OAAO,CAAE,CACb,CACD,CAAC,CAAE,CAAC5C,cAAc,CAAC,CAAC,CAEpBtD,SAAS,CAAC,IAAM,KAAAuI,UAAA,CACf,KAAM,CAAArC,OAAO,CAAGZ,iBAAiB,CAACrD,SAAS,SAATA,SAAS,kBAAAsG,UAAA,CAATtG,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAA8F,UAAA,iBAA5BA,UAAA,CAA8BnB,WAAW,CAAC,CAC5ExC,gBAAgB,CAACsB,OAAO,CAAC,CACzB,GAAIA,OAAO,CAAE,CACZA,OAAO,CAACsC,KAAK,CAACC,eAAe,CAAG,gBAAgB,CAEhD;AACA,KAAM,CAAAtC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CtB,gBAAgB,CAAC,CAChBwB,GAAG,CAAEH,IAAI,CAACG,GAAG,CAAGG,MAAM,CAAC0B,OAAO,CAC9B5B,IAAI,CAAEJ,IAAI,CAACI,IAAI,CAAGE,MAAM,CAAC2B,OAC1B,CAAC,CAAC,CACH,CACD,CAAC,CAAE,CAACnG,SAAS,CAAEQ,WAAW,CAAC,CAAC,CAE5B;AACA;AACA,KAAM,EAAGiG,eAAe,CAAC,CAAGzI,QAAQ,CAAC,KAAK,CAAC,CAC3C,KAAM,CAAA0I,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI9E,gBAAgB,GAAK,MAAM,CAAE,CAChC,GAAIpB,WAAW,CAAGC,UAAU,CAAE,KAAAkG,sBAAA,CAC7B,KAAM,CAAAC,QAAQ,CAAGpG,WAAW,CAAG,CAAC,CAChCmB,cAAc,CAACiF,QAAQ,CAAC,CAExB;AACA,KAAM,CAAAC,YAAY,CAAGxF,cAAc,SAAdA,cAAc,kBAAAsF,sBAAA,CAAdtF,cAAc,CAAE6D,SAAS,UAAAyB,sBAAA,iBAAzBA,sBAAA,CAA4BC,QAAQ,CAAG,CAAC,CAAC,CAC9D,GAAIC,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE1B,WAAW,CAAE,CAC9BP,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAE+B,QAAQ,CAAE,mBAAmB,CAAEC,YAAY,CAAC1B,WAAW,CAAC,CAE3G2B,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAC,WAAW,CAAG1D,iBAAiB,CAACwD,YAAY,CAAC1B,WAAW,EAAI,EAAE,CAAC,CACrE,GAAI4B,WAAW,CAAE,CAChBnC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CACtEb,eAAe,CAAC+C,WAAW,CAAC,CAC7B,CAAC,IAAM,CACNnC,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAEgC,YAAY,CAAC1B,WAAW,CAAC,CACjG,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CAEA7E,UAAU,CAAC,CAAC,CACZ0G,eAAe,CAACxG,WAAW,CAAGC,UAAU,CAAC,CAC1C,CACD,CAAC,IAAM,KAAAwG,sBAAA,CACN,KAAM,CAAAL,QAAQ,CAAGpG,WAAW,CAAG,CAAC,CAChCmB,cAAc,CAACiF,QAAQ,CAAC,CAExB;AACA,KAAM,CAAAC,YAAY,CAAGxF,cAAc,SAAdA,cAAc,kBAAA4F,sBAAA,CAAd5F,cAAc,CAAE6D,SAAS,UAAA+B,sBAAA,iBAAzBA,sBAAA,CAA4BL,QAAQ,CAAG,CAAC,CAAC,CAC9D,GAAIC,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE1B,WAAW,CAAE,CAC9BP,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAE+B,QAAQ,CAAE,mBAAmB,CAAEC,YAAY,CAAC1B,WAAW,CAAC,CAEhH2B,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAC,WAAW,CAAG1D,iBAAiB,CAACwD,YAAY,CAAC1B,WAAW,EAAI,EAAE,CAAC,CACrE,GAAI4B,WAAW,CAAE,CAChBnC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC,CAC3Eb,eAAe,CAAC+C,WAAW,CAAC,CAC7B,CAAC,IAAM,CACNnC,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAEgC,YAAY,CAAC1B,WAAW,CAAC,CACtG,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CAEA,KAAM,CAAA+B,eAAe,CAAG1D,QAAQ,CAAC2D,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAACX,KAAK,CAACa,OAAO,CAAG,MAAM,CACtCF,eAAe,CAACG,MAAM,CAAC,CAAC,CACzB,CACD,CACD,CAAC,CAED,KAAM,CAAAL,eAAe,CAAIM,qBAA8B,EAAK,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC3D,MAAO,CAAAV,qBAAqB,cAC3BzI,IAAA,CAACK,cAAc,EACduC,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCH,kBAAkB,CAAEA,kBAAmB,CACvCC,kBAAkB,CAAEA,kBAAmB,CACvCzB,QAAQ,CAAEA,QAAS,CACnBsB,cAAc,CAAEA,cAAe,CAC/BrB,SAAS,CAAEA,SAAU,CACrBI,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAE4H,cAAe,CAC3B3H,UAAU,CAAEoG,cAAe,CAC3BzG,KAAK,CAAEA,KAAM,CACbC,IAAI,CAAEA,IAAK,CACXC,QAAQ,CAAEA,QAAS,CACnBK,WAAW,CAAEA,WAAW,CAAG,CAAE,CAC7BC,UAAU,CAAEA,UAAW,CACvBC,eAAe,CAAEA,eAAgB,CACjCC,QAAQ,CAAEA,QAAS,CACnBC,mBAAmB,CAAES,cAAc,SAAdA,cAAc,kBAAAkG,sBAAA,CAAdlG,cAAc,CAAE6D,SAAS,UAAAqC,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B/G,WAAW,CAAC,UAAAgH,sBAAA,iBAAxCA,sBAAA,CAA0CU,mBAAoB,CACnFrH,eAAe,CAAEQ,cAAc,SAAdA,cAAc,kBAAAoG,sBAAA,CAAdpG,cAAc,CAAE6D,SAAS,UAAAuC,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BjH,WAAW,CAAC,UAAAkH,sBAAA,iBAAxCA,sBAAA,CAA0CS,eAAgB,CAC3ErH,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAAsG,sBAAA,CAAdtG,cAAc,CAAE6D,SAAS,UAAAyC,sBAAA,kBAAAC,uBAAA,CAAzBD,sBAAA,CAA4BnH,WAAW,CAAC,UAAAoH,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CQ,aAAa,UAAAP,uBAAA,kBAAAC,uBAAA,CAAvDD,uBAAA,CAAyDQ,GAAG,CAAEC,OAAY,EACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,GAAM,CAC3C,GAAGA,MAAM,CACTC,WAAW,CAAEH,OAAO,CAACI,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAAZ,uBAAA,iBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,CAAEC,IAAS,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACvE,CACD9H,eAAe,CAAEA,eAAgB,CACjCC,gBAAgB,CAAEA,gBAAiB,CACnCC,WAAW,CAAEA,WAAY,CACzBG,YAAY,CAAEA,YAAa,CAC3BE,iBAAiB,CAAE,CAAAD,cAAc,SAAdA,cAAc,kBAAA0G,uBAAA,CAAd1G,cAAc,CAAE6D,SAAS,UAAA6C,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BvH,WAAW,CAAG,CAAC,CAAC,UAAAwH,uBAAA,iBAA5CA,uBAAA,CAA8Ce,OAAO,GAAI,CAAC,CAAE,CAC/E,CAAC,CACC,IAAI,CACT,CAAC,CAED,KAAM,CAAAd,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAIzH,WAAW,CAAG,CAAC,CAAE,KAAAwI,uBAAA,CACpB,KAAM,CAAAC,QAAQ,CAAGzI,WAAW,CAAG,CAAC,CAChCmB,cAAc,CAACsH,QAAQ,CAAC,CAExB;AACA,KAAM,CAAAC,YAAY,CAAG7H,cAAc,SAAdA,cAAc,kBAAA2H,uBAAA,CAAd3H,cAAc,CAAE6D,SAAS,UAAA8D,uBAAA,iBAAzBA,uBAAA,CAA4BC,QAAQ,CAAG,CAAC,CAAC,CAC9D,GAAIC,YAAY,SAAZA,YAAY,WAAZA,YAAY,CAAE/D,WAAW,CAAE,CAC9BP,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAEoE,QAAQ,CAAE,mBAAmB,CAAEC,YAAY,CAAC/D,WAAW,CAAC,CAE/G2B,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAqC,WAAW,CAAG9F,iBAAiB,CAAC6F,YAAY,CAAC/D,WAAW,EAAI,EAAE,CAAC,CACrE,GAAIgE,WAAW,CAAE,CAChBvE,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC,CAC1Eb,eAAe,CAACmF,WAAW,CAAC,CAC7B,CAAC,IAAM,CACNvE,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAEqE,YAAY,CAAC/D,WAAW,CAAC,CACrG,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CAEA9E,UAAU,CAAC,CAAC,CACb,CACD,CAAC,CACDtC,SAAS,CAAC,IAAM,CACf,GAAIqD,YAAY,CAAE,CACjBqF,eAAe,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACNA,eAAe,CAAC,KAAK,CAAC,CACvB,CACD,CAAC,CAAE,CAACrF,YAAY,CAAC,CAAC,CAClB;AACA,KAAM,CAAAgI,4BAA4B,CACjC/C,QAAgB,EACqD,CACrE,OAAQA,QAAQ,EACf,IAAK,UAAU,CACd,MAAO,CACNgD,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,MAAO,CAAC,CACrDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,WAAW,CACf,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACtDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,OAAQ,CACzD,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,YAAY,CAChB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAC,CACvDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,aAAa,CACjB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAAC,CACxDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAC5D,CAAC,CACF,IAAK,eAAe,CACnB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACF,IAAK,cAAc,CAClB,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAC,CACzDC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAO,CAC3D,CAAC,CACF,QACC,MAAO,CACNF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAC,CAC1DC,eAAe,CAAE,CAAEF,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAC7D,CAAC,CACH,CACD,CAAC,CAED,KAAM,CAAEF,YAAY,CAAEG,eAAgB,CAAC,CAAGJ,4BAA4B,CAAC,CAAApI,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyI,QAAQ,GAAI,eAAe,CAAC,CAErH,KAAM,CAAAC,SAAS,CAAG,CACjBC,UAAU,CAAE/I,mBAAmB,SAAnBA,mBAAmB,YAAAtB,qBAAA,CAAnBsB,mBAAmB,CAAEgJ,cAAc,UAAAtK,qBAAA,WAAnCA,qBAAA,CAAqCuK,IAAI,CAAG,MAAM,CAAG,QAAQ,CACzEC,SAAS,CAAElJ,mBAAmB,SAAnBA,mBAAmB,YAAArB,sBAAA,CAAnBqB,mBAAmB,CAAEgJ,cAAc,UAAArK,sBAAA,WAAnCA,sBAAA,CAAqCwK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAC5EC,KAAK,CAAE,CAAApJ,mBAAmB,SAAnBA,mBAAmB,kBAAApB,sBAAA,CAAnBoB,mBAAmB,CAAEgJ,cAAc,UAAApK,sBAAA,iBAAnCA,sBAAA,CAAqCyK,SAAS,GAAI,SAAS,CAClEC,SAAS,CAAE,CAAAtJ,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEuJ,SAAS,GAAI,MAC9C,CAAC,CAED;AAEA,KAAM,CAAAC,iBAAiB,CAAIC,OAAe,EAAK,CAC9C;AACA,MAAO,CACNC,MAAM,CAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,CAAE,CAACC,MAAM,CAAEC,EAAE,CAAEC,EAAE,CAAEC,EAAE,GAAK,CACtF,MAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE,CAC1C,CAAC,CACF,CAAC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,QAAQ,CAAGhK,eAAe,EAAIA,eAAe,CAACiK,MAAM,CAAG,CAAC,EAC7DjK,eAAe,CAACkK,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGxK,mBAAmB,EAAIA,mBAAmB,CAACkK,MAAM,CAAG,CAAC,EACpElK,mBAAmB,CAACmK,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAG1K,YAAY,EAAIA,YAAY,CAACgK,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAAU,UAAU,EAAI,CAACX,QAAQ,EAAI,CAACO,OAAO,CAC3C,CAAC,CAED;AACA,KAAM,CAAAK,WAAW,CAAGA,CAAA,GAAM,CACzB,KAAM,CAAAZ,QAAQ,CAAGhK,eAAe,EAAIA,eAAe,CAACiK,MAAM,CAAG,CAAC,EAC7DjK,eAAe,CAACkK,IAAI,CAAEC,IAAS,EAC9BA,IAAI,CAACC,WAAW,EAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,EAAKA,GAAG,CAACC,GAAG,CAChE,CAAC,CAEF,KAAM,CAAAC,OAAO,CAAGxK,mBAAmB,EAAIA,mBAAmB,CAACkK,MAAM,CAAG,CAAC,EACpElK,mBAAmB,CAACmK,IAAI,CAAEM,KAAU,EAAKA,KAAK,CAACC,IAAI,EAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,GAAK,EAAE,CAAC,CAEjF,KAAM,CAAAC,UAAU,CAAG1K,YAAY,EAAIA,YAAY,CAACgK,MAAM,CAAG,CAAC,CAE1D,MAAO,CAAAM,OAAO,EAAI,CAACP,QAAQ,EAAI,CAACW,UAAU,CAC3C,CAAC,CAED;AACA,KAAM,CAAAE,qBAAqB,CAAGA,CAAA,GAAM,KAAAC,mBAAA,CAAAC,qBAAA,CACnC;AACA,GAAI5K,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE6K,KAAK,EAAI,CAACjB,cAAc,CAAC,CAAC,EAAI,CAACa,WAAW,CAAC,CAAC,CAAE,CACnE,MAAO,GAAGzK,gBAAgB,CAAC6K,KAAK,IAAI,CACrC,CAEA;AACA,GAAIjB,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAE,CACtC,MAAO,MAAM,CACd,CAEA;AACA,KAAM,CAAAK,YAAY,CAAG,EAAAH,mBAAA,CAAAzI,UAAU,CAAC6I,OAAO,UAAAJ,mBAAA,iBAAlBA,mBAAA,CAAoBK,WAAW,GAAI,CAAC,CACzD,KAAM,CAAAC,WAAW,CAAG,EAAAL,qBAAA,CAAAzI,kBAAkB,CAAC4I,OAAO,UAAAH,qBAAA,iBAA1BA,qBAAA,CAA4BI,WAAW,GAAI,CAAC,CAEhE;AACA,KAAM,CAAAE,YAAY,CAAGC,IAAI,CAACC,GAAG,CAACN,YAAY,CAAEG,WAAW,CAAC,CAExD;AACA,KAAM,CAAAI,WAAW,CAAGH,YAAY,CAAG,EAAE,CAAE;AAEvC;AACA,KAAM,CAAAI,QAAQ,CAAG,GAAG,CAAE;AACtB,KAAM,CAAAC,QAAQ,CAAG,GAAG,CAAE;AAEtB,KAAM,CAAAC,UAAU,CAAGL,IAAI,CAACC,GAAG,CAACE,QAAQ,CAAEH,IAAI,CAACM,GAAG,CAACJ,WAAW,CAAEE,QAAQ,CAAC,CAAC,CAEtE,MAAO,GAAGC,UAAU,IAAI,CACzB,CAAC,CAED;AACAzO,SAAS,CAAC,IAAM,CACf;AACA2O,qBAAqB,CAAC,IAAM,CAC3B,KAAM,CAAAC,QAAQ,CAAGjB,qBAAqB,CAAC,CAAC,CACxC3I,eAAe,CAAC4J,QAAQ,CAAC,CAC1B,CAAC,CAAC,CACH,CAAC,CAAE,CAAC/L,mBAAmB,CAAEC,eAAe,CAAEC,YAAY,CAAEN,WAAW,CAAC,CAAC,CAErE;AACAzC,SAAS,CAAC,IAAM,CACf,GAAIuF,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAiB,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,KAAA2I,qBAAA,CACZ,KAAM,CAAA1I,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA0I,eAAe,EAAAD,qBAAA,CAAG/K,oBAAoB,CAAC,CAAC,CAAC,UAAA+K,qBAAA,iBAAvBA,qBAAA,CAAyBE,QAAQ,CACzD,KAAM,CAAApH,OAAO,CAAGqH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAArH,OAAO,CAAGoH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAG1H,sBAAsB,CAACtB,IAAI,CAAElB,WAAW,CAAE0C,OAAO,CAAEC,OAAO,CAAC,CAC5E9C,gBAAgB,CAACqK,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAAE,CAAClK,WAAW,CAAEM,KAAK,CAAEzB,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAoP,YAAY,CAAGA,CAAA,GAAM,CAC1B,GAAI7J,KAAK,EAAIN,WAAW,CAAE,CACzB,KAAM,CAAAiB,OAAO,CAAGZ,iBAAiB,CAACC,KAAK,CAAC,CACxC,GAAIW,OAAO,CAAE,KAAAmJ,sBAAA,CACZ,KAAM,CAAAlJ,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5C,KAAM,CAAA0I,eAAe,EAAAO,sBAAA,CAAGvL,oBAAoB,CAAC,CAAC,CAAC,UAAAuL,sBAAA,iBAAvBA,sBAAA,CAAyBN,QAAQ,CACzD,KAAM,CAAApH,OAAO,CAAGqH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEG,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAArH,OAAO,CAAGoH,UAAU,CAAC,CAAAF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEI,SAAS,GAAI,GAAG,CAAC,CAE7D,KAAM,CAAAC,QAAQ,CAAG1H,sBAAsB,CAACtB,IAAI,CAAElB,WAAW,CAAE0C,OAAO,CAAEC,OAAO,CAAC,CAC5E9C,gBAAgB,CAACqK,QAAQ,CAAC,CAC3B,CACD,CACD,CAAC,CAED1I,MAAM,CAAC6I,gBAAgB,CAAC,QAAQ,CAAEF,YAAY,CAAC,CAC/C,MAAO,IAAM3I,MAAM,CAAC8I,mBAAmB,CAAC,QAAQ,CAAEH,YAAY,CAAC,CAChE,CAAC,CAAE,CAAC7J,KAAK,CAAEN,WAAW,CAAEnB,oBAAoB,CAAC,CAAC,CAE9C,KAAM,CAAA0L,cAAc,CAAGzM,YAAY,CAAC6H,MAAM,CAAC,CAACC,GAAQ,CAAEJ,MAAW,GAAK,CACrE,KAAM,CAAAgF,WAAW,CAAGhF,MAAM,CAACC,WAAW,EAAI,SAAS,CAAE;AACrD,GAAI,CAACG,GAAG,CAAC4E,WAAW,CAAC,CAAE,CACtB5E,GAAG,CAAC4E,WAAW,CAAC,CAAG,EAAE,CACtB,CACA5E,GAAG,CAAC4E,WAAW,CAAC,CAACC,IAAI,CAACjF,MAAM,CAAC,CAC7B,MAAO,CAAAI,GAAG,CACX,CAAC,CAAE,CAAC,CAAC,CAAC,CAEN,KAAM,CAAA8E,WAAW,CAAG,CACnBrH,QAAQ,CAAE,CAAArF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyI,QAAQ,GAAI,eAAe,CACvDkE,YAAY,CAAE,CAAA3M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE4M,MAAM,GAAI,KAAK,CAC/CC,WAAW,CAAE,CAAA7M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAE8M,UAAU,GAAI,KAAK,CAClDC,WAAW,CAAE,CAAA/M,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEgN,WAAW,GAAI,OAAO,CACrDC,WAAW,CAAE,OAAO,CACpBzH,eAAe,CAAE,CAAAxF,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkN,eAAe,GAAI,OAAO,CAC7D3B,QAAQ,CAAG3B,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC7D3I,YAAY,CAAG,GAAGA,YAAY,aAAa,CAC3C9B,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE6K,KAAK,CAAG,GAAG7K,gBAAgB,CAAC6K,KAAK,eAAe,CAAG,OAAO,CAChFsC,KAAK,CAAGvD,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAI,iBAAiB,CAC1D3I,YAAY,CAAG,GAAGA,YAAY,aAAa,CAC3C9B,gBAAgB,SAAhBA,gBAAgB,WAAhBA,gBAAgB,CAAE6K,KAAK,CAAG,GAAG7K,gBAAgB,CAAC6K,KAAK,eAAe,CAAG,OAC1E,CAAC,CACD,KAAM,CAAAuC,aAAa,CAAG,EAAA3O,gBAAA,CAAAoB,eAAe,CAACL,WAAW,CAAG,CAAC,CAAC,UAAAf,gBAAA,kBAAAC,qBAAA,CAAhCD,gBAAA,CAAkCwL,WAAW,UAAAvL,qBAAA,kBAAAC,sBAAA,CAA7CD,qBAAA,CAAgDc,WAAW,CAAG,CAAC,CAAC,UAAAb,sBAAA,iBAAhEA,sBAAA,CAAkE0O,aAAa,GAAI,MAAM,CAC/G,KAAM,CAAAC,kBAAkB,CAAIC,MAAW,EAAK,CAC3C,GAAIA,MAAM,CAACC,MAAM,GAAK,UAAU,EAAID,MAAM,CAACC,MAAM,GAAK,MAAM,EAAID,MAAM,CAACC,MAAM,GAAK,SAAS,CAAE,CAC5F,KAAM,CAAAC,SAAS,CAAGF,MAAM,CAACG,SAAS,CAClC,GAAIH,MAAM,CAACI,WAAW,GAAK,UAAU,CAAE,CACtC;AACAnK,MAAM,CAACoK,QAAQ,CAACC,IAAI,CAAGJ,SAAS,CACjC,CAAC,IAAM,CACN;AACAjK,MAAM,CAACsK,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAC,IAAM,CACN,GACCF,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACC,MAAM,EAAI,UAAU,EAC3BD,MAAM,CAACI,WAAW,EAAI,UAAU,EAChCJ,MAAM,CAACI,WAAW,EAAI,UAAU,CAC/B,CACD1G,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACNsG,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACC,MAAM,EAAI,MAAM,EACvBD,MAAM,CAACI,WAAW,EAAI,MAAM,EAC5BJ,MAAM,CAACI,WAAW,EAAI,MAAM,CAC3B,CACDjI,cAAc,CAAC,CAAC,CACjB,CAAC,IAAM,IACN6H,MAAM,CAACC,MAAM,EAAI,SAAS,EAC1BD,MAAM,CAACI,WAAW,EAAI,SAAS,CAC9B,KAAAI,uBAAA,CAAAC,uBAAA,CACD;AACApK,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC,CACxDlD,cAAc,CAAC,CAAC,CAAC,CAEjB;AACA,GAAIN,cAAc,SAAdA,cAAc,YAAA0N,uBAAA,CAAd1N,cAAc,CAAE6D,SAAS,UAAA6J,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,WAA9BA,uBAAA,CAAgC7J,WAAW,CAAE,CAChD2B,UAAU,CAAC,IAAM,CAChB,KAAM,CAAAmI,oBAAoB,CAAG5N,cAAc,CAAC6D,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,EAAI,EAAE,CAC1E,KAAM,CAAA+J,gBAAgB,CAAG7L,iBAAiB,CAAC4L,oBAAoB,CAAC,CAEhE,GAAIC,gBAAgB,CAAE,CACrBtK,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC,CACvEb,eAAe,CAACkL,gBAAgB,CAAC,CAClC,CAAC,IAAM,CACNtK,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC,CACvD,CACD,CAAC,CAAE,GAAG,CAAC,CACR,CACD,CACD,CACA4B,eAAe,CAAC,KAAK,CAAC,CACvB,CAAC,CACD1I,SAAS,CAAC,IAAM,KAAAoR,WAAA,CAAAC,mBAAA,CACf,GAAIpP,SAAS,SAATA,SAAS,YAAAmP,WAAA,CAATnP,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,UAAA2O,WAAA,YAAAC,mBAAA,CAA5BD,WAAA,CAA8BpG,OAAO,UAAAqG,mBAAA,WAArCA,mBAAA,CAAuCC,aAAa,CAAE,CACzD;AACAnN,cAAc,CAAC,IAAI,CAAC,CACrB,CACD,CAAC,CAAE,CAAClC,SAAS,SAATA,SAAS,iBAATA,SAAS,CAAGQ,WAAW,CAAG,CAAC,CAAC,CAAEA,WAAW,CAAE0B,cAAc,CAAC,CAAC,CAE/D;AACAnE,SAAS,CAAC,IAAM,CACf,GAAI0D,kBAAkB,CAAE,KAAA6N,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAA9C,eAAe,CAAGvK,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAyN,sBAAA,CAApBzN,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAA8O,sBAAA,WAAvCA,sBAAA,CAAyCxC,QAAQ,CAC5GjL,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsM,QAAQ,EAAAyC,sBAAA,CAC9C1N,oBAAoB,CAAC,CAAC,CAAC,UAAA0N,sBAAA,iBAAvBA,sBAAA,CAAyBzC,QAAQ,CACpC,KAAM,CAAA8C,WAAW,CAAGtN,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAAmO,uBAAA,CAAdnO,cAAc,CAAE6D,SAAS,UAAAsK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BhP,WAAW,CAAG,CAAC,CAAC,UAAAiP,uBAAA,iBAA5CA,uBAAA,CAA8C1G,OAAO,CACrD1H,cAAc,SAAdA,cAAc,kBAAAqO,uBAAA,CAAdrO,cAAc,CAAE6D,SAAS,UAAAwK,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgC5G,OAAO,CAE1C;AACA;AACA,GAAI8D,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEwC,aAAa,CAAE,CACnC;AACAnN,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACT,kBAAkB,CAAEI,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,GAAI2D,kBAAkB,CAAE,KAAAmO,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACvB;AACA,KAAM,CAAArD,eAAe,CAAGvK,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAgO,sBAAA,CAApBhO,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAqP,sBAAA,WAAvCA,sBAAA,CAAyC/C,QAAQ,CAC5GjL,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsM,QAAQ,EAAAgD,sBAAA,CAC9CjO,oBAAoB,CAAC,CAAC,CAAC,UAAAiO,sBAAA,iBAAvBA,sBAAA,CAAyBhD,QAAQ,CACpC,KAAM,CAAA8C,WAAW,CAAGtN,oBAAoB,GAAK,SAAS,CACnDjB,cAAc,SAAdA,cAAc,kBAAA0O,uBAAA,CAAd1O,cAAc,CAAE6D,SAAS,UAAA6K,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BvP,WAAW,CAAG,CAAC,CAAC,UAAAwP,uBAAA,iBAA5CA,uBAAA,CAA8CjH,OAAO,CACrD1H,cAAc,SAAdA,cAAc,kBAAA4O,uBAAA,CAAd5O,cAAc,CAAE6D,SAAS,UAAA+K,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCnH,OAAO,CAE1C;AACA,GAAI8D,eAAe,SAAfA,eAAe,WAAfA,eAAe,CAAEwC,aAAa,CAAE,CACnC;AACAnN,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACAA,cAAc,CAAC,KAAK,CAAC,CACtB,CACD,CACD,CAAC,CAAE,CAACR,kBAAkB,CAAEG,oBAAoB,CAAC,CAAC,CAE9C;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAoS,iBAAiB,CAAIC,CAAa,EAAK,CAC5C,KAAM,CAAAC,cAAc,CAAG7M,QAAQ,CAAC2D,cAAc,CAAC,cAAc,CAAC,CAE9D;AACA,GAAIkJ,cAAc,EAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,CAAE,CAChE,OACD,CAEA;AACA;AACD,CAAC,CAED/M,QAAQ,CAAC6J,gBAAgB,CAAC,OAAO,CAAE8C,iBAAiB,CAAC,CAErD,MAAO,IAAM,CACZ3M,QAAQ,CAAC8J,mBAAmB,CAAC,OAAO,CAAE6C,iBAAiB,CAAC,CACzD,CAAC,CACF,CAAC,CAAE,CAACtO,oBAAoB,CAAC,CAAC,CAC1B;AACA9D,SAAS,CAAC,IAAM,CACf,KAAM,CAAAyS,iBAAiB,CAAGA,CAAA,GAAM,CAC/B,GAAItN,UAAU,CAAC6I,OAAO,CAAE,CACvB;AACA7I,UAAU,CAAC6I,OAAO,CAACxF,KAAK,CAACkK,MAAM,CAAG,MAAM,CACxC,KAAM,CAAAC,aAAa,CAAGxN,UAAU,CAAC6I,OAAO,CAAC4E,YAAY,CACrD,KAAM,CAAAC,eAAe,CAAG,GAAG,CAAE;AAC7B,KAAM,CAAAC,YAAY,CAAGH,aAAa,CAAGE,eAAe,CAGpDtL,iBAAiB,CAACuL,YAAY,CAAC,CAE/B;AACA,GAAItL,YAAY,CAACwG,OAAO,CAAE,CACzB;AACA,GAAIxG,YAAY,CAACwG,OAAO,CAAC+E,YAAY,CAAE,CACtCvL,YAAY,CAACwG,OAAO,CAAC+E,YAAY,CAAC,CAAC,CACpC,CACA;AACAhK,UAAU,CAAC,IAAM,CAChB,GAAIvB,YAAY,CAACwG,OAAO,EAAIxG,YAAY,CAACwG,OAAO,CAAC+E,YAAY,CAAE,CAC9DvL,YAAY,CAACwG,OAAO,CAAC+E,YAAY,CAAC,CAAC,CACpC,CACD,CAAC,CAAE,EAAE,CAAC,CACP,CACD,CACD,CAAC,CAGDN,iBAAiB,CAAC,CAAC,CAGnB,KAAM,CAAAO,QAAQ,CAAG,CAChBjK,UAAU,CAAC0J,iBAAiB,CAAE,EAAE,CAAC,CACjC1J,UAAU,CAAC0J,iBAAiB,CAAE,GAAG,CAAC,CAClC1J,UAAU,CAAC0J,iBAAiB,CAAE,GAAG,CAAC,CAClC1J,UAAU,CAAC0J,iBAAiB,CAAE,GAAG,CAAC,CAClC,CAGD,GAAI,CAAAQ,cAAqC,CAAG,IAAI,CAChD,GAAI,CAAAC,gBAAyC,CAAG,IAAI,CAEpD,GAAI/N,UAAU,CAAC6I,OAAO,EAAIvH,MAAM,CAAC0M,cAAc,CAAE,CAChDF,cAAc,CAAG,GAAI,CAAAE,cAAc,CAAC,IAAM,CACzCpK,UAAU,CAAC0J,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFQ,cAAc,CAACG,OAAO,CAACjO,UAAU,CAAC6I,OAAO,CAAC,CAC3C,CAGA,GAAI7I,UAAU,CAAC6I,OAAO,EAAIvH,MAAM,CAAC4M,gBAAgB,CAAE,CAClDH,gBAAgB,CAAG,GAAI,CAAAG,gBAAgB,CAAC,IAAM,CAC7CtK,UAAU,CAAC0J,iBAAiB,CAAE,EAAE,CAAC,CAClC,CAAC,CAAC,CACFS,gBAAgB,CAACE,OAAO,CAACjO,UAAU,CAAC6I,OAAO,CAAE,CAC5CsF,SAAS,CAAE,IAAI,CACfC,OAAO,CAAE,IAAI,CACbC,UAAU,CAAE,IAAI,CAChBC,eAAe,CAAE,CAAC,OAAO,CAAE,OAAO,CACnC,CAAC,CAAC,CACH,CAEA,MAAO,IAAM,CACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC,CAC9B,GAAIV,cAAc,CAAE,CACnBA,cAAc,CAACW,UAAU,CAAC,CAAC,CAC5B,CACA,GAAIV,gBAAgB,CAAE,CACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC,CAC9B,CACD,CAAC,CACF,CAAC,CAAE,CAACnR,WAAW,CAAC,CAAC,CACjB;AACA;AAEA,QAAS,CAAAoR,YAAYA,CAACC,SAAiB,CAAE,CACxC,OAAQA,SAAS,EAChB,IAAK,OAAO,CACX,MAAO,YAAY,CACpB,IAAK,KAAK,CACT,MAAO,UAAU,CAClB,IAAK,QAAQ,CACb,QACC,MAAO,QAAQ,CACjB,CACD,CACA,KAAM,CAAAC,iBAAiB,CAAG,QAAAA,CAAA,CAAwC,IAAvC,CAAAzL,QAAgB,CAAA0L,SAAA,CAAAjH,MAAA,IAAAiH,SAAA,MAAA3L,SAAA,CAAA2L,SAAA,IAAG,eAAe,CAC5D,OAAQ1L,QAAQ,EACf,IAAK,aAAa,CACjB,MAAO,CAAEhC,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,cAAc,CAClB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,iBAAkB,CAAC,CAClC,IAAK,eAAe,CACnB,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,aAAa,CACjB,MAAO,CAAEA,GAAG,CAAElE,QAAQ,GAAK,EAAE,CAAG,gBAAgB,CAAG,gBAAiB,CAAC,CACtE,IAAK,cAAc,CAClB,MAAO,CAAEkE,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,UAAU,CACd,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,WAAW,CACf,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CACjC,IAAK,YAAY,CAChB,MAAO,CAAEA,GAAG,CAAE,eAAgB,CAAC,CAChC,QACC,MAAO,CAAEA,GAAG,CAAE,gBAAiB,CAAC,CAClC,CACD,CAAC,CAEA;AACD,KAAM,CAAA2N,kBAAkB,CAAGA,CAACC,QAAgB,CAAEpF,eAAoB,CAAE+C,WAAgB,GAAK,CACxF,GAAItN,oBAAoB,GAAK,SAAS,CAAE,CACvC;AACA,OAAQ2P,QAAQ,EACf,IAAK,gBAAgB,CACpB,MAAO,CAAArC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEsC,cAAc,IAAK9L,SAAS,CAAGwJ,WAAW,CAACsC,cAAc,CAAGrF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEqF,cAAc,CAChH,IAAK,eAAe,CACnB;AACA,MAAO,CAAAtC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEuC,4BAA4B,IAAK/L,SAAS,CAAGwJ,WAAW,CAACuC,4BAA4B,CAAGtF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsF,4BAA4B,CAC1J,IAAK,UAAU,CACd,MAAO,CAAAvC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEwC,QAAQ,IAAKhM,SAAS,CAAGwJ,WAAW,CAACwC,QAAQ,CAAGvF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEuF,QAAQ,CAC9F,IAAK,eAAe,CACnB,MAAO,CAAAxC,WAAW,SAAXA,WAAW,iBAAXA,WAAW,CAAEP,aAAa,IAAKjJ,SAAS,CAAGwJ,WAAW,CAACP,aAAa,CAAGxC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwC,aAAa,CAC7G,QACC,MAAO,CAAAxC,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGoF,QAAQ,CAAC,CACpC,CACD,CAAC,IAAM,CACN;AACA,GAAIA,QAAQ,GAAK,eAAe,CAAE,CACjC,MAAO,CAAApF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsF,4BAA4B,CACrD,CACA,MAAO,CAAAtF,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAGoF,QAAQ,CAAC,CACnC,CACD,CAAC,CAED,KAAM,CAAAI,kBAAkB,CAAGA,CAACjP,OAAY,CAAEyJ,eAAoB,CAAE+C,WAAgB,CAAEtL,IAAS,CAAED,GAAQ,GAAK,CACzGjB,OAAO,CAACmD,KAAK,CAACF,QAAQ,CAAG,UAAU,CACnCjD,OAAO,CAACmD,KAAK,CAACjC,IAAI,CAAG,GAAGA,IAAI,IAAI,CAChClB,OAAO,CAACmD,KAAK,CAAClC,GAAG,CAAG,GAAGA,GAAG,IAAI,CAC9BjB,OAAO,CAACmD,KAAK,CAAC4H,KAAK,CAAG,GAAGtB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyF,IAAI,IAAI,CAAE;AACpDlP,OAAO,CAACmD,KAAK,CAACkK,MAAM,CAAG,GAAG5D,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEyF,IAAI,IAAI,CACnDlP,OAAO,CAACmD,KAAK,CAACC,eAAe,CAAGqG,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE0F,KAAK,CACtDnP,OAAO,CAACmD,KAAK,CAACoH,YAAY,CAAG,KAAK,CAClCvK,OAAO,CAACmD,KAAK,CAACiM,MAAM,CAAG,iBAAiB,CAAE;AAC1CpP,OAAO,CAACmD,KAAK,CAACkM,UAAU,CAAG,MAAM,CACjCrP,OAAO,CAACmD,KAAK,CAACmM,aAAa,CAAG,MAAM,CAAE;AACtCtP,OAAO,CAACuP,SAAS,CAAG,EAAE,CAEtB,GAAI,CAAA9F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+F,IAAI,IAAK,MAAM,EAAI,CAAA/F,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAE+F,IAAI,IAAK,UAAU,CAAE,CAC7E,KAAM,CAAAC,QAAQ,CAAGrP,QAAQ,CAACsP,aAAa,CAAC,MAAM,CAAC,CAC/CD,QAAQ,CAACE,SAAS,CAAGlG,eAAe,CAAC+F,IAAI,GAAK,MAAM,CAAG,GAAG,CAAG,GAAG,CAChEC,QAAQ,CAACtM,KAAK,CAACyD,KAAK,CAAG,OAAO,CAC9B6I,QAAQ,CAACtM,KAAK,CAACyM,QAAQ,CAAG,MAAM,CAChCH,QAAQ,CAACtM,KAAK,CAACoD,UAAU,CAAG,MAAM,CAClCkJ,QAAQ,CAACtM,KAAK,CAACuD,SAAS,CAAG+C,eAAe,CAAC+F,IAAI,GAAK,MAAM,CAAG,QAAQ,CAAG,QAAQ,CAChFC,QAAQ,CAACtM,KAAK,CAACa,OAAO,CAAG,MAAM,CAC/ByL,QAAQ,CAACtM,KAAK,CAAC0M,UAAU,CAAG,QAAQ,CACpCJ,QAAQ,CAACtM,KAAK,CAAC2M,cAAc,CAAG,QAAQ,CACxCL,QAAQ,CAACtM,KAAK,CAAC4H,KAAK,CAAG,MAAM,CAC7B0E,QAAQ,CAACtM,KAAK,CAACkK,MAAM,CAAG,MAAM,CAC9BrN,OAAO,CAAC+P,WAAW,CAACN,QAAQ,CAAC,CAC9B,CAEA;AACA;AACA,KAAM,CAAAO,qBAAqB,CAAGpB,kBAAkB,CAAC,gBAAgB,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CAChG,KAAM,CAAAyD,WAAW,CAAG/Q,oBAAoB,GAAK,SAAS,CAClD8Q,qBAAqB,GAAK,KAAK,EAAI,CAAChQ,OAAO,CAACkQ,aAAa,CACzDzG,eAAe,EAAIzK,gBAAgB,EAAI,CAACgB,OAAO,CAACkQ,aAAc,CAElE,GAAID,WAAW,CAAE,CACPjQ,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC,CACxCpQ,OAAO,CAACmQ,SAAS,CAAClM,MAAM,CAAC,yBAAyB,CAAC,CACvD,CAAC,IAAM,CACHjE,OAAO,CAACmQ,SAAS,CAAClM,MAAM,CAAC,iBAAiB,CAAC,CAC3CjE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CACpD,CAEN;AACApQ,OAAO,CAACmD,KAAK,CAACa,OAAO,CAAG,MAAM,CAC9BhE,OAAO,CAACmD,KAAK,CAACmM,aAAa,CAAG,MAAM,CAEpC;AACA;AACA;AACA,KAAM,CAAAe,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CACvF,GAAI6D,aAAa,CAAE,CAClBvR,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AACA;AAAA,CAGD;AACA;AACA,GAAI,CAACkB,OAAO,CAACsQ,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,KAAM,CAAAC,UAAU,CAAGvQ,OAAO,CAACwQ,SAAS,CAAC,IAAI,CAAgB,CACzD;AACA,GAAIxQ,OAAO,CAACkQ,aAAa,GAAKlN,SAAS,CAAE,CAC9BuN,UAAU,CAASL,aAAa,CAAGlQ,OAAO,CAACkQ,aAAa,CAC7D,CACN,GAAIlQ,OAAO,CAACyQ,UAAU,CAAE,CACvBzQ,OAAO,CAACyQ,UAAU,CAACC,YAAY,CAACH,UAAU,CAAEvQ,OAAO,CAAC,CACpDA,OAAO,CAAGuQ,UAAU,CACrB,CACD,CAEA;AACAvQ,OAAO,CAACmD,KAAK,CAACmM,aAAa,CAAG,MAAM,CAEpC;AACA,KAAM,CAAAqB,QAAQ,CAAG/B,kBAAkB,CAAC,UAAU,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CAC7E,KAAM,CAAAoE,WAAW,CAAI5D,CAAQ,EAAK,CACjCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CACnBrP,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIkP,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACA7R,cAAc,CAAC,IAAI,CAAC,CAEpB;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACA,KAAM,CAAA2S,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CAC9F,GAAIsE,oBAAoB,CAAE,CACzB9Q,OAAO,CAACmQ,SAAS,CAAClM,MAAM,CAAC,iBAAiB,CAAC,CAC3CjE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDpQ,OAAO,CAACkQ,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED,KAAM,CAAAa,cAAc,CAAI/D,CAAQ,EAAK,CACpCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CAEnB;AACA;AACA,KAAM,CAAAR,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CACvF,GAAImE,QAAQ,GAAK,kBAAkB,EAAI,CAACN,aAAa,CAAE,CACtD;AAAA,CAEF,CAAC,CAED,KAAM,CAAAW,WAAW,CAAIhE,CAAQ,EAAK,CACjCA,CAAC,CAAC6D,eAAe,CAAC,CAAC,CACnBrP,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC,CAExC;AACA,GAAIkP,QAAQ,GAAK,kBAAkB,EAAI,CAACA,QAAQ,CAAE,CACjD;AACA7R,cAAc,CAAC,CAACC,WAAW,CAAC,CAE5B;AACA,GAAI,MAAO,CAAAX,kBAAkB,GAAK,UAAU,CAAE,CAC7CA,kBAAkB,CAAC,CAAC,CACrB,CAEA;AACJ,KAAM,CAAA0S,oBAAoB,CAAGlC,kBAAkB,CAAC,eAAe,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CAC1F,GAAIsE,oBAAoB,CAAE,CACzB9Q,OAAO,CAACmQ,SAAS,CAAClM,MAAM,CAAC,iBAAiB,CAAC,CAC3CjE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC,CAChDpQ,OAAO,CAACkQ,aAAa,CAAG,IAAI,CAAE;AAC/B,CACD,CACD,CAAC,CAED;AACA,GAAI,CAAClQ,OAAO,CAACsQ,YAAY,CAAC,yBAAyB,CAAC,CAAE,CACrD,GAAIK,QAAQ,GAAK,kBAAkB,CAAE,CACpC;AACA3Q,OAAO,CAACiK,gBAAgB,CAAC,WAAW,CAAE2G,WAAW,CAAC,CAClD5Q,OAAO,CAACiK,gBAAgB,CAAC,UAAU,CAAE8G,cAAc,CAAC,CAEpD;AACA/Q,OAAO,CAACiK,gBAAgB,CAAC,OAAO,CAAE+G,WAAW,CAAC,CAC/C,CAAC,IAAM,CACN;AACAhR,OAAO,CAACiK,gBAAgB,CAAC,OAAO,CAAE+G,WAAW,CAAC,CAC/C,CAEA;AACAhR,OAAO,CAACiR,YAAY,CAAC,yBAAyB,CAAE,MAAM,CAAC,CACxD,CACD,CAAC,CACDtW,SAAS,CAAC,IAAM,CACf,GAAI,CAAAkG,OAAO,CACX,GAAI,CAAAqQ,KAAK,CAET,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACrC,GAAI,KAAAC,uBAAA,CAAAC,uBAAA,CAAAC,MAAA,CAAAC,OAAA,CACH;AACAL,KAAK,CAAG,CAAAjT,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE6D,SAAS,GAAI,EAAE,CAEvC;AACA,KAAM,CAAA0P,WAAW,CAAGtS,oBAAoB,GAAK,SAAS,EAAIjB,cAAc,SAAdA,cAAc,YAAAmT,uBAAA,CAAdnT,cAAc,CAAE6D,SAAS,UAAAsP,uBAAA,YAAAC,uBAAA,CAAzBD,uBAAA,CAA4BhU,WAAW,CAAG,CAAC,CAAC,UAAAiU,uBAAA,WAA5CA,uBAAA,CAA8CtP,WAAW,CAC/G9D,cAAc,CAAC6D,SAAS,CAAC1E,WAAW,CAAG,CAAC,CAAC,CAAS2E,WAAW,CAC9D,EAAAuP,MAAA,CAAAJ,KAAK,UAAAI,MAAA,kBAAAC,OAAA,CAALD,MAAA,CAAQ,CAAC,CAAC,UAAAC,OAAA,iBAAVA,OAAA,CAAYxP,WAAW,GAAI,EAAE,CAEhClB,OAAO,CAAGZ,iBAAiB,CAACuR,WAAW,EAAI,EAAE,CAAC,CAC9CjS,gBAAgB,CAACsB,OAAO,CAAC,CAEzB,GAAIA,OAAO,CAAE,CACZ;AAAA,CAGD;AACA,KAAM,CAAA4Q,iBAAiB,CAAGjT,gBAAgB,GAAK,SAAS,EACvDU,oBAAoB,GAAK,SAAS,EAClCrC,KAAK,GAAK,SAAS,EAClB2B,gBAAgB,GAAK,MAAM,EAAIU,oBAAoB,GAAK,SAAU,CAEpE,GAAIuS,iBAAiB,CAAE,KAAAC,sBAAA,CAAAC,sBAAA,CAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAItB;AACA,GAAI,CAAAtI,eAAe,CACnB,GAAI,CAAA+C,WAAW,CAEf,GAAItN,oBAAoB,GAAK,SAAS,EAAIT,oBAAoB,SAApBA,oBAAoB,YAAAiT,sBAAA,CAApBjT,oBAAoB,CAAGrB,WAAW,CAAG,CAAC,CAAC,UAAAsU,sBAAA,WAAvCA,sBAAA,CAAyChI,QAAQ,CAAE,KAAAsI,uBAAA,CAAAC,uBAAA,CAC5F;AACAxI,eAAe,CAAGhL,oBAAoB,CAACrB,WAAW,CAAG,CAAC,CAAC,CAACsM,QAAQ,CAChE8C,WAAW,CAAGvO,cAAc,SAAdA,cAAc,kBAAA+T,uBAAA,CAAd/T,cAAc,CAAE6D,SAAS,UAAAkQ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B5U,WAAW,CAAG,CAAC,CAAC,UAAA6U,uBAAA,iBAA5CA,uBAAA,CAA8CtM,OAAO,CACpE,CAAC,IAAM,IAAIlH,oBAAoB,SAApBA,oBAAoB,YAAAkT,sBAAA,CAApBlT,oBAAoB,CAAG,CAAC,CAAC,UAAAkT,sBAAA,WAAzBA,sBAAA,CAA2BjI,QAAQ,CAAE,KAAAwI,uBAAA,CAAAC,uBAAA,CAC/C;AACA1I,eAAe,CAAGhL,oBAAoB,CAAC,CAAC,CAAC,CAACiL,QAAQ,CAClD8C,WAAW,CAAGvO,cAAc,SAAdA,cAAc,kBAAAiU,uBAAA,CAAdjU,cAAc,CAAE6D,SAAS,UAAAoQ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,iBAA9BA,uBAAA,CAAgCxM,OAAO,CACtD,CAAC,IAAM,KAAAyM,uBAAA,CAAAC,uBAAA,CACN;AACA5I,eAAe,CAAG,CACjBG,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACd2F,IAAI,CAAE,UAAU,CAChBL,KAAK,CAAE,QAAQ,CACfD,IAAI,CAAE,IAAI,CACVJ,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5B/C,aAAa,CAAE,KAChB,CAAC,CACDO,WAAW,CAAG,CAAAvO,cAAc,SAAdA,cAAc,kBAAAmU,uBAAA,CAAdnU,cAAc,CAAE6D,SAAS,UAAAsQ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BhV,WAAW,CAAG,CAAC,CAAC,UAAAiV,uBAAA,iBAA5CA,uBAAA,CAA8C1M,OAAO,GAAI,CAAC,CAAC,CAC1E,CACA,KAAM,CAAArD,OAAO,CAAGqH,UAAU,CAAC,EAAAiI,gBAAA,CAAAnI,eAAe,UAAAmI,gBAAA,iBAAfA,gBAAA,CAAiBhI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAArH,OAAO,CAAGoH,UAAU,CAAC,EAAAkI,iBAAA,CAAApI,eAAe,UAAAoI,iBAAA,iBAAfA,iBAAA,CAAiBhI,SAAS,GAAI,GAAG,CAAC,CAC7D,KAAM,CAAAyI,kBAAkB,CAAG3I,UAAU,CAAC,EAAAmI,iBAAA,CAAArI,eAAe,UAAAqI,iBAAA,iBAAfA,iBAAA,CAAiB5C,IAAI,GAAI,IAAI,CAAC,CAEpE;AACArP,cAAc,CAACyS,kBAAkB,CAAC,CAElC,GAAI,CAAApR,IAAI,CAAED,GAAG,CACb,GAAIJ,OAAO,CAAE,CACZ,KAAM,CAAAC,IAAI,CAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC,CAC5CG,IAAI,CAAGJ,IAAI,CAAC2B,CAAC,CAAGH,OAAO,CACvBrB,GAAG,CAAGH,IAAI,CAAC6B,CAAC,EAAIJ,OAAO,CAAG,CAAC,CAAG,CAACA,OAAO,CAAGwG,IAAI,CAACwJ,GAAG,CAAChQ,OAAO,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAuH,QAAQ,CAAG1H,sBAAsB,CAACtB,IAAI,CAAEwR,kBAAkB,CAAEhQ,OAAO,CAAEC,OAAO,CAAC,CACnF9C,gBAAgB,CAACqK,QAAQ,CAAC,CAC3B,CAEA;AACA,KAAM,CAAAhG,eAAe,CAAG1D,QAAQ,CAAC2D,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpB9D,OAAO,CAAG8D,eAAe,CACzB;AACD,CAAC,IAAM,CACN;AACA9D,OAAO,CAAGI,QAAQ,CAACsP,aAAa,CAAC,KAAK,CAAC,CACvC1P,OAAO,CAACwS,EAAE,CAAG,cAAc,CAAE;AAC7BxS,OAAO,CAACkQ,aAAa,CAAG,KAAK,CAAE;AAC/B9P,QAAQ,CAACqS,IAAI,CAAC1C,WAAW,CAAC/P,OAAO,CAAC,CACnC,CAEAA,OAAO,CAACmD,KAAK,CAACuP,MAAM,CAAG,SAAS,CAChC1S,OAAO,CAACmD,KAAK,CAACmM,aAAa,CAAG,MAAM,CAAE;AAEtC;AACAtP,OAAO,CAACmD,KAAK,CAACiM,MAAM,CAAG,MAAM,CAE7B;AACA,IAAA2C,iBAAA,CAAItI,eAAe,UAAAsI,iBAAA,WAAfA,iBAAA,CAAiB9F,aAAa,CAAE,CACnCnN,cAAc,CAAC,IAAI,CAAC,CACrB,CAEA;AACAmQ,kBAAkB,CAACjP,OAAO,CAAEyJ,eAAe,CAAE+C,WAAW,CAAEtL,IAAI,CAAED,GAAG,CAAC,CAEpE;AACA,KAAM,CAAAoP,aAAa,CAAGzB,kBAAkB,CAAC,eAAe,CAAEnF,eAAe,CAAE+C,WAAW,CAAC,CACvF,GAAI6D,aAAa,CAAE,CAClBvR,cAAc,CAAC,IAAI,CAAC,CACrB,CAAC,IAAM,CACN;AAAA,CAGD;AACD,CACD,CAAE,MAAO6T,KAAK,CAAE,CACfnR,OAAO,CAACmR,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACpD,CACD,CAAC,CAEDxB,iBAAiB,CAAC,CAAC,CAEnB,MAAO,IAAM,CACZ,KAAM,CAAArN,eAAe,CAAG1D,QAAQ,CAAC2D,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAID,eAAe,CAAE,CACpBA,eAAe,CAAC8O,OAAO,CAAG,IAAI,CAC9B9O,eAAe,CAAC+O,WAAW,CAAG,IAAI,CAClC/O,eAAe,CAACgP,UAAU,CAAG,IAAI,CAClC,CACD,CAAC,CACF,CAAC,CAAE,CACF7U,cAAc,CACdQ,oBAAoB,CACpBJ,kBAAkB,CAClBC,kBAAkB,CAClBY,oBAAoB,CACpB9B,WACA;AAAA,CACA,CAAC,CACF,KAAM,CAAA2V,cAAc,CAAG,CAAA9U,cAAc,SAAdA,cAAc,kBAAAzB,uBAAA,CAAdyB,cAAc,CAAE6D,SAAS,UAAAtF,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCuW,OAAO,UAAAtW,uBAAA,iBAAvCA,uBAAA,CAAyCuW,cAAc,GAAI,KAAK,CAEvF,QAAS,CAAAC,mBAAmBA,CAAC/T,cAAmB,CAAE,KAAAgU,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CACjD,GAAIlU,cAAc,GAAK,CAAC,CAAE,CACzB,MAAO,MAAM,CACd,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,QAAQ,CAChB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAAC,IAAM,IAAIA,cAAc,GAAK,CAAC,CAAE,CAChC,MAAO,aAAa,CACrB,CAEA,MAAO,CAAAlB,cAAc,SAAdA,cAAc,kBAAAkV,uBAAA,CAAdlV,cAAc,CAAE6D,SAAS,UAAAqR,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9BD,uBAAA,CAAgCJ,OAAO,UAAAK,uBAAA,iBAAvCA,uBAAA,CAAyCC,gBAAgB,GAAI,MAAM,CAC3E,CACA,KAAM,CAAAC,gBAAgB,CAAGL,mBAAmB,CAAC/T,cAAc,CAAC,CAC5D,KAAM,CAAAqU,cAAc,CAAGA,CAAA,GAAM,CAC5B,GAAI,CAACT,cAAc,CAAE,MAAO,KAAI,CAEhC,GAAIQ,gBAAgB,GAAK,MAAM,CAAE,CAChC,mBACC9X,IAAA,CAACP,aAAa,EACbuY,OAAO,CAAC,MAAM,CACdvC,KAAK,CAAE7T,UAAW,CAClB4F,QAAQ,CAAC,QAAQ,CACjByQ,UAAU,CAAEtW,WAAW,CAAG,CAAE,CAC5BuW,EAAE,CAAE,CACHvQ,eAAe,CAAE,aAAa,CAC9BH,QAAQ,CAAE,oBAAoB,CAC9B,+BAA+B,CAAE,CAChCG,eAAe,CAAEhE,aAAe;AACjC,CACD,CAAE,CACFwU,UAAU,cAAEnY,IAAA,CAACV,MAAM,EAACoI,KAAK,CAAE,CAAE0Q,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxDC,UAAU,cAAErY,IAAA,CAACV,MAAM,EAACoI,KAAK,CAAE,CAAE0Q,UAAU,CAAE,QAAS,CAAE,CAAE,CAAE,CACxD,CAAC,CAEJ,CACA,GAAIN,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACC9X,IAAA,CAACX,GAAG,EAAC6Y,EAAE,CAAE,CAAE3P,OAAO,CAAE,MAAM,CAAE6L,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,QAAQ,CAAEC,GAAG,CAAE,KAAK,CAAEC,OAAO,CAAE,KAAM,CAAE,CAAAC,QAAA,CAGrGC,KAAK,CAACC,IAAI,CAAC,CAAE1M,MAAM,CAAErK,UAAW,CAAC,CAAC,CAAC4H,GAAG,CAAC,CAACoP,CAAC,CAAEC,KAAK,gBAChD7Y,IAAA,QAEC0H,KAAK,CAAE,CACN4H,KAAK,CAAE,MAAM,CACbsC,MAAM,CAAE,KAAK,CACbjK,eAAe,CAAEkR,KAAK,GAAKlX,WAAW,CAAG,CAAC,CAAGgC,aAAa,CAAG,SAAS,CAAE;AACxEmL,YAAY,CAAE,OACf,CAAE,EANG+J,KAOL,CACD,CAAC,CACE,CAAC,CAER,CACA,GAAIf,gBAAgB,GAAK,aAAa,CAAE,CACvC,mBACC9X,IAAA,CAACX,GAAG,EAAC6Y,EAAE,CAAE,CAAE3P,OAAO,CAAE,MAAM,CAAE6L,UAAU,CAAE,QAAQ,CAAEkE,YAAY,CAAE,YAAa,CAAE,CAAAG,QAAA,cAC9EvY,KAAA,CAACP,UAAU,EAACuY,EAAE,CAAE,CAAEM,OAAO,CAAE,KAAK,CAAErN,KAAK,CAAExH,aAAc,CAAE,CAAA8U,QAAA,EAAC,OACpD,CAAC9W,WAAW,CAAC,MAAI,CAACC,UAAU,EACtB,CAAC,CACT,CAAC,CAER,CAEA,GAAIkW,gBAAgB,GAAK,QAAQ,CAAE,CAClC,mBACC9X,IAAA,CAACX,GAAG,EAAAoZ,QAAA,cACHzY,IAAA,CAACL,UAAU,EAACqY,OAAO,CAAC,OAAO,CAAAS,QAAA,cAC1BzY,IAAA,CAACR,cAAc,EACdwY,OAAO,CAAC,aAAa,CACrBc,KAAK,CAAEhX,QAAS,CAChBoW,EAAE,CAAE,CACHtG,MAAM,CAAE,KAAK,CACX9C,YAAY,CAAE,MAAM,CACpBiK,MAAM,CAAE,UAAU,CACpB,0BAA0B,CAAE,CAC3BpR,eAAe,CAAEhE,aAAe;AACjC,CACD,CAAE,CACF,CAAC,CACS,CAAC,CACT,CAAC,CAER,CAEA,MAAO,KAAI,CACZ,CAAC,CACD,mBACCzD,KAAA,CAAAE,SAAA,EAAAqY,QAAA,EACE5U,aAAa,eACb7D,IAAA,QAAAyY,QAAA,CAcEnV,WAAW,eACXpD,KAAA,CAACR,OAAO,EACPuQ,IAAI,CAAE+I,OAAO,CAACjV,aAAa,CAAC,EAAIiV,OAAO,CAAC9X,QAAQ,CAAE,CAClDA,QAAQ,CAAEA,QAAS,CACnBK,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFiJ,YAAY,CAAEA,YAAa,CAC3BG,eAAe,CAAEA,eAAgB,CACjCsO,eAAe,CAAC,gBAAgB,CAChCC,cAAc,CACbnV,aAAa,CACV,CACAyB,GAAG,CAAEzB,aAAa,CAACyB,GAAG,CAAE,EAAE,EAAG0I,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAAG,CAAC8K,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAAGkK,IAAI,CAACwJ,GAAG,CAAC5I,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAAC,CAAC,CAChJqC,IAAI,CAAE1B,aAAa,CAAC0B,IAAI,CAAE,EAAE,CAAEyI,UAAU,CAAC/K,YAAY,EAAI,GAAG,CAC5D,CAAC,CACDoE,SACH,CACD2Q,EAAE,CAAE,CACH;AACA;AACA;AACA,gBAAgB,CAAEhX,QAAQ,CAAG,MAAM,CAAG,MAAM,CAC5C,8CAA8C,CAAE,CAC/CyS,MAAM,CAAE,IAAI,CACZ;AACA,GAAG9E,WAAW,CACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAGoE,iBAAiB,CAAC,CAAA9Q,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEyI,QAAQ,GAAI,eAAe,CAAC,CACnEpF,GAAG,CAAE,GAAG,CAAC,CAAAzB,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAEyB,GAAG,GAAI,CAAC,GAC5BpC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC3C8K,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAAG,CAAC,CAClC,CAAC8K,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAChCkK,IAAI,CAACwJ,GAAG,CAAC5I,UAAU,CAAC9K,YAAY,EAAI,GAAG,CAAC,CAAC,CAC1C,CAAC,CAAC,eAAe,CACrBqC,IAAI,CAAE,GAAG,CAAC,CAAA1B,aAAa,SAAbA,aAAa,iBAAbA,aAAa,CAAE0B,IAAI,GAAI,CAAC,GAAKtC,YAAY,EAAIA,YAAY,EAAI,WAAW,CAC9E+K,UAAU,CAAC/K,YAAY,CAAC,EAAI,CAAC,CAC9B,CAAC,CAAE,eAAe,CACrBgW,QAAQ,CAAE,QACX,CACD,CAAE,CACFC,iBAAiB,CAAE,IAAK,CAAAX,QAAA,eAExBzY,IAAA,QAAK0H,KAAK,CAAE,CAAE4Q,YAAY,CAAE,KAAK,CAAE/P,OAAO,CAAE,MAAO,CAAE,CAAAkQ,QAAA,CACnD,CAAAvW,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEmX,aAAa,gBAC9BrZ,IAAA,CAACT,UAAU,EACV+Z,OAAO,CAAEA,CAAA,GAAM,CACd;AACA;AAAA,CACC,CACFpB,EAAE,CAAE,CACH1Q,QAAQ,CAAE,OAAO,CACjB+R,SAAS,CAAE,iCAAiC,CAC5C9T,IAAI,CAAE,MAAM,CACZI,KAAK,CAAE,MAAM,CACbkT,MAAM,CAAE,OAAO,CACfS,UAAU,CAAE,iBAAiB,CAC7BC,MAAM,CAAE,gBAAgB,CACxB9F,MAAM,CAAE,QAAQ,CAChB7E,YAAY,CAAE,MAAM,CACpB0J,OAAO,CAAE,gBACV,CAAE,CAAAC,QAAA,cAEFzY,IAAA,CAACJ,SAAS,EAACsY,EAAE,CAAE,CAAEwB,IAAI,CAAE,CAAC,CAAEvO,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClC,CACZ,CACG,CAAC,cACNnL,IAAA,CAACF,gBAAgB,EAEpB6Z,GAAG,CAAEjT,YAAa,CAClBgB,KAAK,CAAE,CAAEkS,SAAS,CAAE7N,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAQ,CAAE,CAC3EiN,OAAO,CAAE,CACRC,eAAe,CAAE,CAACtT,cAAc,CAChCuT,eAAe,CAAE,IAAI,CACrBC,gBAAgB,CAAE,KAAK,CACvBC,WAAW,CAAE,IAAI,CACjBC,kBAAkB,CAAE,EAAE,CACtBC,kBAAkB,CAAE,IAAI,CACxBC,mBAAmB,CAAE,CACtB,CAAE,CAAA3B,QAAA,cAECzY,IAAA,QAAK0H,KAAK,CAAE,CACXkS,SAAS,CAAE7N,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,OAAO,CAC/DuM,QAAQ,CAAEpN,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAG,SAAS,CAAG,aAAa,CACvE0C,KAAK,CAAEvD,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAG,MAAM,CAAGrF,SAAS,CAC7DwR,MAAM,CAAEhN,cAAc,CAAC,CAAC,CAAG,GAAG,CAAGxE,SAClC,CAAE,CAAAkR,QAAA,cACDvY,KAAA,CAACb,GAAG,EAACqI,KAAK,CAAE,CACX8Q,OAAO,CAAEzM,cAAc,CAAC,CAAC,CAAG,GAAG,CAC7Ba,WAAW,CAAC,CAAC,CAAG,GAAG,CAAI,CAAAzK,gBAAgB,SAAhBA,gBAAgB,iBAAhBA,gBAAgB,CAAEkY,OAAO,GAAI,MAAO,CAC7DzI,MAAM,CAAE7F,cAAc,CAAC,CAAC,CAAG,MAAM,CAAGwD,aAAa,CACjDD,KAAK,CAAEvD,cAAc,CAAC,CAAC,EAAIa,WAAW,CAAC,CAAC,CAAG,MAAM,CAAGrF,SAAS,CAC7DwR,MAAM,CAAEhN,cAAc,CAAC,CAAC,CAAG,GAAG,CAAGxE,SAClC,CAAE,CAAAkR,QAAA,eACDvY,KAAA,CAACb,GAAG,EACHsa,GAAG,CAAEtV,UAAW,CAChBkE,OAAO,CAAC,MAAM,CACd+R,aAAa,CAAC,QAAQ,CACtBC,QAAQ,CAAC,MAAM,CACflG,cAAc,CAAC,QAAQ,CACvB6D,EAAE,CAAE,CACH5I,KAAK,CAAE1C,WAAW,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACtC4L,OAAO,CAAE5L,WAAW,CAAC,CAAC,CAAG,GAAG,CAAGrF,SAChC,CAAE,CAAAkR,QAAA,EAEDzW,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEwH,GAAG,CAAEgR,SAAc,EACpCA,SAAS,CAACpO,WAAW,CAAC5C,GAAG,CAAC,CAACiR,SAAc,CAAEC,QAAgB,gBAC1D1a,IAAA,CAACX,GAAG,EAEHsb,SAAS,CAAC,KAAK,CACfC,GAAG,CAAEH,SAAS,CAACnO,GAAI,CACnBuO,GAAG,CAAEJ,SAAS,CAACK,OAAO,EAAI,OAAQ,CAClC5C,EAAE,CAAE,CACH0B,SAAS,CAAEY,SAAS,CAACO,cAAc,EAAIN,SAAS,CAACM,cAAc,EAAI,OAAO,CAC1E1P,SAAS,CAAEmP,SAAS,CAAClP,SAAS,EAAI,QAAQ,CAC1C0P,SAAS,CAAEP,SAAS,CAACQ,GAAG,EAAI,SAAS,CACrC;AACArJ,MAAM,CAAE,GAAG6I,SAAS,CAACjL,aAAa,EAAI,GAAG,IAAI,CAC7CgK,UAAU,CAAEiB,SAAS,CAACpL,eAAe,EAAI,SAAS,CAClD0J,MAAM,CAAE,QACT,CAAE,CACFO,OAAO,CAAEA,CAAA,GAAM,CACd,GAAIkB,SAAS,CAACU,SAAS,CAAE,CACxB,KAAM,CAAAtL,SAAS,CAAG4K,SAAS,CAACU,SAAS,CACrCvV,MAAM,CAACsK,IAAI,CAACL,SAAS,CAAE,QAAQ,CAAE,qBAAqB,CAAC,CACxD,CACD,CAAE,CACFlI,KAAK,CAAE,CAAEuP,MAAM,CAAEuD,SAAS,CAACU,SAAS,CAAG,SAAS,CAAG,SAAU,CAAE,EAnB1D,GAAGV,SAAS,CAAC3Q,EAAE,IAAI6Q,QAAQ,EAoBhC,CACD,CACF,CAAC,CAEA3Y,mBAAmB,SAAnBA,mBAAmB,iBAAnBA,mBAAmB,CAAEyH,GAAG,CACxB,CAAC2R,SAAc,CAAEtC,KAAU,QAAAuC,qBAAA,CAAAC,sBAAA,OAC1B,CAAAF,SAAS,CAAC1O,IAAI,eACbzM,IAAA,CAACL,UAAU,EACV2b,SAAS,CAAC,eAAe,CACG;AAC5BpD,EAAE,CAAE,CACH7M,SAAS,CAAE,EAAA+P,qBAAA,CAAAD,SAAS,CAACpQ,cAAc,UAAAqQ,qBAAA,iBAAxBA,qBAAA,CAA0BG,UAAU,GAAI1Q,SAAS,CAACQ,SAAS,CACtEF,KAAK,CAAE,EAAAkQ,sBAAA,CAAAF,SAAS,CAACpQ,cAAc,UAAAsQ,sBAAA,iBAAxBA,sBAAA,CAA0BjQ,SAAS,GAAIP,SAAS,CAACM,KAAK,CAC7DqQ,UAAU,CAAE,UAAU,CACtBC,SAAS,CAAE,YAAY,CACvBjD,OAAO,CAAE,OACV,CAAE,CACFkD,uBAAuB,CAAEnQ,iBAAiB,CAAC4P,SAAS,CAAC1O,IAAI,CAAG;AAAA,EARvD0O,SAAS,CAACtR,EAAE,EAAIgP,KASrB,CACD,EACH,CAAC,EACG,CAAC,CAEL8C,MAAM,CAACC,IAAI,CAAClN,cAAc,CAAC,CAAClF,GAAG,CAAEmF,WAAW,OAAAkN,qBAAA,CAAAC,sBAAA,oBAC5C9b,IAAA,CAACX,GAAG,EACHsa,GAAG,CAAErV,kBAAmB,CAExB4T,EAAE,CAAE,CACH3P,OAAO,CAAE,MAAM,CACf8L,cAAc,CAAEtB,YAAY,EAAA8I,qBAAA,CAACnN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAkN,qBAAA,iBAA9BA,qBAAA,CAAgCvQ,SAAS,CAAC,CACvEiP,QAAQ,CAAE,MAAM,CAChBxB,MAAM,CAAEhN,cAAc,CAAC,CAAC,CAAG,CAAC,CAAG,OAAO,CACtCpE,eAAe,EAAAmU,sBAAA,CAAEpN,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,UAAAmN,sBAAA,iBAA9BA,sBAAA,CAAgCzM,eAAe,CAChEmJ,OAAO,CAAEzM,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,OAAO,CAC3CuD,KAAK,CAAEvD,cAAc,CAAC,CAAC,CAAG,MAAM,CAAG,MAAM,CACzC+C,YAAY,CAAE/C,cAAc,CAAC,CAAC,CAAG,MAAM,CAAGxE,SAC3C,CAAE,CAAAkR,QAAA,CAED/J,cAAc,CAACC,WAAW,CAAC,CAACnF,GAAG,CAAC,CAACG,MAAW,CAAEkP,KAAa,QAAAkD,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,oBAC3Drc,IAAA,CAACV,MAAM,EAENga,OAAO,CAAEA,CAAA,GAAM7J,kBAAkB,CAAC9F,MAAM,CAAC2S,YAAY,CAAE,CACvDtE,OAAO,CAAC,WAAW,CACnBE,EAAE,CAAE,CACHqE,WAAW,CAAExQ,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,MAAM,CAC9CgN,MAAM,CAAEhN,cAAc,CAAC,CAAC,CAAG,KAAK,CAAG,eAAe,CAClDpE,eAAe,CAAE,EAAAoU,qBAAA,CAAApS,MAAM,CAAC6S,gBAAgB,UAAAT,qBAAA,iBAAvBA,qBAAA,CAAyBU,qBAAqB,GAAI,SAAS,CAC5EtR,KAAK,CAAE,EAAA6Q,sBAAA,CAAArS,MAAM,CAAC6S,gBAAgB,UAAAR,sBAAA,iBAAvBA,sBAAA,CAAyBU,eAAe,GAAI,MAAM,CACzDjD,MAAM,CAAE,EAAAwC,sBAAA,CAAAtS,MAAM,CAAC6S,gBAAgB,UAAAP,sBAAA,iBAAvBA,sBAAA,CAAyBU,iBAAiB,GAAI,aAAa,CACnExI,QAAQ,CAAE,EAAA+H,sBAAA,CAAAvS,MAAM,CAAC6S,gBAAgB,UAAAN,sBAAA,iBAAvBA,sBAAA,CAAyBU,QAAQ,GAAI,MAAM,CACrDtN,KAAK,CAAE,EAAA6M,sBAAA,CAAAxS,MAAM,CAAC6S,gBAAgB,UAAAL,sBAAA,iBAAvBA,sBAAA,CAAyBnP,KAAK,GAAI,MAAM,CAC/CwL,OAAO,CAAEzM,cAAc,CAAC,CAAC,CAAG,kCAAkC,CAAG,SAAS,CAC1E8Q,UAAU,CAAE9Q,cAAc,CAAC,CAAC,CAAG,0BAA0B,CAAG,QAAQ,CACpE+Q,aAAa,CAAE,MAAM,CACrBhO,YAAY,CAAE,EAAAsN,sBAAA,CAAAzS,MAAM,CAAC6S,gBAAgB,UAAAJ,sBAAA,iBAAvBA,sBAAA,CAAyBW,YAAY,GAAI,KAAK,CAC5DtP,QAAQ,CAAE1B,cAAc,CAAC,CAAC,CAAG,aAAa,CAAGxE,SAAS,CACtDgS,SAAS,CAAE,iBAAiB,CAAE;AAC9B,SAAS,CAAE,CACV5R,eAAe,CAAE,EAAA0U,sBAAA,CAAA1S,MAAM,CAAC6S,gBAAgB,UAAAH,sBAAA,iBAAvBA,sBAAA,CAAyBI,qBAAqB,GAAI,SAAS,CAAE;AAC9EO,OAAO,CAAE,GAAG,CAAE;AACdzD,SAAS,CAAE,iBAAmB;AAC/B,CACD,CAAE,CAAAd,QAAA,CAED9O,MAAM,CAACsT,UAAU,EAxBbpE,KAyBE,CAAC,EACT,CAAC,EAxCGlK,WAyCD,CAAC,EACN,CAAC,EACE,CAAC,CAGD,CAAC,EApIL,aAAanI,cAAc,EAqIV,CAAC,CAElB8Q,cAAc,EAAI1V,UAAU,CAAC,CAAC,EAAImB,gBAAgB,GAAK,MAAM,eAAI/C,IAAA,CAACX,GAAG,EAAAoZ,QAAA,CAAEV,cAAc,CAAC,CAAC,CAAM,CAAC,CAAE,GAAG,EAC7F,CACT,CACG,CACL,cAED/X,IAAA,UAAAyY,QAAA,CACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CACC,CAAC,EACP,CAAC,CAEL,CAAC,CAED,cAAe,CAAApY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}