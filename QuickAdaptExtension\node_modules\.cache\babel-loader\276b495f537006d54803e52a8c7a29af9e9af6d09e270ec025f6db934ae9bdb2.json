{"ast": null, "code": "import React,{useState,useEffect,useContext}from\"react\";import{<PERSON><PERSON>,DialogContent,TextField,InputAdornment,IconButton,Tab,Tabs,Tooltip,DialogTitle,DialogContentText,DialogActions,Button,Typography}from\"@mui/material\";import{DataGrid}from\"@mui/x-data-grid\";import SearchIcon from\"@mui/icons-material/Search\";import ClearIcon from\"@mui/icons-material/Clear\";import{getAllGuides,DeleteGuideByGuideId}from\"../../../services/GuideListServices\";import{ListEditIcon,CopyListIcon,DeleteIconList,NoData}from\"../../../assets/icons/icons\";import DeleteOutlineOutlinedIcon from\"@mui/icons-material/DeleteOutlineOutlined\";import\"./GuideMenuOptions.css\";import AddIcon from\"@mui/icons-material/Add\";import CloneInteractionDialog from\"./CloneGuidePopUp\";import{AccountContext}from\"../../login/AccountContext\";import{useSnackbar}from\"./SnackbarContext\";import{formatDateTime}from\"../../guideSetting/guideList/TimeZoneConversion\";import useDrawerStore from\"../../../store/drawerStore\";import useUserSession from\"../../../store/userSession\";import{jsxs as _jsxs,jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";let editedguide;const PopupModal=_ref=>{let{Open,onClose,title,searchText,onAddClick}=_ref;const{setCurrentGuideId,currentGuideId}=useUserSession(state=>state);const{setBannerPopup,setOpenTooltip,setElementSelected,setBannerButtonSelected,selectedTemplateTour,isUnSavedChanges,setIsUnSavedChanges,openWarning,setOpenWarning,setEditClicked,setActiveMenu,setSearchText}=useDrawerStore(state=>state);const[activeTab,setActiveTab]=useState(0);const[searchQuery,setSearchQuery]=useState(\"\");const[filteredData,setFilteredData]=useState([]);const[isCloneDialogOpen,setIsCloneDialogOpen]=useState(false);const[cloneAnnouncementName,setCloneAnnouncementName]=useState(null);const[guideIdToDelete,setGuideIdToDelete]=useState(null);const[GuidenametoDelete,setGuideNametoDelete]=useState(\"\");const[GuideTypetoDelete,setGuideTypetoDelete]=useState(\"\");const[openDialog,setOpenDialog]=useState(false);const[paginationModel,setPaginationModel]=useState({page:0,pageSize:15});const{accountId}=useContext(AccountContext);const{openSnackbar}=useSnackbar();const[totalCount,setTotalCount]=useState(0);const[name,setName]=useState(\"Announcement\");const handleEditClick=guide=>{setBannerButtonSelected(true);setIsUnSavedChanges(false);setEditClicked(true);setOpenWarning(false);let targetUrl=\"\";editedguide=true;if(guide.GuideType.toLowerCase()==\"announcement\"||guide.GuideType.toLowerCase()===\"tooltip\"||guide.GuideType.toLowerCase()===\"hotspot\"||guide.GuideType.toLowerCase()===\"tour\"||guide.GuideType.toLowerCase()===\"checklist\"){if(guide.GuideType.toLowerCase()===\"tooltip\"||guide.GuideType.toLowerCase()===\"hotspot\"||guide.GuideType.toLowerCase()===\"banner\"||selectedTemplateTour===\"Tooltip\"||selectedTemplateTour===\"Banner\"||selectedTemplateTour===\"Hotspot\"){setOpenTooltip(true);setElementSelected(true);let styleTag=document.getElementById(\"dynamic-body-style\");const bodyElement=document.body;// Add a dynamic class to the body\nbodyElement.classList.add(\"dynamic-body-style\");if(!styleTag){styleTag=document.createElement(\"style\");styleTag.id=\"dynamic-body-style\";// Add styles for body and nested elements\nlet styles=`\n\t\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t`;styleTag.innerHTML=styles;document.head.appendChild(styleTag);}}targetUrl=`${guide===null||guide===void 0?void 0:guide.TargetUrl}`;if(targetUrl!==window.location.href){setCurrentGuideId(guide.GuideId);window.open(targetUrl);}else{setCurrentGuideId(guide.GuideId);}return;}else if(guide.GuideType.toLowerCase()==\"banner\"||selectedTemplateTour===\"Banner\"){//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\nsetCurrentGuideId(guide.GuideId);setBannerPopup(true);}if(targetUrl){//onAddClick(guide.GuideType, true, guide);\nwindow.open(targetUrl);}};const handleCopyClick=announcement=>{setCloneAnnouncementName(announcement);setIsCloneDialogOpen(true);};const handleDeleteConfirmation=guideId=>{setGuideIdToDelete(guideId);setOpenDialog(true);};const handleKeyDown=event=>{if(event.key===\"Enter\"){handleSearch();}};const columns=[{field:\"Name\",headerName:\"Name\",// width: 300,\nhideable:true,resizable:false},{field:\"UpdatedDate\",headerName:\"Last Edited\",// width: 250,\nhideable:true,renderCell:params=>/*#__PURE__*/_jsxs(\"span\",{children:[\" \",`${formatDateTime(params.row.UpdatedDate,\"dd-MM-yyyy\")}`]}),resizable:false},{field:\"actions\",headerName:\"Actions\",// width: 302,\nhideable:true,sortable:false,renderCell:params=>/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Edit\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleEditClick(params.row),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:ListEditIcon},style:{zoom:0.7}})})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Clone\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>handleCopyClick(params.row),children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:CopyListIcon},style:{zoom:0.7}})})}),/*#__PURE__*/_jsx(Tooltip,{arrow:true,title:\"Delete\",children:/*#__PURE__*/_jsx(IconButton,{onClick:()=>{handleDeleteConfirmation(params.row.GuideId);setGuideNametoDelete(params.row.Name);setGuideTypetoDelete(params.row.GuideType);},children:/*#__PURE__*/_jsx(\"span\",{dangerouslySetInnerHTML:{__html:DeleteIconList},style:{zoom:0.7}})})})]}),resizable:false}];const fetchAnnouncements=async()=>{var _data$results;const{page,pageSize}=paginationModel;const offset=page*pageSize;const statusFilter=activeTab===0?\"Active\":activeTab===1?\"InActive\":\"Draft\";const filters=[{FieldName:\"GuideType\",ElementType:\"string\",Condition:\"equals\",Value:title===\"Product Tours\"?\"Tour\":title,IsCustomField:false},{FieldName:\"GuideStatus\",ElementType:\"string\",Condition:\"equals\",Value:statusFilter,IsCustomField:false},{FieldName:\"Name\",ElementType:\"string\",Condition:\"contains\",Value:searchQuery,IsCustomField:false},{FieldName:\"AccountId\",ElementType:\"string\",Condition:\"contains\",Value:accountId,IsCustomField:false}];const data=await getAllGuides(offset,pageSize,filters,\"\");const rowsWithIds=data===null||data===void 0?void 0:(_data$results=data.results)===null||_data$results===void 0?void 0:_data$results.map(item=>({...item,id:item.GuideId}));setFilteredData(rowsWithIds||[]);setTotalCount(data===null||data===void 0?void 0:data._count);};useEffect(()=>{if(Open||accountId){fetchAnnouncements();}},[paginationModel,activeTab,Open,accountId]);// useEffect(() => {\n//     if (accountId) {\n//       fetchAnnouncements();\n//     }\n//   }, [paginationModel, activeTab,accountId]);\nconst handleSearch=()=>{fetchAnnouncements();};useEffect(()=>{if(searchQuery.trim()===\"\"){fetchAnnouncements();}},[searchQuery]);const handleClearSearch=()=>{setSearchQuery(\"\");fetchAnnouncements();};const handleTabChange=(event,newValue)=>{setActiveTab(newValue);setPaginationModel(prev=>({...prev,page:0}));// Reset pagination when the tab changes\n};const getRowSpacing=React.useCallback(params=>{return{top:params.isFirstVisible?0:5,bottom:params.isLastVisible?0:5};},[]);const handleDelete=async()=>{if(guideIdToDelete){try{const response=await DeleteGuideByGuideId(guideIdToDelete);if(response.Success){openSnackbar(`${GuidenametoDelete} ${GuideTypetoDelete} deleted Successfully`,\"success\");await fetchAnnouncements();}else{openSnackbar(response.ErrorMessage,\"error\");}}catch(error){}}setOpenDialog(false);setGuideIdToDelete(null);setGuideNametoDelete(\"\");};const handleCloneSuccess=async()=>{await fetchAnnouncements();};const getNoRowsLabel=()=>{const tabLabels=[\"Active\",\"Inactive\",\"Draft\"];const currentTabLabel=tabLabels[activeTab]||searchText;return`No ${currentTabLabel} ${searchText}s`;};const NoRowsOverlay=()=>/*#__PURE__*/_jsxs(\"div\",{style:{display:\"flex\",alignItems:\"center\",flexDirection:\"column\"},children:[/*#__PURE__*/_jsx(\"span\",{className:\"qadpt-hotsicon\",dangerouslySetInnerHTML:{__html:NoData}}),/*#__PURE__*/_jsx(Typography,{sx:{fontWeight:\"600\"},children:getNoRowsLabel()})]});const handleClosePopup=()=>{//if further ever need of closing popup when clicked outside the popup then please uncomment below code\n// setActiveMenu(null);\n// setSearchText(\"\");\n// onClose();\n};return/*#__PURE__*/_jsxs(\"div\",{id:\"popuplistmenu\",children:[/*#__PURE__*/_jsx(Dialog,{slotProps:{root:{id:\"tooltipdialog\"},backdrop:{sx:{position:\"absolute !important\"}}},open:Open,onClose:handleClosePopup,fullWidth:true,maxWidth:\"md\",className:\"qadpt-gud-menupopup\",children:/*#__PURE__*/_jsxs(DialogContent,{className:\"qadpt-gud-menupopup-content\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-subhead\",id:\"tablesubhead\",children:/*#__PURE__*/_jsxs(\"span\",{className:\"title\",style:{fontWeight:\"600 !important\"},children:[searchText,\"s\"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-head\",id:\"table-head\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"qadpt-titsection\",children:[/*#__PURE__*/_jsx(TextField,{variant:\"outlined\",placeholder:`Search ${title}`,value:searchQuery,onChange:e=>{const newValue=e.target.value;setSearchQuery(newValue);if(newValue===\"\"){handleClearSearch();}},onKeyDown:e=>{if(e.key===\"Enter\"){handleSearch();}},className:\"qadpt-extsearch\",InputProps:{sx:{\"&:hover .MuiOutlinedInput-notchedOutline\":{borderColor:\"#a8a8a8\"},// Prevents color change on hover\n\"&.Mui-focused .MuiOutlinedInput-notchedOutline\":{border:\"1px solid #a8a8a8\"}},startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"search\",onClick:()=>handleSearch(),onMouseDown:event=>event.preventDefault(),children:/*#__PURE__*/_jsx(SearchIcon,{})})}),endAdornment:searchQuery&&/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"clear\",onClick:()=>{setSearchQuery(\"\");handleClearSearch();},children:/*#__PURE__*/_jsx(ClearIcon,{sx:{zoom:\"1.2\"}})})})}}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-right-part\",children:/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onAddClick(searchText),className:\"qadpt-memberButton\",children:[/*#__PURE__*/_jsx(AddIcon,{style:{marginRight:\"8px\",zoom:\"1.4\"}}),/*#__PURE__*/_jsx(\"span\",{children:`Create ${searchText===\"Product Tours\"?\"Tour\":searchText}`})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-tabs-container\",children:/*#__PURE__*/_jsxs(Tabs,{value:activeTab,onChange:handleTabChange,children:[/*#__PURE__*/_jsx(Tab,{label:\"Active\",sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}}),/*#__PURE__*/_jsx(Tab,{label:\"InActive\",sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}}),/*#__PURE__*/_jsx(Tab,{label:\"Draft\",sx:{backgroundColor:\"inherit !important\",border:\"inherit !important\",color:\"inherit !important\",fontSize:\"14px !important\"}})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"qadpt-webgird\",children:/*#__PURE__*/_jsx(DataGrid,{rows:filteredData,columns:columns,getRowId:row=>row.GuideId,getRowSpacing:getRowSpacing,pagination:true,paginationModel:paginationModel,paginationMode:\"server\",onPaginationModelChange:setPaginationModel,rowCount:totalCount,pageSizeOptions:[15,25,50,100],localeText:{MuiTablePagination:{labelRowsPerPage:\"Records Per Page\"},noRowsLabel:getNoRowsLabel()},disableColumnMenu:true,disableRowSelectionOnClick:true,className:\"qadpt-grdcont\",slots:{noRowsOverlay:NoRowsOverlay// Using the 'slots' prop for NoRowsOverlay\n},sx:{\"& .MuiDataGrid-row\":{maxWidth:\"calc(100% - 30px)\",\"--rowBorderColor\":\"transparent\"//   marginTop: \"17px\",\n// marginBottom:\"0 !important\"\n},\"& .MuiDataGrid-cell\":{padding:\"0 15px !important\"},\".MuiTablePagination-toolbar\":{display:\"flex !important\",alignItems:\"baseline !important\"},\".MuiTablePagination-actions button\":{border:\"none !important\",color:\"inherit !important\",backgroundColor:\"initial !important\",\"&:hover\":{backgroundColor:\"initial !important\"// Hover background\n}},\"& .MuiDataGrid-columnHeader\":{background:\"linear-gradient(to right, #f6eeee, #f6eeee)\",padding:\"0 15px !important\",borderRight:\"1px solid #f6eeee\",height:\"40px !important\"},\"& .MuiDataGrid-columnHeaderTitle\":{fontWeight:\"600\"},\"& .MuiDataGrid-filler\":{backgroundColor:\"var(--ext-background)\",\"--rowBorderColor\":\"transparent !important\"},\"& .MuiDataGrid-scrollbarFiller\":{backgroundColor:\"var(--ext-background)\",display:\"none\"}},rowHeight:38})})]})}),/*#__PURE__*/_jsxs(Dialog,{open:openDialog,onClose:()=>setOpenDialog(false),PaperProps:{style:{borderRadius:\"4px\",maxWidth:\"400px\",textAlign:\"center\",maxHeight:\"300px\",boxShadow:\"none\"}},children:[/*#__PURE__*/_jsxs(DialogTitle,{sx:{padding:0},children:[/*#__PURE__*/_jsx(\"div\",{style:{display:\"flex\",justifyContent:\"center\",padding:\"10px\"},children:/*#__PURE__*/_jsx(\"div\",{style:{backgroundColor:\"#e4b6b0\",borderRadius:\"50%\",width:\"40px\",height:\"40px\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},children:/*#__PURE__*/_jsx(DeleteOutlineOutlinedIcon,{sx:{color:\"#F44336\",height:\"20px\",width:\"20px\"}})})}),/*#__PURE__*/_jsxs(Typography,{sx:{fontSize:\"16px !important\",fontWeight:600,padding:\"0 10px\"},children:[\"Delete \",GuideTypetoDelete]})]}),/*#__PURE__*/_jsx(DialogContent,{sx:{padding:\"20px !important\"},children:/*#__PURE__*/_jsxs(DialogContentText,{style:{fontSize:\"14px\",color:\"#000\"},children:[\"The \",/*#__PURE__*/_jsx(\"span\",{style:{fontWeight:\"bold\"},children:GuidenametoDelete}),\" cannot be restored once it is deleted.\"]})}),/*#__PURE__*/_jsxs(DialogActions,{sx:{justifyContent:\"space-between\",borderTop:\"1px solid var(--border-color)\"},children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setOpenDialog(false),sx:{color:\"#9E9E9E\",border:\"1px solid #9E9E9E\",borderRadius:\"4px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleDelete,sx:{backgroundColor:\"var(--error-color)\",color:\"#FFF\",borderRadius:\"4px\",textTransform:\"capitalize\",padding:\"var(--button-padding)\",lineHeight:\"var(--button-lineheight)\"// \"&:hover\": {\n// \tbackgroundColor: \"#D32F2F\",\n// },\n},children:\"Delete\"})]})]}),isCloneDialogOpen&&cloneAnnouncementName&&/*#__PURE__*/_jsx(CloneInteractionDialog,{open:isCloneDialogOpen,handleClose:()=>setIsCloneDialogOpen(false),initialName:cloneAnnouncementName,onCloneSuccess:handleCloneSuccess,name:name})]});};export{editedguide};export default PopupModal;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "IconButton", "Tab", "Tabs", "<PERSON><PERSON><PERSON>", "DialogTitle", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "Typography", "DataGrid", "SearchIcon", "ClearIcon", "getAllGuides", "DeleteGuideByGuideId", "ListEditIcon", "CopyListIcon", "DeleteIconList", "NoData", "DeleteOutlineOutlinedIcon", "AddIcon", "CloneInteractionDialog", "AccountContext", "useSnackbar", "formatDateTime", "useDrawerStore", "useUserSession", "jsxs", "_jsxs", "jsx", "_jsx", "Fragment", "_Fragment", "editedguide", "PopupModal", "_ref", "Open", "onClose", "title", "searchText", "onAddClick", "setCurrentGuideId", "currentGuideId", "state", "setBannerPopup", "setOpenTooltip", "setElementSelected", "setBannerButtonSelected", "selectedTemplateTour", "isUnSavedChanges", "setIsUnSavedChanges", "openWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditClicked", "setActiveMenu", "setSearchText", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "filteredData", "setFilteredData", "isCloneDialogOpen", "setIsCloneDialogOpen", "cloneAnnouncementName", "setCloneAnnouncementName", "guideIdToDelete", "setGuideIdToDelete", "GuidenametoDelete", "setGuideNametoDelete", "GuideTypetoDelete", "setGuideTypetoDelete", "openDialog", "setOpenDialog", "paginationModel", "setPaginationModel", "page", "pageSize", "accountId", "openSnackbar", "totalCount", "setTotalCount", "name", "setName", "handleEditClick", "guide", "targetUrl", "GuideType", "toLowerCase", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "TargetUrl", "window", "location", "href", "GuideId", "open", "handleCopyClick", "announcement", "handleDeleteConfirmation", "guideId", "handleKeyDown", "event", "key", "handleSearch", "columns", "field", "headerName", "hideable", "resizable", "renderCell", "params", "children", "row", "UpdatedDate", "sortable", "arrow", "onClick", "dangerouslySetInnerHTML", "__html", "style", "zoom", "Name", "fetchAnnouncements", "_data$results", "offset", "statusFilter", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "data", "rowsWithIds", "results", "map", "item", "_count", "trim", "handleClearSearch", "handleTabChange", "newValue", "prev", "getRowSpacing", "useCallback", "top", "isFirstVisible", "bottom", "isLastVisible", "handleDelete", "response", "Success", "ErrorMessage", "error", "handleCloneSuccess", "getNoRowsLabel", "tabLabels", "currentTabLabel", "NoRowsOverlay", "display", "alignItems", "flexDirection", "className", "sx", "fontWeight", "handleClosePopup", "slotProps", "root", "backdrop", "position", "fullWidth", "max<PERSON><PERSON><PERSON>", "variant", "placeholder", "value", "onChange", "e", "target", "onKeyDown", "InputProps", "borderColor", "border", "startAdornment", "onMouseDown", "preventDefault", "endAdornment", "marginRight", "label", "backgroundColor", "color", "fontSize", "rows", "getRowId", "pagination", "paginationMode", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "noRowsLabel", "disableColumnMenu", "disableRowSelectionOnClick", "slots", "noRowsOverlay", "padding", "background", "borderRight", "height", "rowHeight", "PaperProps", "borderRadius", "textAlign", "maxHeight", "boxShadow", "justifyContent", "width", "borderTop", "textTransform", "lineHeight", "handleClose", "initialName", "onCloneSuccess"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/PopupList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport {\r\n\t<PERSON><PERSON>,\r\n\tDialogContent,\r\n\tTextField,\r\n\tInputAdornment,\r\n\tIconButton,\r\n\tTab,\r\n\tTabs,\r\n\tTooltip,\r\n\tDialogTitle,\r\n\tDialogContentText,\r\n\tDialogActions,\r\n\tButton,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from \"@mui/x-data-grid\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\n\r\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\r\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport \"./GuideMenuOptions.css\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\r\nimport { AccountContext } from \"../../login/AccountContext\";\r\nimport { useSnackbar } from \"./SnackbarContext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport useUserSession from \"../../../store/userSession\";\r\nlet editedguide: any;\r\ninterface PopupModalProps {\r\n\ttitle: string;\r\n\tOpen: boolean;\r\n\tonClose: () => void;\r\n\tsearchText: string;\r\n\tonAddClick: (searchText: string, isEditing?: boolean, guideDetails?: any) => void;\r\n}\r\ninterface Announcement {\r\n\tAccountId: string;\r\n\tContent: string;\r\n\tCreatedBy: string;\r\n\tCreatedDate: string;\r\n\tFrequency: string;\r\n\tGuideId: string;\r\n\tGuideStatus: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tOrganizationId: string;\r\n\tSegment: string;\r\n\tTargetUrl: string;\r\n\tTemplateId: string;\r\n\tUpdatedBy: string;\r\n\tUpdatedDate: string;\r\n}\r\n\r\nconst PopupModal: React.FC<PopupModalProps> = ({ Open, onClose, title, searchText, onAddClick }) => {\r\n\tconst { setCurrentGuideId, currentGuideId } = useUserSession((state) => state);\r\n\tconst {\r\n\t\tsetBannerPopup,\r\n\t\tsetOpenTooltip,\r\n\t\tsetElementSelected,\r\n\t\tsetBannerButtonSelected,\r\n\t\tselectedTemplateTour,\r\n\t\tisUnSavedChanges,\r\n\t\tsetIsUnSavedChanges,\r\n\t\topenWarning,\r\n\t\tsetOpenWarning,\r\n\t\tsetEditClicked,\r\n\t\tsetActiveMenu,\r\n\t\tsetSearchText,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [activeTab, setActiveTab] = useState(0);\r\n\tconst [searchQuery, setSearchQuery] = useState(\"\");\r\n\tconst [filteredData, setFilteredData] = useState<any[]>([]);\r\n\tconst [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\r\n\tconst [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);\r\n\tconst [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);\r\n\tconst [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\r\n\tconst [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\r\n\tconst [openDialog, setOpenDialog] = useState(false);\r\n\tconst [paginationModel, setPaginationModel] = useState({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [totalCount, setTotalCount] = useState(0);\r\n\tconst [name, setName] = useState(\"Announcement\");\r\n\tconst handleEditClick = (guide: Announcement) => {\r\n\t\tsetBannerButtonSelected(true);\r\n\t\tsetIsUnSavedChanges(false);\r\n\t\tsetEditClicked(true);\r\n\t\tsetOpenWarning(false);\r\n\t\tlet targetUrl = \"\";\r\n\t\teditedguide = true;\r\n\t\tif (\r\n\t\t\tguide.GuideType.toLowerCase() == \"announcement\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tour\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"checklist\"\r\n\t\t) {\r\n\t\t\tif (\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t) {\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t\t// Add a dynamic class to the body\r\n\t\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\t\tif (!styleTag) {\r\n\t\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\t\tlet styles = `\r\n\t\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t`;\r\n\r\n\t\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttargetUrl = `${guide?.TargetUrl}`;\r\n\t\t\tif (targetUrl !== window.location.href) {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t\twindow.open(targetUrl);\r\n\t\t\t} else {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t} else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\t//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\r\n\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\tsetBannerPopup(true);\r\n\t\t}\r\n\t\tif (targetUrl) {\r\n\t\t\t//onAddClick(guide.GuideType, true, guide);\r\n\t\t\twindow.open(targetUrl);\r\n\t\t}\r\n\t};\r\n\t\r\n\tconst handleCopyClick = (announcement: Announcement) => {\r\n\t\tsetCloneAnnouncementName(announcement);\r\n\t\tsetIsCloneDialogOpen(true);\r\n\t};\r\n\tconst handleDeleteConfirmation = (guideId: string) => {\r\n\t\tsetGuideIdToDelete(guideId);\r\n\t\tsetOpenDialog(true);\r\n\t};\r\n\tconst handleKeyDown = (event: React.KeyboardEvent) => {\r\n\t\tif (event.key === \"Enter\") {\r\n\t\t\thandleSearch();\r\n\t\t}\r\n\t};\r\n\tconst columns: GridColDef[] = [\r\n\t\t{\r\n\t\t\tfield: \"Name\",\r\n\t\t\theaderName: \"Name\",\r\n\t\t\t// width: 300,\r\n\t\t\thideable: true,\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"UpdatedDate\",\r\n\t\t\theaderName: \"Last Edited\",\r\n\t\t\t// width: 250,\r\n\t\t\thideable: true,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<span> {`${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`}</span>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: \"Actions\",\r\n\t\t\t// width: 302,\r\n\t\t\thideable: true,\r\n\t\t\tsortable: false,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Edit\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleEditClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: ListEditIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Clone\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleCopyClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CopyListIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Delete\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\thandleDeleteConfirmation(params.row.GuideId);\r\n\t\t\t\t\t\t\t\tsetGuideNametoDelete(params.row.Name);\r\n\t\t\t\t\t\t\t\tsetGuideTypetoDelete(params.row.GuideType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: DeleteIconList }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t];\r\n\r\n\tconst fetchAnnouncements = async () => {\r\n\t\tconst { page, pageSize } = paginationModel;\r\n\t\tconst offset = page * pageSize;\r\n\t\tconst statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\r\n\r\n\t\tconst filters = [\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideType\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: title === \"Product Tours\" ? \"Tour\" : title,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideStatus\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: statusFilter,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"Name\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: searchQuery,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: accountId,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t];\r\n\t\tconst data = await getAllGuides(offset, pageSize, filters, \"\");\r\n\t\tconst rowsWithIds = data?.results?.map((item: any) => ({\r\n\t\t\t...item,\r\n\t\t\tid: item.GuideId,\r\n\t\t}));\r\n\r\n\t\tsetFilteredData(rowsWithIds || []);\r\n\t\tsetTotalCount(data?._count);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (Open || accountId) {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [paginationModel, activeTab, Open, accountId]);\r\n\r\n\t// useEffect(() => {\r\n\t//     if (accountId) {\r\n\t//       fetchAnnouncements();\r\n\t//     }\r\n\t//   }, [paginationModel, activeTab,accountId]);\r\n\r\n\tconst handleSearch = () => {\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (searchQuery.trim() === \"\") {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [searchQuery]);\r\n\tconst handleClearSearch = () => {\r\n\t\tsetSearchQuery(\"\");\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tconst handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n\t\tsetActiveTab(newValue);\r\n\t\tsetPaginationModel((prev) => ({ ...prev, page: 0 })); // Reset pagination when the tab changes\r\n\t};\r\n\tconst getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {\r\n\t\treturn {\r\n\t\t\ttop: params.isFirstVisible ? 0 : 5,\r\n\t\t\tbottom: params.isLastVisible ? 0 : 5,\r\n\t\t};\r\n\t}, []);\r\n\r\n\tconst handleDelete = async () => {\r\n\t\tif (guideIdToDelete) {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await DeleteGuideByGuideId(guideIdToDelete);\r\n\t\t\t\tif (response.Success) {\r\n\t\t\t\t\topenSnackbar(`${GuidenametoDelete} ${GuideTypetoDelete} deleted Successfully`, \"success\");\r\n\t\t\t\t\tawait fetchAnnouncements();\r\n\t\t\t\t} else {\r\n\t\t\t\t\topenSnackbar(response.ErrorMessage, \"error\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t}\r\n\t\tsetOpenDialog(false);\r\n\t\tsetGuideIdToDelete(null);\r\n\t\tsetGuideNametoDelete(\"\");\r\n\t};\r\n\tconst handleCloneSuccess = async () => {\r\n\t\tawait fetchAnnouncements();\r\n\t};\r\n\tconst getNoRowsLabel = () => {\r\n\t\tconst tabLabels = [\"Active\", \"Inactive\", \"Draft\"];\r\n\t\tconst currentTabLabel = tabLabels[activeTab] || searchText;\r\n\t\treturn `No ${currentTabLabel} ${searchText}s`;\r\n\t};\r\n\tconst NoRowsOverlay = () => (\r\n\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n\t\t\t<span\r\n\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: NoData }}\r\n\t\t\t/>\r\n\t\t\t<Typography sx={{ fontWeight: \"600\" }}>{getNoRowsLabel()}</Typography>\r\n\t\t</div>\r\n\t);\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\t//if further ever need of closing popup when clicked outside the popup then please uncomment below code\r\n\t\t// setActiveMenu(null);\r\n\t\t// setSearchText(\"\");\r\n\t\t// onClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div id=\"popuplistmenu\">\r\n\t\t\t<Dialog\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tid: \"tooltipdialog\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbackdrop: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tposition: \"absolute !important\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\topen={Open}\r\n\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\tfullWidth\r\n\t\t\t\tmaxWidth=\"md\"\r\n\t\t\t\tclassName=\"qadpt-gud-menupopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogContent className=\"qadpt-gud-menupopup-content\">\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-subhead\"\r\n\t\t\t\t\t\tid=\"tablesubhead\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tclassName=\"title\"\r\n\t\t\t\t\t\t\tstyle={{ fontWeight: \"600 !important\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{searchText}s\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t{/* <IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\tborderWidth: \"1px\",\r\n\t\t\t\t\t\t\t\tborderStyle: \"solid\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon style={{ color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton> */}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-head\"\r\n\t\t\t\t\t\tid=\"table-head\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-titsection\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tplaceholder={`Search ${title}`}\r\n\t\t\t\t\t\t\t\tvalue={searchQuery}\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newValue = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetSearchQuery(newValue);\r\n\t\t\t\t\t\t\t\t\tif (newValue === \"\") {\r\n\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\r\n\t\t\t\t\t\t\t\t\t\thandleSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-extsearch\"\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"1px solid #a8a8a8\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSearch()}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonMouseDown={(event) => event.preventDefault()}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\tendAdornment: searchQuery && (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"clear\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetSearchQuery(\"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ClearIcon sx={{ zoom: \"1.2\" }} />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={() => onAddClick(searchText)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<AddIcon style={{ marginRight: \"8px\", zoom: \"1.4\" }} />\r\n\t\t\t\t\t\t\t\t\t{/* <i className=\"fal fa-add-plus\"></i> */}\r\n\t\t\t\t\t\t\t\t\t<span>{`Create ${searchText === \"Product Tours\" ? \"Tour\" : searchText}`}</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-tabs-container\">\r\n\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\tvalue={activeTab}\r\n\t\t\t\t\t\t\tonChange={handleTabChange}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"Active\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"InActive\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"Draft\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-webgird\">\r\n\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\trows={filteredData}\r\n\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\tgetRowId={(row) => row.GuideId}\r\n\t\t\t\t\t\t\tgetRowSpacing={getRowSpacing}\r\n\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\t\tonPaginationModelChange={setPaginationModel}\r\n\t\t\t\t\t\t\trowCount={totalCount}\r\n\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\t\tlabelRowsPerPage: \"Records Per Page\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tnoRowsLabel: getNoRowsLabel(),\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableColumnMenu\r\n\t\t\t\t\t\t\tdisableRowSelectionOnClick\r\n\t\t\t\t\t\t\tclassName=\"qadpt-grdcont\"\r\n\t\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\t\tnoRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: \"calc(100% - 30px)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent\",\r\n\t\t\t\t\t\t\t\t\t//   marginTop: \"17px\",\r\n\t\t\t\t\t\t\t\t\t// marginBottom:\"0 !important\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-toolbar\": {\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex !important\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"baseline !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-actions button\": {\r\n\t\t\t\t\t\t\t\t\tborder: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\", // Hover background\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t\t\tbackground: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #f6eeee\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"600\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-filler\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-scrollbarFiller\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\trowHeight={38}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</DialogContent>\r\n\t\t\t</Dialog>\r\n\t\t\t<Dialog\r\n\t\t\t\topen={openDialog}\r\n\t\t\t\tonClose={() => setOpenDialog(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<DeleteOutlineOutlinedIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n\t\t\t\t\t\tDelete {GuideTypetoDelete}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t\tThe <span style={{ fontWeight: \"bold\" }}>{GuidenametoDelete}</span> cannot be restored once it is deleted.\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenDialog(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tCancel\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleDelete}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tDelete\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t\t\t{isCloneDialogOpen && cloneAnnouncementName && (\r\n\t\t\t\t<CloneInteractionDialog\r\n\t\t\t\t\topen={isCloneDialogOpen}\r\n\t\t\t\t\thandleClose={() => setIsCloneDialogOpen(false)}\r\n\t\t\t\t\tinitialName={cloneAnnouncementName}\r\n\t\t\t\t\tonCloneSuccess={handleCloneSuccess}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport { editedguide };\r\nexport default PopupModal;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,UAAU,KAAQ,OAAO,CAC9D,OACCC,MAAM,CACNC,aAAa,CACbC,SAAS,CACTC,cAAc,CACdC,UAAU,CACVC,GAAG,CACHC,IAAI,CACJC,OAAO,CACPC,WAAW,CACXC,iBAAiB,CACjBC,aAAa,CACbC,MAAM,CACNC,UAAU,KACJ,eAAe,CACtB,OAASC,QAAQ,KAAgE,kBAAkB,CACnG,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CAEjD,OAASC,YAAY,CAAEC,oBAAoB,KAAQ,qCAAqC,CACxF,OAASC,YAAY,CAAEC,YAAY,CAAEC,cAAc,CAAEC,MAAM,KAAQ,6BAA6B,CAChG,MAAO,CAAAC,yBAAyB,KAAM,2CAA2C,CACjF,MAAO,wBAAwB,CAC/B,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,sBAAsB,KAAM,mBAAmB,CACtD,OAASC,cAAc,KAAQ,4BAA4B,CAC3D,OAASC,WAAW,KAAQ,mBAAmB,CAE/C,OAASC,cAAc,KAAQ,iDAAiD,CAChF,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CACvD,MAAO,CAAAC,cAAc,KAAM,4BAA4B,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBACxD,GAAI,CAAAC,WAAgB,CA0BpB,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAAsD,IAArD,CAAEC,IAAI,CAAEC,OAAO,CAAEC,KAAK,CAAEC,UAAU,CAAEC,UAAW,CAAC,CAAAL,IAAA,CAC9F,KAAM,CAAEM,iBAAiB,CAAEC,cAAe,CAAC,CAAGhB,cAAc,CAAEiB,KAAK,EAAKA,KAAK,CAAC,CAC9E,KAAM,CACLC,cAAc,CACdC,cAAc,CACdC,kBAAkB,CAClBC,uBAAuB,CACvBC,oBAAoB,CACpBC,gBAAgB,CAChBC,mBAAmB,CACnBC,WAAW,CACXC,cAAc,CACdC,cAAc,CACdC,aAAa,CACbC,aACD,CAAC,CAAG9B,cAAc,CAAEkB,KAAK,EAAKA,KAAK,CAAC,CACpC,KAAM,CAACa,SAAS,CAAEC,YAAY,CAAC,CAAG/D,QAAQ,CAAC,CAAC,CAAC,CAC7C,KAAM,CAACgE,WAAW,CAAEC,cAAc,CAAC,CAAGjE,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACkE,YAAY,CAAEC,eAAe,CAAC,CAAGnE,QAAQ,CAAQ,EAAE,CAAC,CAC3D,KAAM,CAACoE,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGrE,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAACsE,qBAAqB,CAAEC,wBAAwB,CAAC,CAAGvE,QAAQ,CAAsB,IAAI,CAAC,CAC7F,KAAM,CAACwE,eAAe,CAAEC,kBAAkB,CAAC,CAAGzE,QAAQ,CAAgB,IAAI,CAAC,CAC3E,KAAM,CAAC0E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG3E,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC4E,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG7E,QAAQ,CAAC,EAAE,CAAC,CAC9D,KAAM,CAAC8E,UAAU,CAAEC,aAAa,CAAC,CAAG/E,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAACgF,eAAe,CAAEC,kBAAkB,CAAC,CAAGjF,QAAQ,CAAC,CACtDkF,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,EACX,CAAC,CAAC,CACF,KAAM,CAAEC,SAAU,CAAC,CAAGlF,UAAU,CAAC0B,cAAc,CAAC,CAChD,KAAM,CAAEyD,YAAa,CAAC,CAAGxD,WAAW,CAAC,CAAC,CACtC,KAAM,CAACyD,UAAU,CAAEC,aAAa,CAAC,CAAGvF,QAAQ,CAAC,CAAC,CAAC,CAC/C,KAAM,CAACwF,IAAI,CAAEC,OAAO,CAAC,CAAGzF,QAAQ,CAAC,cAAc,CAAC,CAChD,KAAM,CAAA0F,eAAe,CAAIC,KAAmB,EAAK,CAChDtC,uBAAuB,CAAC,IAAI,CAAC,CAC7BG,mBAAmB,CAAC,KAAK,CAAC,CAC1BG,cAAc,CAAC,IAAI,CAAC,CACpBD,cAAc,CAAC,KAAK,CAAC,CACrB,GAAI,CAAAkC,SAAS,CAAG,EAAE,CAClBrD,WAAW,CAAG,IAAI,CAClB,GACCoD,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,EAAI,cAAc,EAC/CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,MAAM,EACxCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,WAAW,CAC5C,CACD,GACCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,EAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,GAAK,QAAQ,EAC1CxC,oBAAoB,GAAK,SAAS,EAClCA,oBAAoB,GAAK,QAAQ,EACjCA,oBAAoB,GAAK,SAAS,CACjC,CACDH,cAAc,CAAC,IAAI,CAAC,CACpBC,kBAAkB,CAAC,IAAI,CAAC,CACxB,GAAI,CAAA2C,QAAQ,CAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB,CAChF,KAAM,CAAAC,WAAW,CAAGF,QAAQ,CAACG,IAAI,CAEjC;AACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAE/C,GAAI,CAACN,QAAQ,CAAE,CACdA,QAAQ,CAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC,CAC1CP,QAAQ,CAACQ,EAAE,CAAG,oBAAoB,CAElC;AACA,GAAI,CAAAC,MAAM,CAAG;AAClB;AACA;AACA;AACA;AACA;AACA,MAAM,CAEDT,QAAQ,CAACU,SAAS,CAAGD,MAAM,CAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC,CACpC,CACD,CACAH,SAAS,CAAG,GAAGD,KAAK,SAALA,KAAK,iBAALA,KAAK,CAAEiB,SAAS,EAAE,CACjC,GAAIhB,SAAS,GAAKiB,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAE,CACvChE,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC,CAChCH,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC,CACvB,CAAC,IAAM,CACN7C,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC,CACjC,CAEA,OACD,CAAC,IAAM,IAAIrB,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,EAAI,QAAQ,EAAIxC,oBAAoB,GAAK,QAAQ,CAAE,CAC1F;AACAP,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC,CAChC9D,cAAc,CAAC,IAAI,CAAC,CACrB,CACA,GAAI0C,SAAS,CAAE,CACd;AACAiB,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC,CACvB,CACD,CAAC,CAED,KAAM,CAAAsB,eAAe,CAAIC,YAA0B,EAAK,CACvD5C,wBAAwB,CAAC4C,YAAY,CAAC,CACtC9C,oBAAoB,CAAC,IAAI,CAAC,CAC3B,CAAC,CACD,KAAM,CAAA+C,wBAAwB,CAAIC,OAAe,EAAK,CACrD5C,kBAAkB,CAAC4C,OAAO,CAAC,CAC3BtC,aAAa,CAAC,IAAI,CAAC,CACpB,CAAC,CACD,KAAM,CAAAuC,aAAa,CAAIC,KAA0B,EAAK,CACrD,GAAIA,KAAK,CAACC,GAAG,GAAK,OAAO,CAAE,CAC1BC,YAAY,CAAC,CAAC,CACf,CACD,CAAC,CACD,KAAM,CAAAC,OAAqB,CAAG,CAC7B,CACCC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,MAAM,CAClB;AACAC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,KACZ,CAAC,CACD,CACCH,KAAK,CAAE,aAAa,CACpBC,UAAU,CAAE,aAAa,CACzB;AACAC,QAAQ,CAAE,IAAI,CACdE,UAAU,CAAGC,MAA4B,eACxC9F,KAAA,SAAA+F,QAAA,EAAM,GAAC,CAAC,GAAGnG,cAAc,CAACkG,MAAM,CAACE,GAAG,CAACC,WAAW,CAAE,YAAY,CAAC,EAAE,EAAO,CACxE,CACDL,SAAS,CAAE,KACZ,CAAC,CACD,CACCH,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,SAAS,CACrB;AACAC,QAAQ,CAAE,IAAI,CACdO,QAAQ,CAAE,KAAK,CACfL,UAAU,CAAGC,MAA4B,eACxC9F,KAAA,CAAAI,SAAA,EAAA2F,QAAA,eACC7F,IAAA,CAAC1B,OAAO,EACP2H,KAAK,MACLzF,KAAK,CAAC,MAAM,CAAAqF,QAAA,cAEZ7F,IAAA,CAAC7B,UAAU,EAAC+H,OAAO,CAAEA,CAAA,GAAM5C,eAAe,CAACsC,MAAM,CAACE,GAAG,CAAE,CAAAD,QAAA,cACtD7F,IAAA,SACCmG,uBAAuB,CAAE,CAAEC,MAAM,CAAEnH,YAAa,CAAE,CAClDoH,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACL,CAAC,cACVtG,IAAA,CAAC1B,OAAO,EACP2H,KAAK,MACLzF,KAAK,CAAC,OAAO,CAAAqF,QAAA,cAEb7F,IAAA,CAAC7B,UAAU,EAAC+H,OAAO,CAAEA,CAAA,GAAMpB,eAAe,CAACc,MAAM,CAACE,GAAG,CAAE,CAAAD,QAAA,cACtD7F,IAAA,SACCmG,uBAAuB,CAAE,CAAEC,MAAM,CAAElH,YAAa,CAAE,CAClDmH,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACL,CAAC,cACVtG,IAAA,CAAC1B,OAAO,EACP2H,KAAK,MACLzF,KAAK,CAAC,QAAQ,CAAAqF,QAAA,cAEd7F,IAAA,CAAC7B,UAAU,EACV+H,OAAO,CAAEA,CAAA,GAAM,CACdlB,wBAAwB,CAACY,MAAM,CAACE,GAAG,CAAClB,OAAO,CAAC,CAC5CrC,oBAAoB,CAACqD,MAAM,CAACE,GAAG,CAACS,IAAI,CAAC,CACrC9D,oBAAoB,CAACmD,MAAM,CAACE,GAAG,CAACrC,SAAS,CAAC,CAC3C,CAAE,CAAAoC,QAAA,cAEF7F,IAAA,SACCmG,uBAAuB,CAAE,CAAEC,MAAM,CAAEjH,cAAe,CAAE,CACpDkH,KAAK,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAE,CACrB,CAAC,CACS,CAAC,CACL,CAAC,EACT,CACF,CACDZ,SAAS,CAAE,KACZ,CAAC,CACD,CAED,KAAM,CAAAc,kBAAkB,CAAG,KAAAA,CAAA,GAAY,KAAAC,aAAA,CACtC,KAAM,CAAE3D,IAAI,CAAEC,QAAS,CAAC,CAAGH,eAAe,CAC1C,KAAM,CAAA8D,MAAM,CAAG5D,IAAI,CAAGC,QAAQ,CAC9B,KAAM,CAAA4D,YAAY,CAAGjF,SAAS,GAAK,CAAC,CAAG,QAAQ,CAAGA,SAAS,GAAK,CAAC,CAAG,UAAU,CAAG,OAAO,CAExF,KAAM,CAAAkF,OAAO,CAAG,CACf,CACCC,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAExG,KAAK,GAAK,eAAe,CAAG,MAAM,CAAGA,KAAK,CACjDyG,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,aAAa,CACxBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,QAAQ,CACnBC,KAAK,CAAEL,YAAY,CACnBM,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,MAAM,CACjBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAEpF,WAAW,CAClBqF,aAAa,CAAE,KAChB,CAAC,CACD,CACCJ,SAAS,CAAE,WAAW,CACtBC,WAAW,CAAE,QAAQ,CACrBC,SAAS,CAAE,UAAU,CACrBC,KAAK,CAAEhE,SAAS,CAChBiE,aAAa,CAAE,KAChB,CAAC,CACD,CACD,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAnI,YAAY,CAAC2H,MAAM,CAAE3D,QAAQ,CAAE6D,OAAO,CAAE,EAAE,CAAC,CAC9D,KAAM,CAAAO,WAAW,CAAGD,IAAI,SAAJA,IAAI,kBAAAT,aAAA,CAAJS,IAAI,CAAEE,OAAO,UAAAX,aAAA,iBAAbA,aAAA,CAAeY,GAAG,CAAEC,IAAS,GAAM,CACtD,GAAGA,IAAI,CACPnD,EAAE,CAAEmD,IAAI,CAAC1C,OACV,CAAC,CAAC,CAAC,CAEH7C,eAAe,CAACoF,WAAW,EAAI,EAAE,CAAC,CAClChE,aAAa,CAAC+D,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEK,MAAM,CAAC,CAC5B,CAAC,CAED1J,SAAS,CAAC,IAAM,CACf,GAAIyC,IAAI,EAAI0C,SAAS,CAAE,CACtBwD,kBAAkB,CAAC,CAAC,CACrB,CACD,CAAC,CAAE,CAAC5D,eAAe,CAAElB,SAAS,CAAEpB,IAAI,CAAE0C,SAAS,CAAC,CAAC,CAEjD;AACA;AACA;AACA;AACA;AAEA,KAAM,CAAAqC,YAAY,CAAGA,CAAA,GAAM,CAC1BmB,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAED3I,SAAS,CAAC,IAAM,CACf,GAAI+D,WAAW,CAAC4F,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC9BhB,kBAAkB,CAAC,CAAC,CACrB,CACD,CAAC,CAAE,CAAC5E,WAAW,CAAC,CAAC,CACjB,KAAM,CAAA6F,iBAAiB,CAAGA,CAAA,GAAM,CAC/B5F,cAAc,CAAC,EAAE,CAAC,CAClB2E,kBAAkB,CAAC,CAAC,CACrB,CAAC,CAED,KAAM,CAAAkB,eAAe,CAAGA,CAACvC,KAA2B,CAAEwC,QAAgB,GAAK,CAC1EhG,YAAY,CAACgG,QAAQ,CAAC,CACtB9E,kBAAkB,CAAE+E,IAAI,GAAM,CAAE,GAAGA,IAAI,CAAE9E,IAAI,CAAE,CAAE,CAAC,CAAC,CAAC,CAAE;AACvD,CAAC,CACD,KAAM,CAAA+E,aAAa,CAAGlK,KAAK,CAACmK,WAAW,CAAElC,MAA4B,EAAK,CACzE,MAAO,CACNmC,GAAG,CAAEnC,MAAM,CAACoC,cAAc,CAAG,CAAC,CAAG,CAAC,CAClCC,MAAM,CAAErC,MAAM,CAACsC,aAAa,CAAG,CAAC,CAAG,CACpC,CAAC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAC,YAAY,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI/F,eAAe,CAAE,CACpB,GAAI,CACH,KAAM,CAAAgG,QAAQ,CAAG,KAAM,CAAApJ,oBAAoB,CAACoD,eAAe,CAAC,CAC5D,GAAIgG,QAAQ,CAACC,OAAO,CAAE,CACrBpF,YAAY,CAAC,GAAGX,iBAAiB,IAAIE,iBAAiB,uBAAuB,CAAE,SAAS,CAAC,CACzF,KAAM,CAAAgE,kBAAkB,CAAC,CAAC,CAC3B,CAAC,IAAM,CACNvD,YAAY,CAACmF,QAAQ,CAACE,YAAY,CAAE,OAAO,CAAC,CAC7C,CACD,CAAE,MAAOC,KAAK,CAAE,CAAC,CAClB,CACA5F,aAAa,CAAC,KAAK,CAAC,CACpBN,kBAAkB,CAAC,IAAI,CAAC,CACxBE,oBAAoB,CAAC,EAAE,CAAC,CACzB,CAAC,CACD,KAAM,CAAAiG,kBAAkB,CAAG,KAAAA,CAAA,GAAY,CACtC,KAAM,CAAAhC,kBAAkB,CAAC,CAAC,CAC3B,CAAC,CACD,KAAM,CAAAiC,cAAc,CAAGA,CAAA,GAAM,CAC5B,KAAM,CAAAC,SAAS,CAAG,CAAC,QAAQ,CAAE,UAAU,CAAE,OAAO,CAAC,CACjD,KAAM,CAAAC,eAAe,CAAGD,SAAS,CAAChH,SAAS,CAAC,EAAIjB,UAAU,CAC1D,MAAO,MAAMkI,eAAe,IAAIlI,UAAU,GAAG,CAC9C,CAAC,CACD,KAAM,CAAAmI,aAAa,CAAGA,CAAA,gBACrB9I,KAAA,QAAKuG,KAAK,CAAE,CAAEwC,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,aAAa,CAAE,QAAS,CAAE,CAAAlD,QAAA,eAC9E7F,IAAA,SACCgJ,SAAS,CAAC,gBAAgB,CAC1B7C,uBAAuB,CAAE,CAAEC,MAAM,CAAEhH,MAAO,CAAE,CAC5C,CAAC,cACFY,IAAA,CAACrB,UAAU,EAACsK,EAAE,CAAE,CAAEC,UAAU,CAAE,KAAM,CAAE,CAAArD,QAAA,CAAE4C,cAAc,CAAC,CAAC,CAAa,CAAC,EAClE,CACL,CAED,KAAM,CAAAU,gBAAgB,CAAGA,CAAA,GAAM,CAC9B;AACA;AACA;AACA;AAAA,CACA,CAED,mBACCrJ,KAAA,QAAKqE,EAAE,CAAC,eAAe,CAAA0B,QAAA,eACtB7F,IAAA,CAACjC,MAAM,EACNqL,SAAS,CAAE,CACVC,IAAI,CAAE,CACLlF,EAAE,CAAE,eACL,CAAC,CACDmF,QAAQ,CAAE,CACTL,EAAE,CAAE,CACHM,QAAQ,CAAE,qBACX,CACD,CACD,CAAE,CACF1E,IAAI,CAAEvE,IAAK,CACXC,OAAO,CAAE4I,gBAAiB,CAC1BK,SAAS,MACTC,QAAQ,CAAC,IAAI,CACbT,SAAS,CAAC,qBAAqB,CAAAnD,QAAA,cAE/B/F,KAAA,CAAC9B,aAAa,EAACgL,SAAS,CAAC,6BAA6B,CAAAnD,QAAA,eACrD7F,IAAA,QACCgJ,SAAS,CAAC,eAAe,CACzB7E,EAAE,CAAC,cAAc,CAAA0B,QAAA,cAEjB/F,KAAA,SACCkJ,SAAS,CAAC,OAAO,CACjB3C,KAAK,CAAE,CAAE6C,UAAU,CAAE,gBAAiB,CAAE,CAAArD,QAAA,EAEvCpF,UAAU,CAAC,GACb,EAAM,CAAC,CAaH,CAAC,cACNT,IAAA,QACCgJ,SAAS,CAAC,YAAY,CACtB7E,EAAE,CAAC,YAAY,CAAA0B,QAAA,cAEf/F,KAAA,QAAKkJ,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,eAChC7F,IAAA,CAAC/B,SAAS,EACTyL,OAAO,CAAC,UAAU,CAClBC,WAAW,CAAE,UAAUnJ,KAAK,EAAG,CAC/BoJ,KAAK,CAAEhI,WAAY,CACnBiI,QAAQ,CAAGC,CAAC,EAAK,CAChB,KAAM,CAAAnC,QAAQ,CAAGmC,CAAC,CAACC,MAAM,CAACH,KAAK,CAC/B/H,cAAc,CAAC8F,QAAQ,CAAC,CACxB,GAAIA,QAAQ,GAAK,EAAE,CAAE,CACpBF,iBAAiB,CAAC,CAAC,CACpB,CACD,CAAE,CACFuC,SAAS,CAAGF,CAAC,EAAK,CACjB,GAAIA,CAAC,CAAC1E,GAAG,GAAK,OAAO,CAAE,CACtBC,YAAY,CAAC,CAAC,CACf,CACD,CAAE,CACF2D,SAAS,CAAC,iBAAiB,CAC3BiB,UAAU,CAAE,CACXhB,EAAE,CAAE,CACH,0CAA0C,CAAE,CAAEiB,WAAW,CAAE,SAAU,CAAC,CAAE;AACxE,gDAAgD,CAAE,CAAEC,MAAM,CAAE,mBAAoB,CACjF,CAAC,CACDC,cAAc,cACbpK,IAAA,CAAC9B,cAAc,EAACqL,QAAQ,CAAC,OAAO,CAAA1D,QAAA,cAC/B7F,IAAA,CAAC7B,UAAU,EACV,aAAW,QAAQ,CACnB+H,OAAO,CAAEA,CAAA,GAAMb,YAAY,CAAC,CAAE,CAC9BgF,WAAW,CAAGlF,KAAK,EAAKA,KAAK,CAACmF,cAAc,CAAC,CAAE,CAAAzE,QAAA,cAE/C7F,IAAA,CAACnB,UAAU,GAAE,CAAC,CACH,CAAC,CACE,CAChB,CACD0L,YAAY,CAAE3I,WAAW,eACxB5B,IAAA,CAAC9B,cAAc,EAACqL,QAAQ,CAAC,KAAK,CAAA1D,QAAA,cAC7B7F,IAAA,CAAC7B,UAAU,EACV,aAAW,OAAO,CAClB+H,OAAO,CAAEA,CAAA,GAAM,CACdrE,cAAc,CAAC,EAAE,CAAC,CAClB4F,iBAAiB,CAAC,CAAC,CACpB,CAAE,CAAA5B,QAAA,cAEF7F,IAAA,CAAClB,SAAS,EAACmK,EAAE,CAAE,CAAE3C,IAAI,CAAE,KAAM,CAAE,CAAE,CAAC,CACvB,CAAC,CACE,CAElB,CAAE,CACF,CAAC,cACFtG,IAAA,QAAKgJ,SAAS,CAAC,kBAAkB,CAAAnD,QAAA,cAChC/F,KAAA,WACCoG,OAAO,CAAEA,CAAA,GAAMxF,UAAU,CAACD,UAAU,CAAE,CACtCuI,SAAS,CAAC,oBAAoB,CAAAnD,QAAA,eAE9B7F,IAAA,CAACV,OAAO,EAAC+G,KAAK,CAAE,CAAEmE,WAAW,CAAE,KAAK,CAAElE,IAAI,CAAE,KAAM,CAAE,CAAE,CAAC,cAEvDtG,IAAA,SAAA6F,QAAA,CAAO,UAAUpF,UAAU,GAAK,eAAe,CAAG,MAAM,CAAGA,UAAU,EAAE,CAAO,CAAC,EACxE,CAAC,CACL,CAAC,EACF,CAAC,CACF,CAAC,cAENT,IAAA,QAAKgJ,SAAS,CAAC,sBAAsB,CAAAnD,QAAA,cACpC/F,KAAA,CAACzB,IAAI,EACJuL,KAAK,CAAElI,SAAU,CACjBmI,QAAQ,CAAEnC,eAAgB,CAAA7B,QAAA,eAE1B7F,IAAA,CAAC5B,GAAG,EACHqM,KAAK,CAAC,QAAQ,CACdxB,EAAE,CAAE,CACHyB,eAAe,CAAE,oBAAoB,CACrCP,MAAM,CAAE,oBAAoB,CAC5BQ,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,cACF5K,IAAA,CAAC5B,GAAG,EACHqM,KAAK,CAAC,UAAU,CAChBxB,EAAE,CAAE,CACHyB,eAAe,CAAE,oBAAoB,CACrCP,MAAM,CAAE,oBAAoB,CAC5BQ,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,cACF5K,IAAA,CAAC5B,GAAG,EACHqM,KAAK,CAAC,OAAO,CACbxB,EAAE,CAAE,CACHyB,eAAe,CAAE,oBAAoB,CACrCP,MAAM,CAAE,oBAAoB,CAC5BQ,KAAK,CAAE,oBAAoB,CAC3BC,QAAQ,CAAE,iBACX,CAAE,CACF,CAAC,EACG,CAAC,CACH,CAAC,cAEN5K,IAAA,QAAKgJ,SAAS,CAAC,eAAe,CAAAnD,QAAA,cAC7B7F,IAAA,CAACpB,QAAQ,EACRiM,IAAI,CAAE/I,YAAa,CACnBwD,OAAO,CAAEA,OAAQ,CACjBwF,QAAQ,CAAGhF,GAAG,EAAKA,GAAG,CAAClB,OAAQ,CAC/BiD,aAAa,CAAEA,aAAc,CAC7BkD,UAAU,MACVnI,eAAe,CAAEA,eAAgB,CACjCoI,cAAc,CAAC,QAAQ,CACvBC,uBAAuB,CAAEpI,kBAAmB,CAC5CqI,QAAQ,CAAEhI,UAAW,CACrBiI,eAAe,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,CACnCC,UAAU,CAAE,CACXC,kBAAkB,CAAE,CACnBC,gBAAgB,CAAE,kBACnB,CAAC,CACDC,WAAW,CAAE9C,cAAc,CAAC,CAC7B,CAAE,CACF+C,iBAAiB,MACjBC,0BAA0B,MAC1BzC,SAAS,CAAC,eAAe,CACzB0C,KAAK,CAAE,CACNC,aAAa,CAAE/C,aAAe;AAC/B,CAAE,CACFK,EAAE,CAAE,CACH,oBAAoB,CAAE,CACrBQ,QAAQ,CAAE,mBAAmB,CAC7B,kBAAkB,CAAE,aACpB;AACA;AACD,CAAC,CACD,qBAAqB,CAAE,CACtBmC,OAAO,CAAE,mBACV,CAAC,CACD,6BAA6B,CAAE,CAC9B/C,OAAO,CAAE,iBAAiB,CAC1BC,UAAU,CAAE,qBACb,CAAC,CACD,oCAAoC,CAAE,CACrCqB,MAAM,CAAE,iBAAiB,CACzBQ,KAAK,CAAE,oBAAoB,CAC3BD,eAAe,CAAE,oBAAoB,CACrC,SAAS,CAAE,CACVA,eAAe,CAAE,oBAAsB;AACxC,CACD,CAAC,CACD,6BAA6B,CAAE,CAC9BmB,UAAU,CAAE,6CAA6C,CACzDD,OAAO,CAAE,mBAAmB,CAC5BE,WAAW,CAAE,mBAAmB,CAChCC,MAAM,CAAE,iBACT,CAAC,CACD,kCAAkC,CAAE,CACnC7C,UAAU,CAAE,KACb,CAAC,CACD,uBAAuB,CAAE,CACxBwB,eAAe,CAAE,uBAAuB,CACxC,kBAAkB,CAAE,wBACrB,CAAC,CACD,gCAAgC,CAAE,CACjCA,eAAe,CAAE,uBAAuB,CACxC7B,OAAO,CAAE,MACV,CACD,CAAE,CACFmD,SAAS,CAAE,EAAG,CACd,CAAC,CACE,CAAC,EACQ,CAAC,CACT,CAAC,cACTlM,KAAA,CAAC/B,MAAM,EACN8G,IAAI,CAAEnC,UAAW,CACjBnC,OAAO,CAAEA,CAAA,GAAMoC,aAAa,CAAC,KAAK,CAAE,CACpCsJ,UAAU,CAAE,CACX5F,KAAK,CAAE,CACN6F,YAAY,CAAE,KAAK,CACnBzC,QAAQ,CAAE,OAAO,CACjB0C,SAAS,CAAE,QAAQ,CACnBC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,MACZ,CACD,CAAE,CAAAxG,QAAA,eAEF/F,KAAA,CAACvB,WAAW,EAAC0K,EAAE,CAAE,CAAE2C,OAAO,CAAE,CAAE,CAAE,CAAA/F,QAAA,eAC/B7F,IAAA,QAAKqG,KAAK,CAAE,CAAEwC,OAAO,CAAE,MAAM,CAAEyD,cAAc,CAAE,QAAQ,CAAEV,OAAO,CAAE,MAAO,CAAE,CAAA/F,QAAA,cAC1E7F,IAAA,QACCqG,KAAK,CAAE,CACNqE,eAAe,CAAE,SAAS,CAC1BwB,YAAY,CAAE,KAAK,CACnBK,KAAK,CAAE,MAAM,CACbR,MAAM,CAAE,MAAM,CACdlD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBwD,cAAc,CAAE,QACjB,CAAE,CAAAzG,QAAA,cAEF7F,IAAA,CAACX,yBAAyB,EAAC4J,EAAE,CAAE,CAAE0B,KAAK,CAAE,SAAS,CAAEoB,MAAM,CAAE,MAAM,CAAEQ,KAAK,CAAE,MAAO,CAAE,CAAE,CAAC,CAClF,CAAC,CACF,CAAC,cACNzM,KAAA,CAACnB,UAAU,EAACsK,EAAE,CAAE,CAAE2B,QAAQ,CAAE,iBAAiB,CAAE1B,UAAU,CAAE,GAAG,CAAE0C,OAAO,CAAE,QAAS,CAAE,CAAA/F,QAAA,EAAC,SAC7E,CAACrD,iBAAiB,EACd,CAAC,EACD,CAAC,cAEdxC,IAAA,CAAChC,aAAa,EAACiL,EAAE,CAAE,CAAE2C,OAAO,CAAE,iBAAkB,CAAE,CAAA/F,QAAA,cACjD/F,KAAA,CAACtB,iBAAiB,EAAC6H,KAAK,CAAE,CAAEuE,QAAQ,CAAE,MAAM,CAAED,KAAK,CAAE,MAAO,CAAE,CAAA9E,QAAA,EAAC,MAC1D,cAAA7F,IAAA,SAAMqG,KAAK,CAAE,CAAE6C,UAAU,CAAE,MAAO,CAAE,CAAArD,QAAA,CAAEvD,iBAAiB,CAAO,CAAC,0CACpE,EAAmB,CAAC,CACN,CAAC,cAEhBxC,KAAA,CAACrB,aAAa,EAACwK,EAAE,CAAE,CAAEqD,cAAc,CAAE,eAAe,CAAEE,SAAS,CAAE,+BAAgC,CAAE,CAAA3G,QAAA,eAClG7F,IAAA,CAACtB,MAAM,EACNwH,OAAO,CAAEA,CAAA,GAAMvD,aAAa,CAAC,KAAK,CAAE,CACpCsG,EAAE,CAAE,CACH0B,KAAK,CAAE,SAAS,CAChBR,MAAM,CAAE,mBAAmB,CAC3B+B,YAAY,CAAE,KAAK,CACnBO,aAAa,CAAE,YAAY,CAC3Bb,OAAO,CAAE,uBAAuB,CAChCc,UAAU,CAAE,0BACb,CAAE,CAAA7G,QAAA,CACF,QAED,CAAQ,CAAC,cACT7F,IAAA,CAACtB,MAAM,EACNwH,OAAO,CAAEiC,YAAa,CACtBc,EAAE,CAAE,CACHyB,eAAe,CAAE,oBAAoB,CACrCC,KAAK,CAAE,MAAM,CACbuB,YAAY,CAAE,KAAK,CACnBO,aAAa,CAAE,YAAY,CAC3Bb,OAAO,CAAE,uBAAuB,CAChCc,UAAU,CAAE,0BACZ;AACA;AACA;AACD,CAAE,CAAA7G,QAAA,CACF,QAED,CAAQ,CAAC,EACK,CAAC,EACT,CAAC,CACR7D,iBAAiB,EAAIE,qBAAqB,eAC1ClC,IAAA,CAACT,sBAAsB,EACtBsF,IAAI,CAAE7C,iBAAkB,CACxB2K,WAAW,CAAEA,CAAA,GAAM1K,oBAAoB,CAAC,KAAK,CAAE,CAC/C2K,WAAW,CAAE1K,qBAAsB,CACnC2K,cAAc,CAAErE,kBAAmB,CACnCpF,IAAI,CAAEA,IAAK,CACX,CACD,EACG,CAAC,CAER,CAAC,CACD,OAASjD,WAAW,EACpB,cAAe,CAAAC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}