{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\HotspotPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef, useCallback } from \"react\";\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HotspotPreview = ({\n  anchorEl,\n  guideStep,\n  title,\n  text,\n  imageUrl,\n  onClose,\n  onPrevious,\n  onContinue,\n  videoUrl,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData,\n  hotspotProperties,\n  handleHotspotHover,\n  handleHotspotClick,\n  isHotspotPopupOpen,\n  showHotspotenduser\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide35, _savedGuideData$Guide36, _savedGuideData$Guide37;\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    toolTipGuideMetaData,\n    elementSelected,\n    axisData,\n    tooltipXaxis,\n    tooltipYaxis,\n    setOpenTooltip,\n    openTooltip,\n    pulseAnimationsH,\n    hotspotGuideMetaData,\n    selectedTemplateTour,\n    selectedOption,\n    ProgressColor\n  } = useDrawerStore(state => state);\n  const [targetElement, setTargetElement] = useState(null);\n  // State to track if the popover should be shown\n  // State for popup visibility is managed through openTooltip\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [dynamicWidth, setDynamicWidth] = useState(null);\n  const [hotspotSize, setHotspotSize] = useState(30); // Track hotspot size for dynamic popup positioning\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n  let hotspot;\n  const getElementByXPath = xpath => {\n    if (!xpath) return null;\n    const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n    const node = result.singleNodeValue;\n    if (node instanceof HTMLElement) {\n      return node;\n    } else if (node !== null && node !== void 0 && node.parentElement) {\n      return node.parentElement; // Return parent if it's a text node\n    } else {\n      return null;\n    }\n  };\n\n  // Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\n  const smoothScrollTo = (element, targetTop, duration = 300) => {\n    // Ensure targetTop is within valid bounds\n    const maxScroll = element.scrollHeight - element.clientHeight;\n    const clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\n\n    // Method 1: Try native smooth scrolling first\n    try {\n      if ('scrollTo' in element && typeof element.scrollTo === 'function') {\n        element.scrollTo({\n          top: clampedTargetTop,\n          behavior: 'smooth'\n        });\n        return;\n      }\n    } catch (error) {\n      console.log(\"Native smooth scrollTo failed, trying animation fallback\");\n    }\n\n    // Method 2: Manual animation fallback\n    try {\n      const startTop = element.scrollTop;\n      const distance = clampedTargetTop - startTop;\n      const startTime = performance.now();\n      const animateScroll = currentTime => {\n        const elapsed = currentTime - startTime;\n        const progress = Math.min(elapsed / duration, 1);\n\n        // Easing function for smooth animation\n        const easeInOutCubic = t => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n        const easedProgress = easeInOutCubic(progress);\n        element.scrollTop = startTop + distance * easedProgress;\n        if (progress < 1) {\n          requestAnimationFrame(animateScroll);\n        }\n      };\n      requestAnimationFrame(animateScroll);\n    } catch (error) {\n      console.log(\"RequestAnimationFrame failed, using direct assignment\");\n      // Method 3: Direct assignment as final fallback\n      element.scrollTop = clampedTargetTop;\n    }\n  };\n\n  // Enhanced cross-environment scrolling function\n  const universalScrollTo = (element, options) => {\n    const isWindow = element === window;\n    const targetElement = isWindow ? document.documentElement : element;\n\n    // Method 1: Try native scrollTo if available and not blocked\n    if (!isWindow && 'scrollTo' in element && typeof element.scrollTo === 'function') {\n      try {\n        element.scrollTo(options);\n        return true;\n      } catch (error) {\n        console.log(\"Native scrollTo blocked or failed:\", error);\n      }\n    }\n\n    // Method 2: Try window.scrollTo for window element\n    if (isWindow && options.behavior === 'smooth') {\n      try {\n        window.scrollTo(options);\n        return true;\n      } catch (error) {\n        console.log(\"Window scrollTo failed:\", error);\n      }\n    }\n\n    // Method 3: Try smooth scrolling with custom animation\n    if (options.behavior === 'smooth' && options.top !== undefined) {\n      try {\n        smoothScrollTo(targetElement, options.top);\n        return true;\n      } catch (error) {\n        console.log(\"Smooth scroll animation failed:\", error);\n      }\n    }\n\n    // Method 4: Direct property assignment (final fallback)\n    try {\n      if (options.top !== undefined) {\n        targetElement.scrollTop = options.top;\n      }\n      if (options.left !== undefined) {\n        targetElement.scrollLeft = options.left;\n      }\n      return true;\n    } catch (error) {\n      console.log(\"Direct property assignment failed:\", error);\n      return false;\n    }\n  };\n\n  // Enhanced reusable element polling function with exponential backoff and better timing\n  const pollForElement = useCallback((xpath, possibleElementPath, onElementFound, maxAttempts = 30, initialIntervalMs = 16,\n  // Start with one frame\n  description = \"Element\", onTimeout) => {\n    let attempts = 0;\n    let currentInterval = initialIntervalMs;\n    let timeoutId;\n    const poll = () => {\n      attempts++;\n      console.log(`🔍 ${description} polling attempt ${attempts}/${maxAttempts}`);\n\n      // Try primary xpath first, then fallback\n      let element = getElementByXPath(xpath);\n      if (!element && possibleElementPath) {\n        element = getElementByXPath(possibleElementPath);\n      }\n      if (element) {\n        console.log(`✅ ${description} found after ${attempts} attempts`);\n        onElementFound(element);\n        return;\n      }\n      if (attempts >= maxAttempts) {\n        console.log(`❌ ${description} not found after ${maxAttempts} attempts`);\n        if (onTimeout) {\n          onTimeout();\n        }\n        return;\n      }\n\n      // Exponential backoff with jitter to avoid thundering herd\n      const jitter = Math.random() * 0.1 * currentInterval;\n      const nextInterval = Math.min(currentInterval * 1.2 + jitter, 200); // Cap at 200ms\n      currentInterval = nextInterval;\n      timeoutId = setTimeout(poll, currentInterval);\n    };\n\n    // Start polling\n    poll();\n\n    // Return cleanup function\n    const cleanup = () => {\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n\n    // Return cleanup function\n    return cleanup;\n  }, []);\n  const scrollToTargetElement = useCallback(async (targetElement, placement = \"top\") => {\n    if (!targetElement) {\n      console.log(\"ScrollToTargetElement: No target element provided\");\n      return;\n    }\n    console.log(\"🎯 Starting enhanced auto-scroll to target hotspot element:\", {\n      element: targetElement,\n      tagName: targetElement.tagName,\n      className: targetElement.className,\n      id: targetElement.id,\n      placement: placement\n    });\n    try {\n      // Queue scroll operations to prevent conflicts\n      const scrollOperationQueue = Promise.resolve().then(async () => {\n        const rect = targetElement.getBoundingClientRect();\n        const viewportHeight = window.innerHeight;\n        const viewportWidth = window.innerWidth;\n\n        // Calculate optimal scroll position based on placement and viewport\n        let targetScrollTop = window.scrollY;\n        let targetScrollLeft = window.scrollX;\n\n        // Enhanced positioning logic based on hotspot placement\n        switch (placement) {\n          case \"top\":\n            // Position element in lower third of viewport to leave room for hotspot above\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.7;\n            break;\n          case \"bottom\":\n            // Position element in upper third of viewport to leave room for hotspot below\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.3;\n            break;\n          case \"left\":\n            // Position element towards right side to leave room for hotspot on left\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n            targetScrollLeft = window.scrollX + rect.left - viewportWidth * 0.7;\n            break;\n          case \"right\":\n            // Position element towards left side to leave room for hotspot on right\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n            targetScrollLeft = window.scrollX + rect.left - viewportWidth * 0.3;\n            break;\n          default:\n            // Default: center the element vertically\n            targetScrollTop = window.scrollY + rect.top - viewportHeight * 0.5;\n        }\n\n        // Ensure scroll positions are within valid bounds\n        const maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\n        const maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\n        targetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\n        targetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\n        console.log(\"📍 Calculated hotspot scroll position:\", {\n          targetScrollTop,\n          targetScrollLeft,\n          currentScrollY: window.scrollY,\n          currentScrollX: window.scrollX,\n          elementRect: rect\n        });\n\n        // Perform the scroll with multiple fallback methods\n        let scrollSuccess = false;\n\n        // Method 1: Try smooth scrolling\n        try {\n          scrollSuccess = universalScrollTo(window, {\n            top: targetScrollTop,\n            left: targetScrollLeft,\n            behavior: 'smooth'\n          });\n          console.log(\"✅ Universal hotspot scroll success:\", scrollSuccess);\n        } catch (error) {\n          console.log(\"❌ Universal hotspot scroll failed:\", error);\n        }\n\n        // Method 2: Fallback to immediate scroll if smooth scroll failed\n        if (!scrollSuccess) {\n          try {\n            window.scrollTo(targetScrollLeft, targetScrollTop);\n            scrollSuccess = true;\n            console.log(\"✅ Fallback hotspot scroll successful\");\n          } catch (error) {\n            console.log(\"❌ Fallback hotspot scroll failed:\", error);\n          }\n        }\n\n        // Method 3: Final fallback using direct property assignment\n        if (!scrollSuccess) {\n          try {\n            document.documentElement.scrollTop = targetScrollTop;\n            document.documentElement.scrollLeft = targetScrollLeft;\n            document.body.scrollTop = targetScrollTop;\n            document.body.scrollLeft = targetScrollLeft;\n            console.log(\"✅ Direct property assignment completed\");\n          } catch (error) {\n            console.log(\"❌ Direct property assignment failed:\", error);\n          }\n        }\n\n        // Small delay to allow scroll to complete\n        await new Promise(resolve => setTimeout(resolve, 100));\n        console.log(\"🏁 Auto-scroll operation completed\");\n      });\n      await scrollOperationQueue;\n    } catch (error) {\n      console.error(\"❌ ScrollToTargetElement error:\", error);\n    }\n  }, [smoothScrollTo, universalScrollTo]);\n  let xpath;\n  if (savedGuideData) xpath = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.ElementPath;\n  const getElementPosition = xpath => {\n    const element = getElementByXPath(xpath || \"\");\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        //+ window.scrollY + yOffset, // Adjust for vertical scroll\n        left: rect.left // + window.scrollX + xOffset, // Adjust for horizontal scroll\n      };\n    }\n    return null;\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  // Function to calculate popup position below the hotspot\n  const calculatePopupPosition = (elementRect, hotspotSize, xOffset, yOffset) => {\n    const hotspotLeft = elementRect.x + xOffset;\n    const hotspotTop = elementRect.y + yOffset;\n\n    // Position popup below the hotspot for better user experience\n    const dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\n    const dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\n\n    return {\n      top: hotspotTop + window.scrollY + dynamicOffsetY,\n      left: hotspotLeft + window.scrollX + dynamicOffsetX\n    };\n  };\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        // Account for scrolling\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [xpath]);\n  useEffect(() => {\n    if (typeof window !== undefined) {\n      const position = getElementPosition(xpath || \"\");\n      if (position) {\n        setPopupPosition(position);\n      }\n    }\n  }, [xpath]);\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    // setTargetElement(element);\n    if (element) {}\n  }, [savedGuideData]);\n  useEffect(() => {\n    var _guideStep;\n    const element = getElementByXPath(guideStep === null || guideStep === void 0 ? void 0 : (_guideStep = guideStep[currentStep - 1]) === null || _guideStep === void 0 ? void 0 : _guideStep.ElementPath);\n    setTargetElement(element);\n    if (element) {\n      element.style.backgroundColor = \"red !important\";\n\n      // Update popup position when target element changes\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [guideStep, currentStep]);\n\n  // Smart auto-scroll for current hotspot step changes - only when element is not visible\n  useEffect(() => {\n    var _savedGuideData$Guide3;\n    const currentStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : _savedGuideData$Guide3[currentStep - 1];\n    if (!(currentStepData !== null && currentStepData !== void 0 && currentStepData.ElementPath)) {\n      return;\n    }\n\n    // Small delay to allow DOM to settle\n    const timeoutId = setTimeout(() => {\n      const currentElement = getElementByXPath(currentStepData.ElementPath || \"\");\n      if (currentElement) {\n        const rect = currentElement.getBoundingClientRect();\n        const viewportHeight = window.innerHeight;\n        const viewportWidth = window.innerWidth;\n\n        // Check if element is completely out of view\n        const isCompletelyOutOfView = rect.bottom < 0 ||\n        // Completely above viewport\n        rect.top > viewportHeight ||\n        // Completely below viewport\n        rect.right < 0 ||\n        // Completely left of viewport\n        rect.left > viewportWidth // Completely right of viewport\n        ;\n        if (isCompletelyOutOfView) {\n          console.log(\"🔄 Current hotspot step element is out of view, gentle auto-scroll\");\n          try {\n            currentElement.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              inline: 'nearest'\n            });\n          } catch (error) {\n            console.log(\"Current hotspot step auto-scroll failed:\", error);\n          }\n        }\n      }\n    }, 100);\n    return () => clearTimeout(timeoutId);\n  }, [currentStep, savedGuideData]);\n\n  // Hotspot styles are applied directly in the applyHotspotStyles function\n  // State for overlay value\n  const [, setOverlayValue] = useState(false);\n  const handleContinue = async () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        var _savedGuideData$Guide4;\n        // Enhanced auto-scroll: Check if next element needs scrolling before navigation\n        const nextStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide4 = savedGuideData.GuideStep) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4[currentStep]; // currentStep is 0-based for next step\n        if (nextStepData !== null && nextStepData !== void 0 && nextStepData.ElementPath) {\n          console.log(\"🔍 Enhanced checking auto-scroll for next hotspot element:\", {\n            xpath: nextStepData.ElementPath,\n            stepIndex: currentStep + 1,\n            stepTitle: `Step ${currentStep + 1}`\n          });\n\n          // Use advanced polling and scrolling\n          const scrollPromise = new Promise(resolve => {\n            pollForElement(nextStepData.ElementPath || \"\", \"\",\n            // No fallback path for hotspots\n            async foundElement => {\n              console.log(\"🎯 Next hotspot element found, checking visibility\");\n              const rect = foundElement.getBoundingClientRect();\n              const viewportHeight = window.innerHeight;\n              const viewportWidth = window.innerWidth;\n\n              // Enhanced visibility check\n              const isReasonablyVisible = rect.top >= -50 && rect.top <= viewportHeight - 100 && rect.left >= -50 && rect.left <= viewportWidth - 100 && rect.bottom > 100 && rect.right > 100;\n              if (isReasonablyVisible) {\n                console.log(\"✅ Next hotspot element is reasonably visible, minimal adjustment\");\n                // Element is mostly visible, just ensure it's well positioned\n                try {\n                  foundElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'nearest',\n                    // Don't force center if already visible\n                    inline: 'nearest'\n                  });\n                } catch (error) {\n                  console.log(\"Minimal hotspot scroll adjustment failed:\", error);\n                }\n              } else {\n                console.log(\"🎯 Next hotspot element not visible, performing enhanced auto-scroll\");\n                // Element is not visible, use advanced scrolling\n                try {\n                  await scrollToTargetElement(foundElement, \"top\");\n                  console.log(\"✅ Enhanced hotspot auto-scroll completed successfully\");\n                } catch (scrollError) {\n                  console.error(\"❌ Enhanced hotspot auto-scroll failed:\", scrollError);\n                }\n              }\n              resolve();\n            }, 20,\n            // Reasonable maxAttempts\n            30,\n            // Reasonable initial interval\n            \"Next hotspot element\", () => {\n              console.log(\"❌ Next hotspot element not found after polling, continuing without scroll\");\n              resolve();\n            });\n          });\n          try {\n            await scrollPromise;\n            console.log(\"✅ Hotspot element finding and scroll check completed\");\n          } catch (error) {\n            console.error(\"❌ Hotspot scroll promise failed:\", error);\n          }\n        }\n        setCurrentStep(currentStep + 1);\n        onContinue();\n        renderNextPopup(currentStep < totalSteps);\n      }\n    } else {\n      var _savedGuideData$Guide5;\n      // Enhanced Tour template logic\n      const nextStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : _savedGuideData$Guide5[currentStep]; // currentStep is 0-based for next step\n      if (nextStepData !== null && nextStepData !== void 0 && nextStepData.ElementPath) {\n        console.log(\"🔍 Enhanced checking auto-scroll for next tour hotspot element:\", {\n          xpath: nextStepData.ElementPath,\n          stepIndex: currentStep + 1,\n          stepTitle: `Step ${currentStep + 1}`\n        });\n\n        // Use advanced polling and scrolling for tour hotspots too\n        const scrollPromise = new Promise(resolve => {\n          pollForElement(nextStepData.ElementPath || \"\", \"\",\n          // No fallback path for hotspots\n          async foundElement => {\n            console.log(\"🎯 Next tour hotspot element found, checking visibility\");\n            const rect = foundElement.getBoundingClientRect();\n            const viewportHeight = window.innerHeight;\n            const viewportWidth = window.innerWidth;\n\n            // Enhanced visibility check\n            const isReasonablyVisible = rect.top >= -50 && rect.top <= viewportHeight - 100 && rect.left >= -50 && rect.left <= viewportWidth - 100 && rect.bottom > 100 && rect.right > 100;\n            if (isReasonablyVisible) {\n              console.log(\"✅ Next tour hotspot element is reasonably visible, minimal adjustment\");\n              // Element is mostly visible, just ensure it's well positioned\n              try {\n                foundElement.scrollIntoView({\n                  behavior: 'smooth',\n                  block: 'nearest',\n                  // Don't force center if already visible\n                  inline: 'nearest'\n                });\n              } catch (error) {\n                console.log(\"Minimal tour hotspot scroll adjustment failed:\", error);\n              }\n            } else {\n              console.log(\"🎯 Next tour hotspot element not visible, performing enhanced auto-scroll\");\n              // Element is not visible, use advanced scrolling\n              try {\n                await scrollToTargetElement(foundElement, \"top\");\n                console.log(\"✅ Enhanced tour hotspot auto-scroll completed successfully\");\n              } catch (scrollError) {\n                console.error(\"❌ Enhanced tour hotspot auto-scroll failed:\", scrollError);\n              }\n            }\n            resolve();\n          }, 20,\n          // Reasonable maxAttempts\n          30,\n          // Reasonable initial interval\n          \"Next tour hotspot element\", () => {\n            console.log(\"❌ Next tour hotspot element not found after polling, continuing without scroll\");\n            resolve();\n          });\n        });\n        try {\n          await scrollPromise;\n          console.log(\"✅ Tour hotspot element finding and scroll check completed\");\n        } catch (error) {\n          console.error(\"❌ Tour hotspot scroll promise failed:\", error);\n        }\n      }\n      setCurrentStep(currentStep + 1);\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.style.display = \"none\";\n        existingHotspot.remove();\n      }\n    }\n  };\n  const renderNextPopup = shouldRenderNextPopup => {\n    var _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11, _savedGuideData$Guide12, _savedGuideData$Guide13, _savedGuideData$Guide14, _savedGuideData$Guide15;\n    return shouldRenderNextPopup ? /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: isHotspotPopupOpen,\n      showHotspotenduser: showHotspotenduser,\n      handleHotspotHover: handleHotspotHover,\n      handleHotspotClick: handleHotspotClick,\n      anchorEl: anchorEl,\n      savedGuideData: savedGuideData,\n      guideStep: guideStep,\n      onClose: onClose,\n      onPrevious: handlePrevious,\n      onContinue: handleContinue,\n      title: title,\n      text: text,\n      imageUrl: imageUrl,\n      currentStep: currentStep + 1,\n      totalSteps: totalSteps,\n      onDontShowAgain: onDontShowAgain,\n      progress: progress,\n      textFieldProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide6 = savedGuideData.GuideStep) === null || _savedGuideData$Guide6 === void 0 ? void 0 : (_savedGuideData$Guide7 = _savedGuideData$Guide6[currentStep]) === null || _savedGuideData$Guide7 === void 0 ? void 0 : _savedGuideData$Guide7.TextFieldProperties,\n      imageProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide8 = savedGuideData.GuideStep) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8[currentStep]) === null || _savedGuideData$Guide9 === void 0 ? void 0 : _savedGuideData$Guide9.ImageProperties,\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide10 = savedGuideData.GuideStep) === null || _savedGuideData$Guide10 === void 0 ? void 0 : (_savedGuideData$Guide11 = _savedGuideData$Guide10[currentStep]) === null || _savedGuideData$Guide11 === void 0 ? void 0 : (_savedGuideData$Guide12 = _savedGuideData$Guide11.ButtonSection) === null || _savedGuideData$Guide12 === void 0 ? void 0 : (_savedGuideData$Guide13 = _savedGuideData$Guide12.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide13 === void 0 ? void 0 : _savedGuideData$Guide13.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: modalProperties,\n      canvasProperties: canvasProperties,\n      htmlSnippet: htmlSnippet,\n      OverlayValue: OverlayValue,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide14 = savedGuideData.GuideStep) === null || _savedGuideData$Guide14 === void 0 ? void 0 : (_savedGuideData$Guide15 = _savedGuideData$Guide14[currentStep - 1]) === null || _savedGuideData$Guide15 === void 0 ? void 0 : _savedGuideData$Guide15.Hotspot) || {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 4\n    }, this) : null;\n  };\n  const handlePrevious = async () => {\n    if (currentStep > 1) {\n      console.log(\"🔙 Navigating to previous hotspot step:\", {\n        currentStep: currentStep,\n        prevStep: currentStep - 1\n      });\n\n      // Smart previous navigation scrolling\n      if (currentStep - 1 === 1) {\n        console.log(\"HandlePrevious: Going back to first hotspot step, scroll to top\");\n        try {\n          window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n          });\n        } catch (error) {\n          console.log(\"HandlePrevious: Scroll to top failed:\", error);\n        }\n      } else {\n        var _savedGuideData$Guide16;\n        // For other steps, check if previous step element needs scrolling\n        const prevStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide16 = savedGuideData.GuideStep) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16[currentStep - 2]; // currentStep - 2 for 0-based array\n        if (prevStepData !== null && prevStepData !== void 0 && prevStepData.ElementPath) {\n          console.log(\"HandlePrevious: Checking if previous hotspot element needs scrolling\");\n          setTimeout(() => {\n            const prevElement = getElementByXPath(prevStepData.ElementPath || \"\");\n            if (prevElement) {\n              const rect = prevElement.getBoundingClientRect();\n              const isOutOfView = rect.bottom < 0 || rect.top > window.innerHeight || rect.right < 0 || rect.left > window.innerWidth;\n              if (isOutOfView) {\n                console.log(\"HandlePrevious: Previous hotspot element out of view, scrolling\");\n                try {\n                  prevElement.scrollIntoView({\n                    behavior: 'smooth',\n                    block: 'center',\n                    inline: 'nearest'\n                  });\n                } catch (error) {\n                  console.log(\"HandlePrevious: Hotspot element scroll failed:\", error);\n                }\n              }\n            }\n          }, 100);\n        }\n      }\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  useEffect(() => {\n    if (OverlayValue) {\n      setOverlayValue(true);\n    } else {\n      setOverlayValue(false);\n    }\n  }, [OverlayValue]);\n  // Image fit is used directly in the component\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n\n  // Image styles are applied directly in the component\n\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Helper function to check if popup has only buttons (no text or images)\n  const hasOnlyButtons = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasButtons && !hasImage && !hasText;\n  };\n\n  // Helper function to check if popup has only text (no buttons or images)\n  const hasOnlyText = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasText && !hasImage && !hasButtons;\n  };\n\n  // Function to calculate the optimal width based on content and buttons\n  const calculateOptimalWidth = () => {\n    var _contentRef$current, _buttonContainerRef$c;\n    // If we have a fixed width from canvas settings and not a compact popup, use that\n    if (canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width && !hasOnlyButtons() && !hasOnlyText()) {\n      return `${canvasProperties.Width}px`;\n    }\n\n    // For popups with only buttons or only text, use auto width\n    if (hasOnlyButtons() || hasOnlyText()) {\n      return \"auto\";\n    }\n\n    // Get the width of content and button container\n    const contentWidth = ((_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.scrollWidth) || 0;\n    const buttonWidth = ((_buttonContainerRef$c = buttonContainerRef.current) === null || _buttonContainerRef$c === void 0 ? void 0 : _buttonContainerRef$c.scrollWidth) || 0;\n\n    // Use the larger of the two, with some minimum and maximum constraints\n    const optimalWidth = Math.max(contentWidth, buttonWidth);\n\n    // Add some padding to ensure text has room to wrap naturally\n    const paddedWidth = optimalWidth + 20; // 10px padding on each side\n\n    // Ensure width is between reasonable bounds\n    const minWidth = 250; // Minimum width\n    const maxWidth = 800; // Maximum width\n\n    const finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n    return `${finalWidth}px`;\n  };\n\n  // Update dynamic width when content or buttons change\n  useEffect(() => {\n    // Use requestAnimationFrame to ensure DOM has been updated\n    requestAnimationFrame(() => {\n      const newWidth = calculateOptimalWidth();\n      setDynamicWidth(newWidth);\n    });\n  }, [textFieldProperties, imageProperties, customButton, currentStep]);\n\n  // Recalculate popup position when hotspot size changes\n  useEffect(() => {\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        var _toolTipGuideMetaData;\n        const rect = element.getBoundingClientRect();\n        const hotspotPropData = (_toolTipGuideMetaData = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData === void 0 ? void 0 : _toolTipGuideMetaData.hotspots;\n        const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n        const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n        setPopupPosition(popupPos);\n      }\n    }\n  }, [hotspotSize, xpath, toolTipGuideMetaData]);\n\n  // Recalculate popup position on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (xpath && hotspotSize) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          var _toolTipGuideMetaData2;\n          const rect = element.getBoundingClientRect();\n          const hotspotPropData = (_toolTipGuideMetaData2 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n          const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n          const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n          setPopupPosition(popupPos);\n        }\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [xpath, hotspotSize, toolTipGuideMetaData]);\n  const groupedButtons = customButton.reduce((acc, button) => {\n    const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n    if (!acc[containerId]) {\n      acc[containerId] = [];\n    }\n    acc[containerId].push(button);\n    return acc;\n  }, {});\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Radius) || \"4px\",\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"black\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    maxWidth: hasOnlyButtons() || hasOnlyText() ? \"none !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"800px\",\n    width: hasOnlyButtons() || hasOnlyText() ? \"auto !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"300px\"\n  };\n  const sectionHeight = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.SectionHeight) || \"auto\";\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"Previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide17, _savedGuideData$Guide18;\n        // Reset to the first step\n        setCurrentStep(1);\n        console.log(\"🔄 Restarting hotspot tour, checking first step element\");\n\n        // Enhanced restart scrolling logic\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide17 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide17 !== void 0 && (_savedGuideData$Guide18 = _savedGuideData$Guide17[0]) !== null && _savedGuideData$Guide18 !== void 0 && _savedGuideData$Guide18.ElementPath) {\n          // Check if first step element exists and use gentle scroll\n          const firstElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstElement) {\n            try {\n              firstElement.scrollIntoView({\n                behavior: 'smooth',\n                block: 'center',\n                inline: 'nearest'\n              });\n              console.log(\"✅ Gentle restart scroll to first hotspot completed\");\n            } catch (error) {\n              console.log(\"❌ Gentle restart scroll failed, scrolling to top\");\n              window.scrollTo({\n                top: 0,\n                behavior: 'smooth'\n              });\n            }\n          } else {\n            console.log(\"❌ First hotspot step element not found, scrolling to top\");\n            window.scrollTo({\n              top: 0,\n              behavior: 'smooth'\n            });\n          }\n        } else {\n          // No xpath available, just scroll to top\n          console.log(\"ℹ️ No xpath for first hotspot step, scrolling to top\");\n          window.scrollTo({\n            top: 0,\n            behavior: 'smooth'\n          });\n        }\n      }\n    }\n    setOverlayValue(false);\n  };\n  useEffect(() => {\n    var _guideStep2, _guideStep2$Hotspot;\n    if (guideStep !== null && guideStep !== void 0 && (_guideStep2 = guideStep[currentStep - 1]) !== null && _guideStep2 !== void 0 && (_guideStep2$Hotspot = _guideStep2.Hotspot) !== null && _guideStep2$Hotspot !== void 0 && _guideStep2$Hotspot.ShowByDefault) {\n      // Show tooltip by default\n      setOpenTooltip(true);\n    }\n  }, [guideStep === null || guideStep === void 0 ? void 0 : guideStep[currentStep - 1], currentStep, setOpenTooltip]);\n\n  // Add effect to handle isHotspotPopupOpen prop changes\n  useEffect(() => {\n    if (isHotspotPopupOpen) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData3 !== void 0 && _toolTipGuideMetaData3.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData4 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide19 = savedGuideData.GuideStep) === null || _savedGuideData$Guide19 === void 0 ? void 0 : (_savedGuideData$Guide20 = _savedGuideData$Guide19[currentStep - 1]) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : (_savedGuideData$Guide22 = _savedGuideData$Guide21[0]) === null || _savedGuideData$Guide22 === void 0 ? void 0 : _savedGuideData$Guide22.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      // For \"Hovering Hotspot\", we'll wait for the hover event\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [isHotspotPopupOpen, toolTipGuideMetaData]);\n\n  // Add effect to handle showHotspotenduser prop changes\n  useEffect(() => {\n    if (showHotspotenduser) {\n      var _toolTipGuideMetaData5, _toolTipGuideMetaData6, _savedGuideData$Guide23, _savedGuideData$Guide24, _savedGuideData$Guide25, _savedGuideData$Guide26;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData6 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide23 = savedGuideData.GuideStep) === null || _savedGuideData$Guide23 === void 0 ? void 0 : (_savedGuideData$Guide24 = _savedGuideData$Guide23[currentStep - 1]) === null || _savedGuideData$Guide24 === void 0 ? void 0 : _savedGuideData$Guide24.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide25 = savedGuideData.GuideStep) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25[0]) === null || _savedGuideData$Guide26 === void 0 ? void 0 : _savedGuideData$Guide26.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [showHotspotenduser, toolTipGuideMetaData]);\n\n  // Add a global click handler to detect clicks outside the hotspot to close the tooltip\n  useEffect(() => {\n    const handleGlobalClick = e => {\n      const hotspotElement = document.getElementById(\"hotspotBlink\");\n\n      // Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\n      if (hotspotElement && hotspotElement.contains(e.target)) {\n        return;\n      }\n\n      // We want to keep the tooltip open once it's been displayed\n      // So we're not closing it on clicks outside anymore\n    };\n    document.addEventListener(\"click\", handleGlobalClick);\n    return () => {\n      document.removeEventListener(\"click\", handleGlobalClick);\n    };\n  }, [toolTipGuideMetaData]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  // We no longer need the persistent monitoring effect since we want the tooltip\n  // to close when the mouse leaves the hotspot\n\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const getCanvasPosition = (position = \"center-center\") => {\n    switch (position) {\n      case \"bottom-left\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-right\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-center\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"center-center\":\n        return {\n          top: \"25% !important\"\n        };\n      case \"left-center\":\n        return {\n          top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\"\n        };\n      case \"right-center\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-left\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-right\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-center\":\n        return {\n          top: \"9% !important\"\n        };\n      default:\n        return {\n          top: \"25% !important\"\n        };\n    }\n  };\n\n  // function to get the correct property value based on tour vs normal hotspot\n  const getHotspotProperty = (propName, hotspotPropData, hotspotData) => {\n    if (selectedTemplateTour === \"Hotspot\") {\n      // For tour hotspots, use saved data first, fallback to metadata\n      switch (propName) {\n        case 'PulseAnimation':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.PulseAnimation) !== undefined ? hotspotData.PulseAnimation : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.PulseAnimation;\n        case 'StopAnimation':\n          // Always use stopAnimationUponInteraction for consistency\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.stopAnimationUponInteraction) !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n        case 'ShowUpon':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowUpon) !== undefined ? hotspotData.ShowUpon : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon;\n        case 'ShowByDefault':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowByDefault) !== undefined ? hotspotData.ShowByDefault : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowByDefault;\n        default:\n          return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n      }\n    } else {\n      // For normal hotspots, use metadata\n      if (propName === 'StopAnimation') {\n        return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n      }\n      return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n    }\n  };\n  const applyHotspotStyles = (hotspot, hotspotPropData, hotspotData, left, top) => {\n    hotspot.style.position = \"absolute\";\n    hotspot.style.left = `${left}px`;\n    hotspot.style.top = `${top}px`;\n    hotspot.style.width = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`; // Default size if not provided\n    hotspot.style.height = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`;\n    hotspot.style.backgroundColor = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Color;\n    hotspot.style.borderRadius = \"50%\";\n    hotspot.style.zIndex = \"auto !important\"; // Increased z-index\n    hotspot.style.transition = \"none\";\n    hotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\n    hotspot.innerHTML = \"\";\n    if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n      const textSpan = document.createElement(\"span\");\n      textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n      textSpan.style.color = \"white\";\n      textSpan.style.fontSize = \"14px\";\n      textSpan.style.fontWeight = \"bold\";\n      textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n      textSpan.style.display = \"flex\";\n      textSpan.style.alignItems = \"center\";\n      textSpan.style.justifyContent = \"center\";\n      textSpan.style.width = \"100%\";\n      textSpan.style.height = \"100%\";\n      hotspot.appendChild(textSpan);\n    }\n\n    // Apply animation class if needed\n    // Track if pulse has been stopped by hover\n    const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\n    const shouldPulse = selectedTemplateTour === \"Hotspot\" ? pulseAnimationEnabled !== false && !hotspot._pulseStopped : hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped;\n    if (shouldPulse) {\n      hotspot.classList.add(\"pulse-animation\");\n      hotspot.classList.remove(\"pulse-animation-removed\");\n    } else {\n      hotspot.classList.remove(\"pulse-animation\");\n      hotspot.classList.add(\"pulse-animation-removed\");\n    }\n\n    // Ensure the hotspot is visible and clickable\n    hotspot.style.display = \"flex\";\n    hotspot.style.pointerEvents = \"auto\";\n\n    // No need for separate animation control functions here\n    // Animation will be controlled directly in the event handlers\n    // Set initial state of openTooltip based on ShowByDefault and ShowUpon\n    const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n    if (showByDefault) {\n      setOpenTooltip(true);\n    } else {\n      // If not showing by default, only show based on interaction type\n      //setOpenTooltip(false);\n    }\n\n    // Only clone and replace if the hotspot doesn't have event listeners already\n    // This prevents losing the _pulseStopped state unnecessarily\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      const newHotspot = hotspot.cloneNode(true);\n      // Copy the _pulseStopped property if it exists\n      if (hotspot._pulseStopped !== undefined) {\n        newHotspot._pulseStopped = hotspot._pulseStopped;\n      }\n      if (hotspot.parentNode) {\n        hotspot.parentNode.replaceChild(newHotspot, hotspot);\n        hotspot = newHotspot;\n      }\n    }\n\n    // Ensure pointer events are enabled\n    hotspot.style.pointerEvents = \"auto\";\n\n    // Define combined event handlers that handle both animation and tooltip\n    const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\n    const handleHover = e => {\n      e.stopPropagation();\n      console.log(\"Hover detected on hotspot\");\n\n      // Show tooltip if ShowUpon is \"Hovering Hotspot\"\n      if (showUpon === \"Hovering Hotspot\") {\n        // Set openTooltip to true when hovering\n        setOpenTooltip(true);\n\n        // Call the passed hover handler if it exists\n        if (typeof handleHotspotHover === \"function\") {\n          handleHotspotHover();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n    const handleMouseOut = e => {\n      e.stopPropagation();\n\n      // Hide tooltip when mouse leaves the hotspot\n      // Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\n      const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n      if (showUpon === \"Hovering Hotspot\" && !showByDefault) {\n        // setOpenTooltip(false);\n      }\n    };\n    const handleClick = e => {\n      e.stopPropagation();\n      console.log(\"Click detected on hotspot\");\n\n      // Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\n      if (showUpon === \"Clicking Hotspot\" || !showUpon) {\n        // Toggle the tooltip state\n        setOpenTooltip(!openTooltip);\n\n        // Call the passed click handler if it exists\n        if (typeof handleHotspotClick === \"function\") {\n          handleHotspotClick();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n\n    // Add appropriate event listeners based on ShowUpon property\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      if (showUpon === \"Hovering Hotspot\") {\n        // For hover interaction\n        hotspot.addEventListener(\"mouseover\", handleHover);\n        hotspot.addEventListener(\"mouseout\", handleMouseOut);\n\n        // Also add click handler for better user experience\n        hotspot.addEventListener(\"click\", handleClick);\n      } else {\n        // For click interaction (default)\n        hotspot.addEventListener(\"click\", handleClick);\n      }\n\n      // Mark that listeners have been attached\n      hotspot.setAttribute('data-listeners-attached', 'true');\n    }\n  };\n  useEffect(() => {\n    let element;\n    let steps;\n    const fetchGuideDetails = async () => {\n      try {\n        var _savedGuideData$Guide27, _savedGuideData$Guide28, _steps, _steps$;\n        //   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\n        steps = (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideStep) || [];\n\n        // For tour hotspots, use the current step's element path\n        const elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide27 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide27 !== void 0 && (_savedGuideData$Guide28 = _savedGuideData$Guide27[currentStep - 1]) !== null && _savedGuideData$Guide28 !== void 0 && _savedGuideData$Guide28.ElementPath ? savedGuideData.GuideStep[currentStep - 1].ElementPath : ((_steps = steps) === null || _steps === void 0 ? void 0 : (_steps$ = _steps[0]) === null || _steps$ === void 0 ? void 0 : _steps$.ElementPath) || \"\";\n        element = getElementByXPath(elementPath || \"\");\n        setTargetElement(element);\n        if (element) {\n          // element.style.outline = \"2px solid red\";\n        }\n\n        // Check if this is a hotspot scenario (normal or tour)\n        const isHotspotScenario = selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" || title === \"Hotspot\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\";\n        if (isHotspotScenario) {\n          var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _hotspotPropData, _hotspotPropData2, _hotspotPropData3, _hotspotPropData4;\n          // Get hotspot properties - prioritize tour data for tour hotspots\n          let hotspotPropData;\n          let hotspotData;\n          if (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.hotspots) {\n            var _savedGuideData$Guide29, _savedGuideData$Guide30;\n            // Tour hotspot - use current step metadata\n            hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide29 = savedGuideData.GuideStep) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29[currentStep - 1]) === null || _savedGuideData$Guide30 === void 0 ? void 0 : _savedGuideData$Guide30.Hotspot;\n          } else if (toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData8 = toolTipGuideMetaData[0]) !== null && _toolTipGuideMetaData8 !== void 0 && _toolTipGuideMetaData8.hotspots) {\n            var _savedGuideData$Guide31, _savedGuideData$Guide32;\n            // Normal hotspot - use first metadata entry\n            hotspotPropData = toolTipGuideMetaData[0].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide31 = savedGuideData.GuideStep) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31[0]) === null || _savedGuideData$Guide32 === void 0 ? void 0 : _savedGuideData$Guide32.Hotspot;\n          } else {\n            var _savedGuideData$Guide33, _savedGuideData$Guide34;\n            // Fallback to default values for tour hotspots without metadata\n            hotspotPropData = {\n              XPosition: \"4\",\n              YPosition: \"4\",\n              Type: \"Question\",\n              Color: \"yellow\",\n              Size: \"16\",\n              PulseAnimation: true,\n              stopAnimationUponInteraction: true,\n              ShowUpon: \"Hovering Hotspot\",\n              ShowByDefault: false\n            };\n            hotspotData = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide33 = savedGuideData.GuideStep) === null || _savedGuideData$Guide33 === void 0 ? void 0 : (_savedGuideData$Guide34 = _savedGuideData$Guide33[currentStep - 1]) === null || _savedGuideData$Guide34 === void 0 ? void 0 : _savedGuideData$Guide34.Hotspot) || {};\n          }\n          const xOffset = parseFloat(((_hotspotPropData = hotspotPropData) === null || _hotspotPropData === void 0 ? void 0 : _hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat(((_hotspotPropData2 = hotspotPropData) === null || _hotspotPropData2 === void 0 ? void 0 : _hotspotPropData2.YPosition) || \"4\");\n          const currentHotspotSize = parseFloat(((_hotspotPropData3 = hotspotPropData) === null || _hotspotPropData3 === void 0 ? void 0 : _hotspotPropData3.Size) || \"30\");\n\n          // Update hotspot size state\n          setHotspotSize(currentHotspotSize);\n          let left, top;\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            left = rect.x + xOffset;\n            top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\n\n            // Calculate popup position below the hotspot\n            const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n            setPopupPosition(popupPos);\n          }\n\n          // Check if hotspot already exists, preserve it to maintain _pulseStopped state\n          const existingHotspot = document.getElementById(\"hotspotBlink\");\n          if (existingHotspot) {\n            hotspot = existingHotspot;\n            // Don't reset _pulseStopped if it already exists\n          } else {\n            // Create new hotspot only if it doesn't exist\n            hotspot = document.createElement(\"div\");\n            hotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\n            hotspot._pulseStopped = false; // Set only on creation\n            document.body.appendChild(hotspot);\n          }\n          hotspot.style.cursor = \"pointer\";\n          hotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\n\n          // Make sure the hotspot is visible and clickable\n          hotspot.style.zIndex = \"9999\";\n\n          // If ShowByDefault is true, set openTooltip to true immediately\n          if ((_hotspotPropData4 = hotspotPropData) !== null && _hotspotPropData4 !== void 0 && _hotspotPropData4.ShowByDefault) {\n            setOpenTooltip(true);\n          }\n\n          // Set styles first\n          applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\n\n          // Set initial tooltip visibility based on ShowByDefault\n          const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n          if (showByDefault) {\n            setOpenTooltip(true);\n          } else {\n            //setOpenTooltip(false);\n          }\n\n          // We don't need to add event listeners here as they're already added in applyHotspotStyles\n        }\n      } catch (error) {\n        console.error(\"Error in fetchGuideDetails:\", error);\n      }\n    };\n    fetchGuideDetails();\n    return () => {\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.onclick = null;\n        existingHotspot.onmouseover = null;\n        existingHotspot.onmouseout = null;\n      }\n    };\n  }, [savedGuideData, toolTipGuideMetaData, isHotspotPopupOpen, showHotspotenduser, selectedTemplateTour, currentStep\n  // Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n  ]);\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide35 = savedGuideData.GuideStep) === null || _savedGuideData$Guide35 === void 0 ? void 0 : (_savedGuideData$Guide36 = _savedGuideData$Guide35[0]) === null || _savedGuideData$Guide36 === void 0 ? void 0 : (_savedGuideData$Guide37 = _savedGuideData$Guide36.Tooltip) === null || _savedGuideData$Guide37 === void 0 ? void 0 : _savedGuideData$Guide37.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide38, _savedGuideData$Guide39, _savedGuideData$Guide40;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide38 = savedGuideData.GuideStep) === null || _savedGuideData$Guide38 === void 0 ? void 0 : (_savedGuideData$Guide39 = _savedGuideData$Guide38[0]) === null || _savedGuideData$Guide39 === void 0 ? void 0 : (_savedGuideData$Guide40 = _savedGuideData$Guide39.Tooltip) === null || _savedGuideData$Guide40 === void 0 ? void 0 : _savedGuideData$Guide40.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          position: \"inherit !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1643,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1644,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1631,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"14px\",\n            height: \"4px\",\n            backgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\",\n            // Active color and inactive color\n            borderRadius: \"100px\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1654,\n          columnNumber: 7\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1650,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1670,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1669,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              \"& .MuiLinearProgress-bar\": {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1681,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1680,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1679,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [targetElement && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: openTooltip && /*#__PURE__*/_jsxDEV(Popover, {\n        open: Boolean(popupPosition) || Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: () => {\n          // We want to keep the tooltip open once it's been displayed\n          // So we're not closing it on Popover close events\n        },\n        anchorOrigin: anchorOrigin,\n        transformOrigin: transformOrigin,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: popupPosition ? {\n          top: popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\n          left: popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\")\n        } : undefined,\n        sx: {\n          // \"& .MuiBackdrop-root\": {\n          //     position: 'relative !important', // Ensures higher specificity\n          // },\n          \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n          '& .MuiPaper-root:not(.MuiMobileStepper-root)': {\n            zIndex: 1000,\n            // borderRadius: \"1px\",\n            ...canvasStyle,\n            //...getAnchorAndTransformOrigins,\n            //top: \"16% !important\",\n            // top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n            //     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n            //         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n            //             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n            //                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n            //                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n            //                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n            //                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n            //                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n            ...getCanvasPosition((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\"),\n            top: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.top) || 0) + (tooltipYaxis && tooltipYaxis != 'undefined' ? parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\")) : 0)}px !important`,\n            left: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.left) || 0) + (tooltipXaxis && tooltipXaxis != 'undefined' ? parseFloat(tooltipXaxis) || 0 : 0)}px !important`,\n            overflow: \"hidden\"\n          }\n        },\n        disableScrollLock: true,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            placeContent: \"end\",\n            display: \"flex\"\n          },\n          children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              // Only close if explicitly requested by user clicking the close button\n              //setOpenTooltip(false);\n            },\n            sx: {\n              position: \"fixed\",\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n              left: \"auto\",\n              right: \"auto\",\n              margin: \"-15px\",\n              background: \"#fff !important\",\n              border: \"1px solid #ccc\",\n              zIndex: \"999999\",\n              borderRadius: \"50px\",\n              padding: \"5px !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              sx: {\n                zoom: 1,\n                color: \"#000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1791,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1773,\n            columnNumber: 10\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1771,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\n              overflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\n              width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n              margin: hasOnlyButtons() ? \"0\" : undefined\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                padding: hasOnlyButtons() ? \"0\" : hasOnlyText() ? \"0\" : (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding) || \"10px\",\n                height: hasOnlyButtons() ? \"auto\" : sectionHeight,\n                width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n                margin: hasOnlyButtons() ? \"0\" : undefined\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                ref: contentRef,\n                display: \"flex\",\n                flexDirection: \"column\",\n                flexWrap: \"wrap\",\n                justifyContent: \"center\",\n                sx: {\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\n                  padding: hasOnlyText() ? \"0\" : undefined\n                },\n                children: [imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map(imageProp => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"img\",\n                  src: customImg.Url,\n                  alt: customImg.AltText || \"Image\",\n                  sx: {\n                    maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                    textAlign: imageProp.Alignment || \"center\",\n                    objectFit: customImg.Fit || \"contain\",\n                    //  width: \"500px\",\n                    height: `${customImg.SectionHeight || 250}px`,\n                    background: customImg.BackgroundColor || \"#ffffff\",\n                    margin: \"10px 0\"\n                  },\n                  onClick: () => {\n                    if (imageProp.Hyperlink) {\n                      const targetUrl = imageProp.Hyperlink;\n                      window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                    }\n                  },\n                  style: {\n                    cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                  }\n                }, `${imageProp.Id}-${imgIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1835,\n                  columnNumber: 13\n                }, this))), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n                  var _textField$TextProper, _textField$TextProper2;\n                  return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"qadpt-preview\",\n                    // Use a unique key, either Id or index\n                    sx: {\n                      textAlign: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextFormat) || textStyle.textAlign,\n                      color: ((_textField$TextProper2 = textField.TextProperties) === null || _textField$TextProper2 === void 0 ? void 0 : _textField$TextProper2.TextColor) || textStyle.color,\n                      whiteSpace: \"pre-wrap\",\n                      wordBreak: \"break-word\",\n                      padding: \"0 5px\"\n                    },\n                    dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n                  }, textField.Id || index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1863,\n                    columnNumber: 14\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1822,\n                columnNumber: 10\n              }, this), Object.keys(groupedButtons).map(containerId => {\n                var _groupedButtons$conta, _groupedButtons$conta2;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ref: buttonContainerRef,\n                  sx: {\n                    display: \"flex\",\n                    justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                    flexWrap: \"wrap\",\n                    margin: hasOnlyButtons() ? 0 : \"5px 0\",\n                    backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                    padding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\n                    width: hasOnlyButtons() ? \"auto\" : \"100%\",\n                    borderRadius: hasOnlyButtons() ? \"15px\" : undefined\n                  },\n                  children: groupedButtons[containerId].map((button, index) => {\n                    var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6, _button$ButtonPropert7;\n                    return /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => handleButtonAction(button.ButtonAction),\n                      variant: \"contained\",\n                      sx: {\n                        marginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\n                        margin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\n                        backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                        color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                        border: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"transparent\",\n                        fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"15px\",\n                        width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                        padding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\n                        lineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\n                        textTransform: \"none\",\n                        borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                        minWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\n                        boxShadow: \"none !important\",\n                        // Remove box shadow in normal state\n                        \"&:hover\": {\n                          backgroundColor: ((_button$ButtonPropert7 = button.ButtonProperties) === null || _button$ButtonPropert7 === void 0 ? void 0 : _button$ButtonPropert7.ButtonBackgroundColor) || \"#007bff\",\n                          // Keep the same background color on hover\n                          opacity: 0.9,\n                          // Slightly reduce opacity on hover for visual feedback\n                          boxShadow: \"none !important\" // Remove box shadow in hover state\n                        }\n                      },\n                      children: button.ButtonName\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1895,\n                      columnNumber: 13\n                    }, this);\n                  })\n                }, containerId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1880,\n                  columnNumber: 11\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1815,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1809,\n            columnNumber: 8\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1795,\n          columnNumber: 8\n        }, this), enableProgress && totalSteps > 1 && selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n          children: renderProgress()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1931,\n          columnNumber: 75\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1718,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1703,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1937,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(HotspotPreview, \"6/BT+phR/Kq8Re6ufAczdr6+gAc=\", false, function () {\n  return [useDrawerStore];\n});\n_c = HotspotPreview;\nexport default HotspotPreview;\nvar _c;\n$RefreshReg$(_c, \"HotspotPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "useCallback", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HotspotPreview", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "_savedGuideData$Guide37", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "dynamicWidth", "setDynamicWidth", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "smoothScrollTo", "element", "targetTop", "duration", "maxScroll", "scrollHeight", "clientHeight", "clampedTargetTop", "Math", "max", "min", "scrollTo", "top", "behavior", "error", "console", "log", "startTop", "scrollTop", "distance", "startTime", "performance", "now", "animateScroll", "currentTime", "elapsed", "easeInOutCubic", "t", "easedProgress", "requestAnimationFrame", "universalScrollTo", "options", "isWindow", "window", "documentElement", "undefined", "left", "scrollLeft", "pollForElement", "possibleElementPath", "onElementFound", "maxAttempts", "initialIntervalMs", "description", "onTimeout", "attempts", "currentInterval", "timeoutId", "poll", "jitter", "random", "nextInterval", "setTimeout", "cleanup", "clearTimeout", "scrollToTargetElement", "placement", "tagName", "className", "id", "scrollOperationQueue", "Promise", "resolve", "then", "rect", "getBoundingClientRect", "viewportHeight", "innerHeight", "viewportWidth", "innerWidth", "targetScrollTop", "scrollY", "targetScrollLeft", "scrollX", "maxScrollTop", "maxScrollLeft", "scrollWidth", "currentScrollY", "currentScrollX", "elementRect", "scrollSuccess", "body", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "calculatePopupPosition", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "dynamicOffsetX", "dynamicOffsetY", "position", "_guideStep", "style", "backgroundColor", "_savedGuideData$Guide3", "currentStepData", "currentElement", "isCompletelyOutOfView", "bottom", "right", "scrollIntoView", "block", "inline", "setOverlayValue", "handleContinue", "_savedGuideData$Guide4", "nextStepData", "stepIndex", "step<PERSON>itle", "scrollPromise", "foundElement", "isReasonablyVisible", "scrollError", "renderNextPopup", "_savedGuideData$Guide5", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "_savedGuideData$Guide15", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "prevStep", "_savedGuideData$Guide16", "prevStepData", "prevElement", "isOutOfView", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "length", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "calculateOptimalWidth", "_contentRef$current", "_buttonContainerRef$c", "<PERSON><PERSON><PERSON>", "contentWidth", "current", "buttonWidth", "optimalWidth", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "newWidth", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "handleResize", "_toolTipGuideMetaData2", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "width", "sectionHeight", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "firstElement", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "height", "contentHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "_savedGuideData$Guide34", "currentHotspotSize", "abs", "cursor", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide38", "_savedGuideData$Guide39", "_savedGuideData$Guide40", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "imageProp", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef, useCallback } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tif (!xpath) return null;\r\n\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\tconst node = result.singleNodeValue;\r\n\t\tif (node instanceof HTMLElement) {\r\n\t\t\treturn node;\r\n\t\t} else if (node?.parentElement) {\r\n\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t} else {\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced scrolling function with multiple fallback methods for cross-environment compatibility\r\n\tconst smoothScrollTo = (element: HTMLElement, targetTop: number, duration: number = 300) => {\r\n\t\t// Ensure targetTop is within valid bounds\r\n\t\tconst maxScroll = element.scrollHeight - element.clientHeight;\r\n\t\tconst clampedTargetTop = Math.max(0, Math.min(targetTop, maxScroll));\r\n\r\n\t\t// Method 1: Try native smooth scrolling first\r\n\t\ttry {\r\n\t\t\tif ('scrollTo' in element && typeof element.scrollTo === 'function') {\r\n\t\t\t\telement.scrollTo({\r\n\t\t\t\t\ttop: clampedTargetTop,\r\n\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Native smooth scrollTo failed, trying animation fallback\");\r\n\t\t}\r\n\r\n\t\t// Method 2: Manual animation fallback\r\n\t\ttry {\r\n\t\t\tconst startTop = element.scrollTop;\r\n\t\t\tconst distance = clampedTargetTop - startTop;\r\n\t\t\tconst startTime = performance.now();\r\n\r\n\t\t\tconst animateScroll = (currentTime: number) => {\r\n\t\t\t\tconst elapsed = currentTime - startTime;\r\n\t\t\t\tconst progress = Math.min(elapsed / duration, 1);\r\n\r\n\t\t\t\t// Easing function for smooth animation\r\n\t\t\t\tconst easeInOutCubic = (t: number) => t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\r\n\t\t\t\tconst easedProgress = easeInOutCubic(progress);\r\n\r\n\t\t\t\telement.scrollTop = startTop + (distance * easedProgress);\r\n\r\n\t\t\t\tif (progress < 1) {\r\n\t\t\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t\t\t}\r\n\t\t\t};\r\n\r\n\t\t\trequestAnimationFrame(animateScroll);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"RequestAnimationFrame failed, using direct assignment\");\r\n\t\t\t// Method 3: Direct assignment as final fallback\r\n\t\t\telement.scrollTop = clampedTargetTop;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced cross-environment scrolling function\r\n\tconst universalScrollTo = (element: HTMLElement | Window, options: { top?: number; left?: number; behavior?: 'smooth' | 'auto' }) => {\r\n\t\tconst isWindow = element === window;\r\n\t\tconst targetElement = isWindow ? document.documentElement : element as HTMLElement;\r\n\r\n\t\t// Method 1: Try native scrollTo if available and not blocked\r\n\t\tif (!isWindow && 'scrollTo' in element && typeof (element as any).scrollTo === 'function') {\r\n\t\t\ttry {\r\n\t\t\t\t(element as any).scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Native scrollTo blocked or failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 2: Try window.scrollTo for window element\r\n\t\tif (isWindow && options.behavior === 'smooth') {\r\n\t\t\ttry {\r\n\t\t\t\twindow.scrollTo(options);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Window scrollTo failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 3: Try smooth scrolling with custom animation\r\n\t\tif (options.behavior === 'smooth' && options.top !== undefined) {\r\n\t\t\ttry {\r\n\t\t\t\tsmoothScrollTo(targetElement, options.top);\r\n\t\t\t\treturn true;\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.log(\"Smooth scroll animation failed:\", error);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Method 4: Direct property assignment (final fallback)\r\n\t\ttry {\r\n\t\t\tif (options.top !== undefined) {\r\n\t\t\t\ttargetElement.scrollTop = options.top;\r\n\t\t\t}\r\n\t\t\tif (options.left !== undefined) {\r\n\t\t\t\ttargetElement.scrollLeft = options.left;\r\n\t\t\t}\r\n\t\t\treturn true;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.log(\"Direct property assignment failed:\", error);\r\n\t\t\treturn false;\r\n\t\t}\r\n\t};\r\n\r\n\t// Enhanced reusable element polling function with exponential backoff and better timing\r\n\tconst pollForElement = useCallback((\r\n\t\txpath: string,\r\n\t\tpossibleElementPath: string,\r\n\t\tonElementFound: (element: HTMLElement) => void,\r\n\t\tmaxAttempts: number = 30,\r\n\t\tinitialIntervalMs: number = 16, // Start with one frame\r\n\t\tdescription: string = \"Element\",\r\n\t\tonTimeout?: () => void\r\n\t) => {\r\n\t\tlet attempts = 0;\r\n\t\tlet currentInterval = initialIntervalMs;\r\n\t\tlet timeoutId: NodeJS.Timeout;\r\n\r\n\t\tconst poll = () => {\r\n\t\t\tattempts++;\r\n\t\t\tconsole.log(`🔍 ${description} polling attempt ${attempts}/${maxAttempts}`);\r\n\r\n\t\t\t// Try primary xpath first, then fallback\r\n\t\t\tlet element = getElementByXPath(xpath);\r\n\t\t\tif (!element && possibleElementPath) {\r\n\t\t\t\telement = getElementByXPath(possibleElementPath);\r\n\t\t\t}\r\n\r\n\t\t\tif (element) {\r\n\t\t\t\tconsole.log(`✅ ${description} found after ${attempts} attempts`);\r\n\t\t\t\tonElementFound(element);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\tif (attempts >= maxAttempts) {\r\n\t\t\t\tconsole.log(`❌ ${description} not found after ${maxAttempts} attempts`);\r\n\t\t\t\tif (onTimeout) {\r\n\t\t\t\t\tonTimeout();\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// Exponential backoff with jitter to avoid thundering herd\r\n\t\t\tconst jitter = Math.random() * 0.1 * currentInterval;\r\n\t\t\tconst nextInterval = Math.min(currentInterval * 1.2 + jitter, 200); // Cap at 200ms\r\n\t\t\tcurrentInterval = nextInterval;\r\n\r\n\t\t\ttimeoutId = setTimeout(poll, currentInterval);\r\n\t\t};\r\n\r\n\t\t// Start polling\r\n\t\tpoll();\r\n\r\n\t\t// Return cleanup function\r\n\t\tconst cleanup = () => {\r\n\t\t\tif (timeoutId) {\r\n\t\t\t\tclearTimeout(timeoutId);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Return cleanup function\r\n\t\treturn cleanup;\r\n\t}, []);\r\n\r\n\tconst scrollToTargetElement = useCallback(async (targetElement: HTMLElement, placement: \"top\" | \"left\" | \"right\" | \"bottom\" = \"top\") => {\r\n\t\tif (!targetElement) {\r\n\t\t\tconsole.log(\"ScrollToTargetElement: No target element provided\");\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconsole.log(\"🎯 Starting enhanced auto-scroll to target hotspot element:\", {\r\n\t\t\telement: targetElement,\r\n\t\t\ttagName: targetElement.tagName,\r\n\t\t\tclassName: targetElement.className,\r\n\t\t\tid: targetElement.id,\r\n\t\t\tplacement: placement\r\n\t\t});\r\n\r\n\t\ttry {\r\n\t\t\t// Queue scroll operations to prevent conflicts\r\n\t\t\tconst scrollOperationQueue = Promise.resolve().then(async () => {\r\n\t\t\t\tconst rect = targetElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Calculate optimal scroll position based on placement and viewport\r\n\t\t\t\tlet targetScrollTop = window.scrollY;\r\n\t\t\t\tlet targetScrollLeft = window.scrollX;\r\n\r\n\t\t\t\t// Enhanced positioning logic based on hotspot placement\r\n\t\t\t\tswitch (placement) {\r\n\t\t\t\t\tcase \"top\":\r\n\t\t\t\t\t\t// Position element in lower third of viewport to leave room for hotspot above\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"bottom\":\r\n\t\t\t\t\t\t// Position element in upper third of viewport to leave room for hotspot below\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"left\":\r\n\t\t\t\t\t\t// Position element towards right side to leave room for hotspot on left\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.7);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase \"right\":\r\n\t\t\t\t\t\t// Position element towards left side to leave room for hotspot on right\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t\t\ttargetScrollLeft = window.scrollX + rect.left - (viewportWidth * 0.3);\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault:\r\n\t\t\t\t\t\t// Default: center the element vertically\r\n\t\t\t\t\t\ttargetScrollTop = window.scrollY + rect.top - (viewportHeight * 0.5);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Ensure scroll positions are within valid bounds\r\n\t\t\t\tconst maxScrollTop = Math.max(0, document.documentElement.scrollHeight - viewportHeight);\r\n\t\t\t\tconst maxScrollLeft = Math.max(0, document.documentElement.scrollWidth - viewportWidth);\r\n\r\n\t\t\t\ttargetScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));\r\n\t\t\t\ttargetScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft));\r\n\r\n\t\t\t\tconsole.log(\"📍 Calculated hotspot scroll position:\", {\r\n\t\t\t\t\ttargetScrollTop,\r\n\t\t\t\t\ttargetScrollLeft,\r\n\t\t\t\t\tcurrentScrollY: window.scrollY,\r\n\t\t\t\t\tcurrentScrollX: window.scrollX,\r\n\t\t\t\t\telementRect: rect\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Perform the scroll with multiple fallback methods\r\n\t\t\t\tlet scrollSuccess = false;\r\n\r\n\t\t\t\t// Method 1: Try smooth scrolling\r\n\t\t\t\ttry {\r\n\t\t\t\t\tscrollSuccess = universalScrollTo(window, {\r\n\t\t\t\t\t\ttop: targetScrollTop,\r\n\t\t\t\t\t\tleft: targetScrollLeft,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(\"✅ Universal hotspot scroll success:\", scrollSuccess);\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"❌ Universal hotspot scroll failed:\", error);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 2: Fallback to immediate scroll if smooth scroll failed\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\twindow.scrollTo(targetScrollLeft, targetScrollTop);\r\n\t\t\t\t\t\tscrollSuccess = true;\r\n\t\t\t\t\t\tconsole.log(\"✅ Fallback hotspot scroll successful\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Fallback hotspot scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Method 3: Final fallback using direct property assignment\r\n\t\t\t\tif (!scrollSuccess) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tdocument.documentElement.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.documentElement.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tdocument.body.scrollTop = targetScrollTop;\r\n\t\t\t\t\t\tdocument.body.scrollLeft = targetScrollLeft;\r\n\t\t\t\t\t\tconsole.log(\"✅ Direct property assignment completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"❌ Direct property assignment failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Small delay to allow scroll to complete\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n\t\t\t\tconsole.log(\"🏁 Auto-scroll operation completed\");\r\n\t\t\t});\r\n\r\n\t\t\tawait scrollOperationQueue;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"❌ ScrollToTargetElement error:\", error);\r\n\t\t}\r\n\t}, [smoothScrollTo, universalScrollTo]);\r\n\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Smart auto-scroll for current hotspot step changes - only when element is not visible\r\n\tuseEffect(() => {\r\n\t\tconst currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n\t\tif (!currentStepData?.ElementPath) {\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// Small delay to allow DOM to settle\r\n\t\tconst timeoutId = setTimeout(() => {\r\n\t\t\tconst currentElement = getElementByXPath(currentStepData.ElementPath || \"\");\r\n\r\n\t\t\tif (currentElement) {\r\n\t\t\t\tconst rect = currentElement.getBoundingClientRect();\r\n\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t// Check if element is completely out of view\r\n\t\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (isCompletelyOutOfView) {\r\n\t\t\t\t\tconsole.log(\"🔄 Current hotspot step element is out of view, gentle auto-scroll\");\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tcurrentElement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"Current hotspot step auto-scroll failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}, 100);\r\n\r\n\t\treturn () => clearTimeout(timeoutId);\r\n\t}, [currentStep, savedGuideData]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = async () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\t// Enhanced auto-scroll: Check if next element needs scrolling before navigation\r\n\t\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[currentStep]; // currentStep is 0-based for next step\r\n\t\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\t\tconsole.log(\"🔍 Enhanced checking auto-scroll for next hotspot element:\", {\r\n\t\t\t\t\t\txpath: nextStepData.ElementPath,\r\n\t\t\t\t\t\tstepIndex: currentStep + 1,\r\n\t\t\t\t\t\tstepTitle: `Step ${currentStep + 1}`\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// Use advanced polling and scrolling\r\n\t\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\t\tnextStepData.ElementPath || \"\",\r\n\t\t\t\t\t\t\t\"\", // No fallback path for hotspots\r\n\t\t\t\t\t\t\tasync (foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next hotspot element found, checking visibility\");\r\n\r\n\t\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t\t// Enhanced visibility check\r\n\t\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\t\trect.top >= -50 && rect.top <= viewportHeight - 100 &&\r\n\t\t\t\t\t\t\t\t\trect.left >= -50 && rect.left <= viewportWidth - 100 &&\r\n\t\t\t\t\t\t\t\t\trect.bottom > 100 && rect.right > 100\r\n\t\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Next hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal hotspot scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next hotspot element not visible, performing enhanced auto-scroll\");\r\n\t\t\t\t\t\t\t\t\t// Element is not visible, use advanced scrolling\r\n\t\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\t\tawait scrollToTargetElement(foundElement, \"top\");\r\n\t\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Enhanced hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Enhanced hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\t\"Next hotspot element\",\r\n\t\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\t\tconsole.log(\"❌ Next hotspot element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\t\tconsole.log(\"✅ Hotspot element finding and scroll check completed\");\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error(\"❌ Hotspot scroll promise failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// Enhanced Tour template logic\r\n\t\t\tconst nextStepData = savedGuideData?.GuideStep?.[currentStep]; // currentStep is 0-based for next step\r\n\t\t\tif (nextStepData?.ElementPath) {\r\n\t\t\t\tconsole.log(\"🔍 Enhanced checking auto-scroll for next tour hotspot element:\", {\r\n\t\t\t\t\txpath: nextStepData.ElementPath,\r\n\t\t\t\t\tstepIndex: currentStep + 1,\r\n\t\t\t\t\tstepTitle: `Step ${currentStep + 1}`\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// Use advanced polling and scrolling for tour hotspots too\r\n\t\t\t\tconst scrollPromise = new Promise<void>((resolve) => {\r\n\t\t\t\t\tpollForElement(\r\n\t\t\t\t\t\tnextStepData.ElementPath || \"\",\r\n\t\t\t\t\t\t\"\", // No fallback path for hotspots\r\n\t\t\t\t\t\tasync (foundElement: HTMLElement) => {\r\n\t\t\t\t\t\t\tconsole.log(\"🎯 Next tour hotspot element found, checking visibility\");\r\n\r\n\t\t\t\t\t\t\tconst rect = foundElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t\t\t// Enhanced visibility check\r\n\t\t\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\t\t\trect.top >= -50 && rect.top <= viewportHeight - 100 &&\r\n\t\t\t\t\t\t\t\trect.left >= -50 && rect.left <= viewportWidth - 100 &&\r\n\t\t\t\t\t\t\t\trect.bottom > 100 && rect.right > 100\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (isReasonablyVisible) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Next tour hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tfoundElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"Minimal tour hotspot scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tconsole.log(\"🎯 Next tour hotspot element not visible, performing enhanced auto-scroll\");\r\n\t\t\t\t\t\t\t\t// Element is not visible, use advanced scrolling\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tawait scrollToTargetElement(foundElement, \"top\");\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"✅ Enhanced tour hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\t\tconsole.error(\"❌ Enhanced tour hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t20, // Reasonable maxAttempts\r\n\t\t\t\t\t\t30, // Reasonable initial interval\r\n\t\t\t\t\t\t\"Next tour hotspot element\",\r\n\t\t\t\t\t\t() => {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Next tour hotspot element not found after polling, continuing without scroll\");\r\n\t\t\t\t\t\t\tresolve();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\t\t\t\t});\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tawait scrollPromise;\r\n\t\t\t\t\tconsole.log(\"✅ Tour hotspot element finding and scroll check completed\");\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error(\"❌ Tour hotspot scroll promise failed:\", error);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = async () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tconsole.log(\"🔙 Navigating to previous hotspot step:\", {\r\n\t\t\t\tcurrentStep: currentStep,\r\n\t\t\t\tprevStep: currentStep - 1\r\n\t\t\t});\r\n\r\n\t\t\t// Smart previous navigation scrolling\r\n\t\t\tif (currentStep - 1 === 1) {\r\n\t\t\t\tconsole.log(\"HandlePrevious: Going back to first hotspot step, scroll to top\");\r\n\t\t\t\ttry {\r\n\t\t\t\t\twindow.scrollTo({\r\n\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\tbehavior: 'smooth'\r\n\t\t\t\t\t});\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Scroll to top failed:\", error);\r\n\t\t\t\t}\r\n\t\t\t} else {\r\n\t\t\t\t// For other steps, check if previous step element needs scrolling\r\n\t\t\t\tconst prevStepData = savedGuideData?.GuideStep?.[currentStep - 2]; // currentStep - 2 for 0-based array\r\n\t\t\t\tif (prevStepData?.ElementPath) {\r\n\t\t\t\t\tconsole.log(\"HandlePrevious: Checking if previous hotspot element needs scrolling\");\r\n\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tconst prevElement = getElementByXPath(prevStepData.ElementPath || \"\");\r\n\t\t\t\t\t\tif (prevElement) {\r\n\t\t\t\t\t\t\tconst rect = prevElement.getBoundingClientRect();\r\n\t\t\t\t\t\t\tconst isOutOfView = (\r\n\t\t\t\t\t\t\t\trect.bottom < 0 ||\r\n\t\t\t\t\t\t\t\trect.top > window.innerHeight ||\r\n\t\t\t\t\t\t\t\trect.right < 0 ||\r\n\t\t\t\t\t\t\t\trect.left > window.innerWidth\r\n\t\t\t\t\t\t\t);\r\n\r\n\t\t\t\t\t\t\tif (isOutOfView) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Previous hotspot element out of view, scrolling\");\r\n\t\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\t\tprevElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\t\tconsole.log(\"HandlePrevious: Hotspot element scroll failed:\", error);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\tconst calculateOptimalWidth = () => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\treturn `${canvasProperties.Width}px`;\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, use auto width\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\treturn \"auto\";\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = 800; // Maximum width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn `${finalWidth}px`;\r\n\t};\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newWidth = calculateOptimalWidth();\r\n\t\t\tsetDynamicWidth(newWidth);\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep]);\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t\t  canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"800px\",\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t   canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"300px\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\r\n\t\t\t\tconsole.log(\"🔄 Restarting hotspot tour, checking first step element\");\r\n\r\n\t\t\t\t// Enhanced restart scrolling logic\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\t// Check if first step element exists and use gentle scroll\r\n\t\t\t\t\tconst firstElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstElement) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tfirstElement.scrollIntoView({\r\n\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\tblock: 'center',\r\n\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Gentle restart scroll to first hotspot completed\");\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.log(\"❌ Gentle restart scroll failed, scrolling to top\");\r\n\t\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log(\"❌ First hotspot step element not found, scrolling to top\");\r\n\t\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// No xpath available, just scroll to top\r\n\t\t\t\t\tconsole.log(\"ℹ️ No xpath for first hotspot step, scrolling to top\");\r\n\t\t\t\t\twindow.scrollTo({ top: 0, behavior: 'smooth' });\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element;\r\n\t\tlet steps;\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top +10+ (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left +10+ parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AACvE,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,EAAEC,OAAO,EAAiBC,UAAU,QAAQ,eAAe;AAE1H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE;AACA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA6GrD,MAAMC,cAAoC,GAAGA,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC,cAAc;EACdC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,kBAAkB;EACnBC;AAEH,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IACLC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACD,CAAC,GAAG5D,cAAc,CAAE6D,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1E,QAAQ,CAAqB,IAAI,CAAC;EAC5E;EACA;EACA,MAAM,CAAC2E,aAAa,EAAEC,gBAAgB,CAAC,GAAG5E,QAAQ,CAAuC,IAAI,CAAC;EAC9F,MAAM,CAAC6E,YAAY,EAAEC,eAAe,CAAC,GAAG9E,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC+E,WAAW,EAAEC,cAAc,CAAC,GAAGhF,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAMiF,UAAU,GAAGhF,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMiF,kBAAkB,GAAGjF,MAAM,CAAiB,IAAI,CAAC;EACvD,IAAIkF,OAAY;EAChB,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;IAChE,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IACvB,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,EAAEE,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;IAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;IACnC,IAAID,IAAI,YAAYE,WAAW,EAAE;MAChC,OAAOF,IAAI;IACZ,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;MAC/B,OAAOH,IAAI,CAACG,aAAa,CAAC,CAAC;IAC5B,CAAC,MAAM;MACN,OAAO,IAAI;IACZ;EACD,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAACC,OAAoB,EAAEC,SAAiB,EAAEC,QAAgB,GAAG,GAAG,KAAK;IAC3F;IACA,MAAMC,SAAS,GAAGH,OAAO,CAACI,YAAY,GAAGJ,OAAO,CAACK,YAAY;IAC7D,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,SAAS,EAAEE,SAAS,CAAC,CAAC;;IAEpE;IACA,IAAI;MACH,IAAI,UAAU,IAAIH,OAAO,IAAI,OAAOA,OAAO,CAACU,QAAQ,KAAK,UAAU,EAAE;QACpEV,OAAO,CAACU,QAAQ,CAAC;UAChBC,GAAG,EAAEL,gBAAgB;UACrBM,QAAQ,EAAE;QACX,CAAC,CAAC;QACF;MACD;IACD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACfC,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;IACxE;;IAEA;IACA,IAAI;MACH,MAAMC,QAAQ,GAAGhB,OAAO,CAACiB,SAAS;MAClC,MAAMC,QAAQ,GAAGZ,gBAAgB,GAAGU,QAAQ;MAC5C,MAAMG,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;MAEnC,MAAMC,aAAa,GAAIC,WAAmB,IAAK;QAC9C,MAAMC,OAAO,GAAGD,WAAW,GAAGJ,SAAS;QACvC,MAAMrF,QAAQ,GAAGyE,IAAI,CAACE,GAAG,CAACe,OAAO,GAAGtB,QAAQ,EAAE,CAAC,CAAC;;QAEhD;QACA,MAAMuB,cAAc,GAAIC,CAAS,IAAKA,CAAC,GAAG,GAAG,GAAG,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,GAAG,CAAC,KAAK,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;QACvG,MAAMC,aAAa,GAAGF,cAAc,CAAC3F,QAAQ,CAAC;QAE9CkE,OAAO,CAACiB,SAAS,GAAGD,QAAQ,GAAIE,QAAQ,GAAGS,aAAc;QAEzD,IAAI7F,QAAQ,GAAG,CAAC,EAAE;UACjB8F,qBAAqB,CAACN,aAAa,CAAC;QACrC;MACD,CAAC;MAEDM,qBAAqB,CAACN,aAAa,CAAC;IACrC,CAAC,CAAC,OAAOT,KAAK,EAAE;MACfC,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACpE;MACAf,OAAO,CAACiB,SAAS,GAAGX,gBAAgB;IACrC;EACD,CAAC;;EAED;EACA,MAAMuB,iBAAiB,GAAGA,CAAC7B,OAA6B,EAAE8B,OAAsE,KAAK;IACpI,MAAMC,QAAQ,GAAG/B,OAAO,KAAKgC,MAAM;IACnC,MAAMvD,aAAa,GAAGsD,QAAQ,GAAGxC,QAAQ,CAAC0C,eAAe,GAAGjC,OAAsB;;IAElF;IACA,IAAI,CAAC+B,QAAQ,IAAI,UAAU,IAAI/B,OAAO,IAAI,OAAQA,OAAO,CAASU,QAAQ,KAAK,UAAU,EAAE;MAC1F,IAAI;QACFV,OAAO,CAASU,QAAQ,CAACoB,OAAO,CAAC;QAClC,OAAO,IAAI;MACZ,CAAC,CAAC,OAAOjB,KAAK,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,KAAK,CAAC;MACzD;IACD;;IAEA;IACA,IAAIkB,QAAQ,IAAID,OAAO,CAAClB,QAAQ,KAAK,QAAQ,EAAE;MAC9C,IAAI;QACHoB,MAAM,CAACtB,QAAQ,CAACoB,OAAO,CAAC;QACxB,OAAO,IAAI;MACZ,CAAC,CAAC,OAAOjB,KAAK,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,KAAK,CAAC;MAC9C;IACD;;IAEA;IACA,IAAIiB,OAAO,CAAClB,QAAQ,KAAK,QAAQ,IAAIkB,OAAO,CAACnB,GAAG,KAAKuB,SAAS,EAAE;MAC/D,IAAI;QACHnC,cAAc,CAACtB,aAAa,EAAEqD,OAAO,CAACnB,GAAG,CAAC;QAC1C,OAAO,IAAI;MACZ,CAAC,CAAC,OAAOE,KAAK,EAAE;QACfC,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEF,KAAK,CAAC;MACtD;IACD;;IAEA;IACA,IAAI;MACH,IAAIiB,OAAO,CAACnB,GAAG,KAAKuB,SAAS,EAAE;QAC9BzD,aAAa,CAACwC,SAAS,GAAGa,OAAO,CAACnB,GAAG;MACtC;MACA,IAAImB,OAAO,CAACK,IAAI,KAAKD,SAAS,EAAE;QAC/BzD,aAAa,CAAC2D,UAAU,GAAGN,OAAO,CAACK,IAAI;MACxC;MACA,OAAO,IAAI;IACZ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,KAAK,CAAC;MACxD,OAAO,KAAK;IACb;EACD,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAGnI,WAAW,CAAC,CAClCmF,KAAa,EACbiD,mBAA2B,EAC3BC,cAA8C,EAC9CC,WAAmB,GAAG,EAAE,EACxBC,iBAAyB,GAAG,EAAE;EAAE;EAChCC,WAAmB,GAAG,SAAS,EAC/BC,SAAsB,KAClB;IACJ,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,eAAe,GAAGJ,iBAAiB;IACvC,IAAIK,SAAyB;IAE7B,MAAMC,IAAI,GAAGA,CAAA,KAAM;MAClBH,QAAQ,EAAE;MACV9B,OAAO,CAACC,GAAG,CAAC,MAAM2B,WAAW,oBAAoBE,QAAQ,IAAIJ,WAAW,EAAE,CAAC;;MAE3E;MACA,IAAIxC,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,CAAC;MACtC,IAAI,CAACW,OAAO,IAAIsC,mBAAmB,EAAE;QACpCtC,OAAO,GAAGZ,iBAAiB,CAACkD,mBAAmB,CAAC;MACjD;MAEA,IAAItC,OAAO,EAAE;QACZc,OAAO,CAACC,GAAG,CAAC,KAAK2B,WAAW,gBAAgBE,QAAQ,WAAW,CAAC;QAChEL,cAAc,CAACvC,OAAO,CAAC;QACvB;MACD;MAEA,IAAI4C,QAAQ,IAAIJ,WAAW,EAAE;QAC5B1B,OAAO,CAACC,GAAG,CAAC,KAAK2B,WAAW,oBAAoBF,WAAW,WAAW,CAAC;QACvE,IAAIG,SAAS,EAAE;UACdA,SAAS,CAAC,CAAC;QACZ;QACA;MACD;;MAEA;MACA,MAAMK,MAAM,GAAGzC,IAAI,CAAC0C,MAAM,CAAC,CAAC,GAAG,GAAG,GAAGJ,eAAe;MACpD,MAAMK,YAAY,GAAG3C,IAAI,CAACE,GAAG,CAACoC,eAAe,GAAG,GAAG,GAAGG,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;MACpEH,eAAe,GAAGK,YAAY;MAE9BJ,SAAS,GAAGK,UAAU,CAACJ,IAAI,EAAEF,eAAe,CAAC;IAC9C,CAAC;;IAED;IACAE,IAAI,CAAC,CAAC;;IAEN;IACA,MAAMK,OAAO,GAAGA,CAAA,KAAM;MACrB,IAAIN,SAAS,EAAE;QACdO,YAAY,CAACP,SAAS,CAAC;MACxB;IACD,CAAC;;IAED;IACA,OAAOM,OAAO;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,qBAAqB,GAAGpJ,WAAW,CAAC,OAAOuE,aAA0B,EAAE8E,SAA8C,GAAG,KAAK,KAAK;IACvI,IAAI,CAAC9E,aAAa,EAAE;MACnBqC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MAChE;IACD;IAEAD,OAAO,CAACC,GAAG,CAAC,6DAA6D,EAAE;MAC1Ef,OAAO,EAAEvB,aAAa;MACtB+E,OAAO,EAAE/E,aAAa,CAAC+E,OAAO;MAC9BC,SAAS,EAAEhF,aAAa,CAACgF,SAAS;MAClCC,EAAE,EAAEjF,aAAa,CAACiF,EAAE;MACpBH,SAAS,EAAEA;IACZ,CAAC,CAAC;IAEF,IAAI;MACH;MACA,MAAMI,oBAAoB,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,YAAY;QAC/D,MAAMC,IAAI,GAAGtF,aAAa,CAACuF,qBAAqB,CAAC,CAAC;QAClD,MAAMC,cAAc,GAAGjC,MAAM,CAACkC,WAAW;QACzC,MAAMC,aAAa,GAAGnC,MAAM,CAACoC,UAAU;;QAEvC;QACA,IAAIC,eAAe,GAAGrC,MAAM,CAACsC,OAAO;QACpC,IAAIC,gBAAgB,GAAGvC,MAAM,CAACwC,OAAO;;QAErC;QACA,QAAQjB,SAAS;UAChB,KAAK,KAAK;YACT;YACAc,eAAe,GAAGrC,MAAM,CAACsC,OAAO,GAAGP,IAAI,CAACpD,GAAG,GAAIsD,cAAc,GAAG,GAAI;YACpE;UACD,KAAK,QAAQ;YACZ;YACAI,eAAe,GAAGrC,MAAM,CAACsC,OAAO,GAAGP,IAAI,CAACpD,GAAG,GAAIsD,cAAc,GAAG,GAAI;YACpE;UACD,KAAK,MAAM;YACV;YACAI,eAAe,GAAGrC,MAAM,CAACsC,OAAO,GAAGP,IAAI,CAACpD,GAAG,GAAIsD,cAAc,GAAG,GAAI;YACpEM,gBAAgB,GAAGvC,MAAM,CAACwC,OAAO,GAAGT,IAAI,CAAC5B,IAAI,GAAIgC,aAAa,GAAG,GAAI;YACrE;UACD,KAAK,OAAO;YACX;YACAE,eAAe,GAAGrC,MAAM,CAACsC,OAAO,GAAGP,IAAI,CAACpD,GAAG,GAAIsD,cAAc,GAAG,GAAI;YACpEM,gBAAgB,GAAGvC,MAAM,CAACwC,OAAO,GAAGT,IAAI,CAAC5B,IAAI,GAAIgC,aAAa,GAAG,GAAI;YACrE;UACD;YACC;YACAE,eAAe,GAAGrC,MAAM,CAACsC,OAAO,GAAGP,IAAI,CAACpD,GAAG,GAAIsD,cAAc,GAAG,GAAI;QACtE;;QAEA;QACA,MAAMQ,YAAY,GAAGlE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,QAAQ,CAAC0C,eAAe,CAAC7B,YAAY,GAAG6D,cAAc,CAAC;QACxF,MAAMS,aAAa,GAAGnE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEjB,QAAQ,CAAC0C,eAAe,CAAC0C,WAAW,GAAGR,aAAa,CAAC;QAEvFE,eAAe,GAAG9D,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC4D,eAAe,EAAEI,YAAY,CAAC,CAAC;QACtEF,gBAAgB,GAAGhE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAAC8D,gBAAgB,EAAEG,aAAa,CAAC,CAAC;QAEzE5D,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE;UACrDsD,eAAe;UACfE,gBAAgB;UAChBK,cAAc,EAAE5C,MAAM,CAACsC,OAAO;UAC9BO,cAAc,EAAE7C,MAAM,CAACwC,OAAO;UAC9BM,WAAW,EAAEf;QACd,CAAC,CAAC;;QAEF;QACA,IAAIgB,aAAa,GAAG,KAAK;;QAEzB;QACA,IAAI;UACHA,aAAa,GAAGlD,iBAAiB,CAACG,MAAM,EAAE;YACzCrB,GAAG,EAAE0D,eAAe;YACpBlC,IAAI,EAAEoC,gBAAgB;YACtB3D,QAAQ,EAAE;UACX,CAAC,CAAC;UACFE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEgE,aAAa,CAAC;QAClE,CAAC,CAAC,OAAOlE,KAAK,EAAE;UACfC,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEF,KAAK,CAAC;QACzD;;QAEA;QACA,IAAI,CAACkE,aAAa,EAAE;UACnB,IAAI;YACH/C,MAAM,CAACtB,QAAQ,CAAC6D,gBAAgB,EAAEF,eAAe,CAAC;YAClDU,aAAa,GAAG,IAAI;YACpBjE,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UACpD,CAAC,CAAC,OAAOF,KAAK,EAAE;YACfC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,KAAK,CAAC;UACxD;QACD;;QAEA;QACA,IAAI,CAACkE,aAAa,EAAE;UACnB,IAAI;YACHxF,QAAQ,CAAC0C,eAAe,CAAChB,SAAS,GAAGoD,eAAe;YACpD9E,QAAQ,CAAC0C,eAAe,CAACG,UAAU,GAAGmC,gBAAgB;YACtDhF,QAAQ,CAACyF,IAAI,CAAC/D,SAAS,GAAGoD,eAAe;YACzC9E,QAAQ,CAACyF,IAAI,CAAC5C,UAAU,GAAGmC,gBAAgB;YAC3CzD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;UACtD,CAAC,CAAC,OAAOF,KAAK,EAAE;YACfC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEF,KAAK,CAAC;UAC3D;QACD;;QAEA;QACA,MAAM,IAAI+C,OAAO,CAACC,OAAO,IAAIV,UAAU,CAACU,OAAO,EAAE,GAAG,CAAC,CAAC;QAEtD/C,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAClD,CAAC,CAAC;MAEF,MAAM4C,oBAAoB;IAC3B,CAAC,CAAC,OAAO9C,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACvD;EACD,CAAC,EAAE,CAACd,cAAc,EAAE8B,iBAAiB,CAAC,CAAC;EAEvC,IAAIxC,KAAU;EACd,IAAI7C,cAAc,EAAE6C,KAAK,GAAG7C,cAAc,aAAdA,cAAc,wBAAAO,qBAAA,GAAdP,cAAc,CAAEyI,SAAS,cAAAlI,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,uBAA9BA,sBAAA,CAAgCkI,WAAW;EACvE,MAAMC,kBAAkB,GAAI9F,KAAyB,IAAK;IACzD,MAAMW,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,IAAI,EAAE,CAAC;IAC9C,IAAIW,OAAO,EAAE;MACZ,MAAM+D,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNrD,GAAG,EAAEoD,IAAI,CAACpD,GAAG;QAAE;QACfwB,IAAI,EAAE4B,IAAI,CAAC5B,IAAI,CAAE;MAClB,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACC;EACA,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGrL,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAMsL,YAAY,GAAGrL,MAAM,CAAM,IAAI,CAAC;EACxC;EACA,MAAMsL,sBAAsB,GAAGA,CAACT,WAAoB,EAAE/F,WAAmB,EAAEyG,OAAe,EAAEC,OAAe,KAAK;IAC/G,MAAMC,WAAW,GAAGZ,WAAW,CAACa,CAAC,GAAGH,OAAO;IAC3C,MAAMI,UAAU,GAAGd,WAAW,CAACe,CAAC,GAAGJ,OAAO;;IAE1C;IACA,MAAMK,cAAc,GAAG/G,WAAW,GAAG,CAAC,CAAC,CAAC;IACxC,MAAMgH,cAAc,GAAGhH,WAAW,GAAG,EAAE,CAAC,CAAC;;IAEzC,OAAO;MACN4B,GAAG,EAAEiF,UAAU,GAAG5D,MAAM,CAACsC,OAAO,GAAGyB,cAAc;MACjD5D,IAAI,EAAEuD,WAAW,GAAG1D,MAAM,CAACwC,OAAO,GAAGsB;IACtC,CAAC;EACF,CAAC;EACD/L,SAAS,CAAC,MAAM;IACf,MAAMiG,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,CAAC;IACxC,IAAIW,OAAO,EAAE;MACZ,MAAM+D,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;MAC5CpF,gBAAgB,CAAC;QAChB+B,GAAG,EAAEoD,IAAI,CAACpD,GAAG,GAAGqB,MAAM,CAACsC,OAAO;QAAE;QAChCnC,IAAI,EAAE4B,IAAI,CAAC5B,IAAI,GAAGH,MAAM,CAACwC;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACnF,KAAK,CAAC,CAAC;EACXtF,SAAS,CAAC,MAAM;IACf,IAAI,OAAOiI,MAAM,KAAKE,SAAS,EAAE;MAChC,MAAM8D,QAAQ,GAAGb,kBAAkB,CAAC9F,KAAK,IAAI,EAAE,CAAC;MAChD,IAAI2G,QAAQ,EAAE;QACbpH,gBAAgB,CAACoH,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAAC3G,KAAK,CAAC,CAAC;EACXtF,SAAS,CAAC,MAAM;IACf,MAAMiG,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,CAAC;IACxC;IACA,IAAIW,OAAO,EAAE,CACb;EACD,CAAC,EAAE,CAACxD,cAAc,CAAC,CAAC;EAEpBzC,SAAS,CAAC,MAAM;IAAA,IAAAkM,UAAA;IACf,MAAMjG,OAAO,GAAGZ,iBAAiB,CAACjE,SAAS,aAATA,SAAS,wBAAA8K,UAAA,GAAT9K,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAsK,UAAA,uBAA5BA,UAAA,CAA8Bf,WAAW,CAAC;IAC5ExG,gBAAgB,CAACsB,OAAO,CAAC;IACzB,IAAIA,OAAO,EAAE;MACZA,OAAO,CAACkG,KAAK,CAACC,eAAe,GAAG,gBAAgB;;MAEhD;MACA,MAAMpC,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;MAC5CpF,gBAAgB,CAAC;QAChB+B,GAAG,EAAEoD,IAAI,CAACpD,GAAG,GAAGqB,MAAM,CAACsC,OAAO;QAC9BnC,IAAI,EAAE4B,IAAI,CAAC5B,IAAI,GAAGH,MAAM,CAACwC;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACrJ,SAAS,EAAEQ,WAAW,CAAC,CAAC;;EAE5B;EACA5B,SAAS,CAAC,MAAM;IAAA,IAAAqM,sBAAA;IACf,MAAMC,eAAe,GAAG7J,cAAc,aAAdA,cAAc,wBAAA4J,sBAAA,GAAd5J,cAAc,CAAEyI,SAAS,cAAAmB,sBAAA,uBAAzBA,sBAAA,CAA4BzK,WAAW,GAAG,CAAC,CAAC;IACpE,IAAI,EAAC0K,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEnB,WAAW,GAAE;MAClC;IACD;;IAEA;IACA,MAAMpC,SAAS,GAAGK,UAAU,CAAC,MAAM;MAClC,MAAMmD,cAAc,GAAGlH,iBAAiB,CAACiH,eAAe,CAACnB,WAAW,IAAI,EAAE,CAAC;MAE3E,IAAIoB,cAAc,EAAE;QACnB,MAAMvC,IAAI,GAAGuC,cAAc,CAACtC,qBAAqB,CAAC,CAAC;QACnD,MAAMC,cAAc,GAAGjC,MAAM,CAACkC,WAAW;QACzC,MAAMC,aAAa,GAAGnC,MAAM,CAACoC,UAAU;;QAEvC;QACA,MAAMmC,qBAAqB,GAC1BxC,IAAI,CAACyC,MAAM,GAAG,CAAC;QAAI;QACnBzC,IAAI,CAACpD,GAAG,GAAGsD,cAAc;QAAI;QAC7BF,IAAI,CAAC0C,KAAK,GAAG,CAAC;QAAI;QAClB1C,IAAI,CAAC5B,IAAI,GAAGgC,aAAa,CAAC;QAC1B;QAED,IAAIoC,qBAAqB,EAAE;UAC1BzF,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;UACjF,IAAI;YACHuF,cAAc,CAACI,cAAc,CAAC;cAC7B9F,QAAQ,EAAE,QAAQ;cAClB+F,KAAK,EAAE,QAAQ;cACfC,MAAM,EAAE;YACT,CAAC,CAAC;UACH,CAAC,CAAC,OAAO/F,KAAK,EAAE;YACfC,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEF,KAAK,CAAC;UAC/D;QACD;MACD;IACD,CAAC,EAAE,GAAG,CAAC;IAEP,OAAO,MAAMwC,YAAY,CAACP,SAAS,CAAC;EACrC,CAAC,EAAE,CAACnH,WAAW,EAAEa,cAAc,CAAC,CAAC;;EAEjC;EACA;EACA,MAAM,GAAGqK,eAAe,CAAC,GAAG7M,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM8M,cAAc,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAInJ,gBAAgB,KAAK,MAAM,EAAE;MAChC,IAAIhC,WAAW,GAAGC,UAAU,EAAE;QAAA,IAAAmL,sBAAA;QAC7B;QACA,MAAMC,YAAY,GAAGxK,cAAc,aAAdA,cAAc,wBAAAuK,sBAAA,GAAdvK,cAAc,CAAEyI,SAAS,cAAA8B,sBAAA,uBAAzBA,sBAAA,CAA4BpL,WAAW,CAAC,CAAC,CAAC;QAC/D,IAAIqL,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE9B,WAAW,EAAE;UAC9BpE,OAAO,CAACC,GAAG,CAAC,4DAA4D,EAAE;YACzE1B,KAAK,EAAE2H,YAAY,CAAC9B,WAAW;YAC/B+B,SAAS,EAAEtL,WAAW,GAAG,CAAC;YAC1BuL,SAAS,EAAE,QAAQvL,WAAW,GAAG,CAAC;UACnC,CAAC,CAAC;;UAEF;UACA,MAAMwL,aAAa,GAAG,IAAIvD,OAAO,CAAQC,OAAO,IAAK;YACpDxB,cAAc,CACb2E,YAAY,CAAC9B,WAAW,IAAI,EAAE,EAC9B,EAAE;YAAE;YACJ,MAAOkC,YAAyB,IAAK;cACpCtG,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;cAEjE,MAAMgD,IAAI,GAAGqD,YAAY,CAACpD,qBAAqB,CAAC,CAAC;cACjD,MAAMC,cAAc,GAAGjC,MAAM,CAACkC,WAAW;cACzC,MAAMC,aAAa,GAAGnC,MAAM,CAACoC,UAAU;;cAEvC;cACA,MAAMiD,mBAAmB,GACxBtD,IAAI,CAACpD,GAAG,IAAI,CAAC,EAAE,IAAIoD,IAAI,CAACpD,GAAG,IAAIsD,cAAc,GAAG,GAAG,IACnDF,IAAI,CAAC5B,IAAI,IAAI,CAAC,EAAE,IAAI4B,IAAI,CAAC5B,IAAI,IAAIgC,aAAa,GAAG,GAAG,IACpDJ,IAAI,CAACyC,MAAM,GAAG,GAAG,IAAIzC,IAAI,CAAC0C,KAAK,GAAG,GAClC;cAED,IAAIY,mBAAmB,EAAE;gBACxBvG,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;gBAC/E;gBACA,IAAI;kBACHqG,YAAY,CAACV,cAAc,CAAC;oBAC3B9F,QAAQ,EAAE,QAAQ;oBAClB+F,KAAK,EAAE,SAAS;oBAAE;oBAClBC,MAAM,EAAE;kBACT,CAAC,CAAC;gBACH,CAAC,CAAC,OAAO/F,KAAK,EAAE;kBACfC,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEF,KAAK,CAAC;gBAChE;cACD,CAAC,MAAM;gBACNC,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;gBACnF;gBACA,IAAI;kBACH,MAAMuC,qBAAqB,CAAC8D,YAAY,EAAE,KAAK,CAAC;kBAChDtG,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;gBACrE,CAAC,CAAC,OAAOuG,WAAW,EAAE;kBACrBxG,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEyG,WAAW,CAAC;gBACrE;cACD;cAEAzD,OAAO,CAAC,CAAC;YACV,CAAC,EACD,EAAE;YAAE;YACJ,EAAE;YAAE;YACJ,sBAAsB,EACtB,MAAM;cACL/C,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;cACxF8C,OAAO,CAAC,CAAC;YACV,CACD,CAAC;UACF,CAAC,CAAC;UAEF,IAAI;YACH,MAAMsD,aAAa;YACnBrG,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACpE,CAAC,CAAC,OAAOF,KAAK,EAAE;YACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UACzD;QACD;QAEAnD,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;QAC/BF,UAAU,CAAC,CAAC;QACZ8L,eAAe,CAAC5L,WAAW,GAAGC,UAAU,CAAC;MAC1C;IACD,CAAC,MAAM;MAAA,IAAA4L,sBAAA;MACN;MACA,MAAMR,YAAY,GAAGxK,cAAc,aAAdA,cAAc,wBAAAgL,sBAAA,GAAdhL,cAAc,CAAEyI,SAAS,cAAAuC,sBAAA,uBAAzBA,sBAAA,CAA4B7L,WAAW,CAAC,CAAC,CAAC;MAC/D,IAAIqL,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE9B,WAAW,EAAE;QAC9BpE,OAAO,CAACC,GAAG,CAAC,iEAAiE,EAAE;UAC9E1B,KAAK,EAAE2H,YAAY,CAAC9B,WAAW;UAC/B+B,SAAS,EAAEtL,WAAW,GAAG,CAAC;UAC1BuL,SAAS,EAAE,QAAQvL,WAAW,GAAG,CAAC;QACnC,CAAC,CAAC;;QAEF;QACA,MAAMwL,aAAa,GAAG,IAAIvD,OAAO,CAAQC,OAAO,IAAK;UACpDxB,cAAc,CACb2E,YAAY,CAAC9B,WAAW,IAAI,EAAE,EAC9B,EAAE;UAAE;UACJ,MAAOkC,YAAyB,IAAK;YACpCtG,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;YAEtE,MAAMgD,IAAI,GAAGqD,YAAY,CAACpD,qBAAqB,CAAC,CAAC;YACjD,MAAMC,cAAc,GAAGjC,MAAM,CAACkC,WAAW;YACzC,MAAMC,aAAa,GAAGnC,MAAM,CAACoC,UAAU;;YAEvC;YACA,MAAMiD,mBAAmB,GACxBtD,IAAI,CAACpD,GAAG,IAAI,CAAC,EAAE,IAAIoD,IAAI,CAACpD,GAAG,IAAIsD,cAAc,GAAG,GAAG,IACnDF,IAAI,CAAC5B,IAAI,IAAI,CAAC,EAAE,IAAI4B,IAAI,CAAC5B,IAAI,IAAIgC,aAAa,GAAG,GAAG,IACpDJ,IAAI,CAACyC,MAAM,GAAG,GAAG,IAAIzC,IAAI,CAAC0C,KAAK,GAAG,GAClC;YAED,IAAIY,mBAAmB,EAAE;cACxBvG,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;cACpF;cACA,IAAI;gBACHqG,YAAY,CAACV,cAAc,CAAC;kBAC3B9F,QAAQ,EAAE,QAAQ;kBAClB+F,KAAK,EAAE,SAAS;kBAAE;kBAClBC,MAAM,EAAE;gBACT,CAAC,CAAC;cACH,CAAC,CAAC,OAAO/F,KAAK,EAAE;gBACfC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,KAAK,CAAC;cACrE;YACD,CAAC,MAAM;cACNC,OAAO,CAACC,GAAG,CAAC,2EAA2E,CAAC;cACxF;cACA,IAAI;gBACH,MAAMuC,qBAAqB,CAAC8D,YAAY,EAAE,KAAK,CAAC;gBAChDtG,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;cAC1E,CAAC,CAAC,OAAOuG,WAAW,EAAE;gBACrBxG,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEyG,WAAW,CAAC;cAC1E;YACD;YAEAzD,OAAO,CAAC,CAAC;UACV,CAAC,EACD,EAAE;UAAE;UACJ,EAAE;UAAE;UACJ,2BAA2B,EAC3B,MAAM;YACL/C,OAAO,CAACC,GAAG,CAAC,gFAAgF,CAAC;YAC7F8C,OAAO,CAAC,CAAC;UACV,CACD,CAAC;QACF,CAAC,CAAC;QAEF,IAAI;UACH,MAAMsD,aAAa;UACnBrG,OAAO,CAACC,GAAG,CAAC,2DAA2D,CAAC;QACzE,CAAC,CAAC,OAAOF,KAAK,EAAE;UACfC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC9D;MACD;MAEAnD,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/B,MAAM8L,eAAe,GAAGlI,QAAQ,CAACmI,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACvB,KAAK,CAACyB,OAAO,GAAG,MAAM;QACtCF,eAAe,CAACG,MAAM,CAAC,CAAC;MACzB;IACD;EACD,CAAC;EAED,MAAML,eAAe,GAAIM,qBAA8B,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,OAAOV,qBAAqB,gBAC3B/M,OAAA,CAACG,cAAc;MACd2B,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCH,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCzB,QAAQ,EAAEA,QAAS;MACnBsB,cAAc,EAAEA,cAAe;MAC/BrB,SAAS,EAAEA,SAAU;MACrBI,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEgN,cAAe;MAC3B/M,UAAU,EAAEqL,cAAe;MAC3B1L,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAK;MACXC,QAAQ,EAAEA,QAAS;MACnBK,WAAW,EAAEA,WAAW,GAAG,CAAE;MAC7BC,UAAU,EAAEA,UAAW;MACvBC,eAAe,EAAEA,eAAgB;MACjCC,QAAQ,EAAEA,QAAS;MACnBC,mBAAmB,EAAES,cAAc,aAAdA,cAAc,wBAAAsL,sBAAA,GAAdtL,cAAc,CAAEyI,SAAS,cAAA6C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BnM,WAAW,CAAC,cAAAoM,sBAAA,uBAAxCA,sBAAA,CAA0CU,mBAAoB;MACnFzM,eAAe,EAAEQ,cAAc,aAAdA,cAAc,wBAAAwL,sBAAA,GAAdxL,cAAc,CAAEyI,SAAS,cAAA+C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BrM,WAAW,CAAC,cAAAsM,sBAAA,uBAAxCA,sBAAA,CAA0CS,eAAgB;MAC3EzM,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAA0L,uBAAA,GAAd1L,cAAc,CAAEyI,SAAS,cAAAiD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BvM,WAAW,CAAC,cAAAwM,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0CQ,aAAa,cAAAP,uBAAA,wBAAAC,uBAAA,GAAvDD,uBAAA,CAAyDQ,GAAG,CAAEC,OAAY,IACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;QAC3C,GAAGA,MAAM;QACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAAZ,uBAAA,uBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,EAAEC,IAAS,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACvE;MACDlN,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,WAAW,EAAEA,WAAY;MACzBG,YAAY,EAAEA,YAAa;MAC3BE,iBAAiB,EAAE,CAAAD,cAAc,aAAdA,cAAc,wBAAA8L,uBAAA,GAAd9L,cAAc,CAAEyI,SAAS,cAAAqD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B3M,WAAW,GAAG,CAAC,CAAC,cAAA4M,uBAAA,uBAA5CA,uBAAA,CAA8Ce,OAAO,KAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,GACC,IAAI;EACT,CAAC;EAED,MAAMlB,cAAc,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI7M,WAAW,GAAG,CAAC,EAAE;MACpBmF,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;QACtDpF,WAAW,EAAEA,WAAW;QACxBgO,QAAQ,EAAEhO,WAAW,GAAG;MACzB,CAAC,CAAC;;MAEF;MACA,IAAIA,WAAW,GAAG,CAAC,KAAK,CAAC,EAAE;QAC1BmF,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9E,IAAI;UACHiB,MAAM,CAACtB,QAAQ,CAAC;YACfC,GAAG,EAAE,CAAC;YACNC,QAAQ,EAAE;UACX,CAAC,CAAC;QACH,CAAC,CAAC,OAAOC,KAAK,EAAE;UACfC,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,KAAK,CAAC;QAC5D;MACD,CAAC,MAAM;QAAA,IAAA+I,uBAAA;QACN;QACA,MAAMC,YAAY,GAAGrN,cAAc,aAAdA,cAAc,wBAAAoN,uBAAA,GAAdpN,cAAc,CAAEyI,SAAS,cAAA2E,uBAAA,uBAAzBA,uBAAA,CAA4BjO,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;QACnE,IAAIkO,YAAY,aAAZA,YAAY,eAAZA,YAAY,CAAE3E,WAAW,EAAE;UAC9BpE,OAAO,CAACC,GAAG,CAAC,sEAAsE,CAAC;UAEnFoC,UAAU,CAAC,MAAM;YAChB,MAAM2G,WAAW,GAAG1K,iBAAiB,CAACyK,YAAY,CAAC3E,WAAW,IAAI,EAAE,CAAC;YACrE,IAAI4E,WAAW,EAAE;cAChB,MAAM/F,IAAI,GAAG+F,WAAW,CAAC9F,qBAAqB,CAAC,CAAC;cAChD,MAAM+F,WAAW,GAChBhG,IAAI,CAACyC,MAAM,GAAG,CAAC,IACfzC,IAAI,CAACpD,GAAG,GAAGqB,MAAM,CAACkC,WAAW,IAC7BH,IAAI,CAAC0C,KAAK,GAAG,CAAC,IACd1C,IAAI,CAAC5B,IAAI,GAAGH,MAAM,CAACoC,UACnB;cAED,IAAI2F,WAAW,EAAE;gBAChBjJ,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;gBAC9E,IAAI;kBACH+I,WAAW,CAACpD,cAAc,CAAC;oBAC1B9F,QAAQ,EAAE,QAAQ;oBAClB+F,KAAK,EAAE,QAAQ;oBACfC,MAAM,EAAE;kBACT,CAAC,CAAC;gBACH,CAAC,CAAC,OAAO/F,KAAK,EAAE;kBACfC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,KAAK,CAAC;gBACrE;cACD;YACD;UACD,CAAC,EAAE,GAAG,CAAC;QACR;MACD;MAEAnD,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/BH,UAAU,CAAC,CAAC;IACb;EACD,CAAC;EACDzB,SAAS,CAAC,MAAM;IACf,IAAIwC,YAAY,EAAE;MACjBsK,eAAe,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACNA,eAAe,CAAC,KAAK,CAAC;IACvB;EACD,CAAC,EAAE,CAACtK,YAAY,CAAC,CAAC;EAClB;EACA,MAAMyN,4BAA4B,GACjChE,QAAgB,IACqD;IACrE,QAAQA,QAAQ;MACf,KAAK,UAAU;QACd,OAAO;UACNiE,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UACrDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,WAAW;QACf,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACtDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QACzD,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,YAAY;QAChB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UACvDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF;QACC,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;IACH;EACD,CAAC;EAED,MAAM;IAAEF,YAAY;IAAEG;EAAgB,CAAC,GAAGJ,4BAA4B,CAAC,CAAA7N,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkO,QAAQ,KAAI,eAAe,CAAC;EAErH,MAAMC,SAAS,GAAG;IACjBC,UAAU,EAAExO,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,qBAAA,GAAnBlB,mBAAmB,CAAEyO,cAAc,cAAAvN,qBAAA,eAAnCA,qBAAA,CAAqCwN,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAE3O,mBAAmB,aAAnBA,mBAAmB,gBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAEyO,cAAc,cAAAtN,sBAAA,eAAnCA,sBAAA,CAAqCyN,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAA7O,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAEyO,cAAc,cAAArN,sBAAA,uBAAnCA,sBAAA,CAAqC0N,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAA/O,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEgP,SAAS,KAAI;EAC9C,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC9C;IACA,OAAO;MACNC,MAAM,EAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,EAAE,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QACtF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC1C,CAAC;IACF,CAAC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGzP,eAAe,IAAIA,eAAe,CAAC0P,MAAM,GAAG,CAAC,IAC7D1P,eAAe,CAAC2P,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGjQ,mBAAmB,IAAIA,mBAAmB,CAAC2P,MAAM,GAAG,CAAC,IACpE3P,mBAAmB,CAAC4P,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAGnQ,YAAY,IAAIA,YAAY,CAACyP,MAAM,GAAG,CAAC;IAE1D,OAAOU,UAAU,IAAI,CAACX,QAAQ,IAAI,CAACO,OAAO;EAC3C,CAAC;;EAED;EACA,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACzB,MAAMZ,QAAQ,GAAGzP,eAAe,IAAIA,eAAe,CAAC0P,MAAM,GAAG,CAAC,IAC7D1P,eAAe,CAAC2P,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGjQ,mBAAmB,IAAIA,mBAAmB,CAAC2P,MAAM,GAAG,CAAC,IACpE3P,mBAAmB,CAAC4P,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAGnQ,YAAY,IAAIA,YAAY,CAACyP,MAAM,GAAG,CAAC;IAE1D,OAAOM,OAAO,IAAI,CAACP,QAAQ,IAAI,CAACW,UAAU;EAC3C,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA,EAAAC,qBAAA;IACnC;IACA,IAAIrQ,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEsQ,KAAK,IAAI,CAACjB,cAAc,CAAC,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE;MACnE,OAAO,GAAGlQ,gBAAgB,CAACsQ,KAAK,IAAI;IACrC;;IAEA;IACA,IAAIjB,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,EAAE;MACtC,OAAO,MAAM;IACd;;IAEA;IACA,MAAMK,YAAY,GAAG,EAAAH,mBAAA,GAAAtN,UAAU,CAAC0N,OAAO,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoB5H,WAAW,KAAI,CAAC;IACzD,MAAMiI,WAAW,GAAG,EAAAJ,qBAAA,GAAAtN,kBAAkB,CAACyN,OAAO,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4B7H,WAAW,KAAI,CAAC;;IAEhE;IACA,MAAMkI,YAAY,GAAGtM,IAAI,CAACC,GAAG,CAACkM,YAAY,EAAEE,WAAW,CAAC;;IAExD;IACA,MAAME,WAAW,GAAGD,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEvC;IACA,MAAME,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMC,QAAQ,GAAG,GAAG,CAAC,CAAC;;IAEtB,MAAMC,UAAU,GAAG1M,IAAI,CAACC,GAAG,CAACuM,QAAQ,EAAExM,IAAI,CAACE,GAAG,CAACqM,WAAW,EAAEE,QAAQ,CAAC,CAAC;IAEtE,OAAO,GAAGC,UAAU,IAAI;EACzB,CAAC;;EAED;EACAlT,SAAS,CAAC,MAAM;IACf;IACA6H,qBAAqB,CAAC,MAAM;MAC3B,MAAMsL,QAAQ,GAAGZ,qBAAqB,CAAC,CAAC;MACxCxN,eAAe,CAACoO,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACH,CAAC,EAAE,CAACnR,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,EAAEN,WAAW,CAAC,CAAC;;EAErE;EACA5B,SAAS,CAAC,MAAM;IACf,IAAIsF,KAAK,IAAIN,WAAW,EAAE;MACzB,MAAMiB,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIW,OAAO,EAAE;QAAA,IAAAmN,qBAAA;QACZ,MAAMpJ,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;QAC5C,MAAMoJ,eAAe,IAAAD,qBAAA,GAAGvP,oBAAoB,CAAC,CAAC,CAAC,cAAAuP,qBAAA,uBAAvBA,qBAAA,CAAyBE,QAAQ;QACzD,MAAM7H,OAAO,GAAG8H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;QAC7D,MAAM9H,OAAO,GAAG6H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;QAE7D,MAAMC,QAAQ,GAAGlI,sBAAsB,CAACxB,IAAI,EAAEhF,WAAW,EAAEyG,OAAO,EAAEC,OAAO,CAAC;QAC5E7G,gBAAgB,CAAC6O,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAAC1O,WAAW,EAAEM,KAAK,EAAEzB,oBAAoB,CAAC,CAAC;;EAE9C;EACA7D,SAAS,CAAC,MAAM;IACf,MAAM2T,YAAY,GAAGA,CAAA,KAAM;MAC1B,IAAIrO,KAAK,IAAIN,WAAW,EAAE;QACzB,MAAMiB,OAAO,GAAGZ,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIW,OAAO,EAAE;UAAA,IAAA2N,sBAAA;UACZ,MAAM5J,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;UAC5C,MAAMoJ,eAAe,IAAAO,sBAAA,GAAG/P,oBAAoB,CAAC,CAAC,CAAC,cAAA+P,sBAAA,uBAAvBA,sBAAA,CAAyBN,QAAQ;UACzD,MAAM7H,OAAO,GAAG8H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAM9H,OAAO,GAAG6H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;UAE7D,MAAMC,QAAQ,GAAGlI,sBAAsB,CAACxB,IAAI,EAAEhF,WAAW,EAAEyG,OAAO,EAAEC,OAAO,CAAC;UAC5E7G,gBAAgB,CAAC6O,QAAQ,CAAC;QAC3B;MACD;IACD,CAAC;IAEDzL,MAAM,CAAC4L,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAM1L,MAAM,CAAC6L,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EAChE,CAAC,EAAE,CAACrO,KAAK,EAAEN,WAAW,EAAEnB,oBAAoB,CAAC,CAAC;EAE9C,MAAMkQ,cAAc,GAAG7R,YAAY,CAACiN,MAAM,CAAC,CAACC,GAAQ,EAAEJ,MAAW,KAAK;IACrE,MAAMgF,WAAW,GAAGhF,MAAM,CAACC,WAAW,IAAI,SAAS,CAAC,CAAC;IACrD,IAAI,CAACG,GAAG,CAAC4E,WAAW,CAAC,EAAE;MACtB5E,GAAG,CAAC4E,WAAW,CAAC,GAAG,EAAE;IACtB;IACA5E,GAAG,CAAC4E,WAAW,CAAC,CAACC,IAAI,CAACjF,MAAM,CAAC;IAC7B,OAAOI,GAAG;EACX,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAM8E,WAAW,GAAG;IACnBjI,QAAQ,EAAE,CAAA7J,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkO,QAAQ,KAAI,eAAe;IACvD6D,YAAY,EAAE,CAAA/R,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgS,MAAM,KAAI,KAAK;IAC/CC,WAAW,EAAE,CAAAjS,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkS,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAAnS,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEoS,WAAW,KAAI,OAAO;IACrDC,WAAW,EAAE,OAAO;IACpBrI,eAAe,EAAE,CAAAhK,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEsS,eAAe,KAAI,OAAO;IAC7DzB,QAAQ,EAAGxB,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC7DxN,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEsQ,KAAK,GAAG,GAAGtQ,gBAAgB,CAACsQ,KAAK,eAAe,GAAG,OAAO;IAChFiC,KAAK,EAAGlD,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC1DxN,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAEsQ,KAAK,GAAG,GAAGtQ,gBAAgB,CAACsQ,KAAK,eAAe,GAAG;EAC1E,CAAC;EACD,MAAMkC,aAAa,GAAG,EAAAvR,gBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkCyO,WAAW,cAAAxO,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkEsR,aAAa,KAAI,MAAM;EAC/G,MAAMC,kBAAkB,GAAIC,MAAW,IAAK;IAC3C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAC5F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACAlN,MAAM,CAACmN,QAAQ,CAACC,IAAI,GAAGJ,SAAS;MACjC,CAAC,MAAM;QACN;QACAhN,MAAM,CAACqN,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IACCF,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACI,WAAW,IAAI,UAAU,IAChCJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAC/B;QACD1G,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACNsG,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACI,WAAW,IAAI,MAAM,IAC5BJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAC3B;QACDpI,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACNgI,MAAM,CAACC,MAAM,IAAI,SAAS,IAC1BD,MAAM,CAACI,WAAW,IAAI,SAAS,EAC9B;QAAA,IAAAI,uBAAA,EAAAC,uBAAA;QACD;QACA7R,cAAc,CAAC,CAAC,CAAC;QAEjBoD,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;;QAEtE;QACA,IAAIvE,cAAc,aAAdA,cAAc,gBAAA8S,uBAAA,GAAd9S,cAAc,CAAEyI,SAAS,cAAAqK,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCrK,WAAW,EAAE;UAChD;UACA,MAAMsK,YAAY,GAAGpQ,iBAAiB,CAAC5C,cAAc,CAACyI,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UAC/E,IAAIsK,YAAY,EAAE;YACjB,IAAI;cACHA,YAAY,CAAC9I,cAAc,CAAC;gBAC3B9F,QAAQ,EAAE,QAAQ;gBAClB+F,KAAK,EAAE,QAAQ;gBACfC,MAAM,EAAE;cACT,CAAC,CAAC;cACF9F,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;YAClE,CAAC,CAAC,OAAOF,KAAK,EAAE;cACfC,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;cAC/DiB,MAAM,CAACtB,QAAQ,CAAC;gBAAEC,GAAG,EAAE,CAAC;gBAAEC,QAAQ,EAAE;cAAS,CAAC,CAAC;YAChD;UACD,CAAC,MAAM;YACNE,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;YACvEiB,MAAM,CAACtB,QAAQ,CAAC;cAAEC,GAAG,EAAE,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UAChD;QACD,CAAC,MAAM;UACN;UACAE,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;UACnEiB,MAAM,CAACtB,QAAQ,CAAC;YAAEC,GAAG,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAS,CAAC,CAAC;QAChD;MACD;IACD;IACAiG,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACD9M,SAAS,CAAC,MAAM;IAAA,IAAA0V,WAAA,EAAAC,mBAAA;IACf,IAAIvU,SAAS,aAATA,SAAS,gBAAAsU,WAAA,GAATtU,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAA8T,WAAA,gBAAAC,mBAAA,GAA5BD,WAAA,CAA8BnG,OAAO,cAAAoG,mBAAA,eAArCA,mBAAA,CAAuCC,aAAa,EAAE;MACzD;MACA1R,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,EAAEsC,cAAc,CAAC,CAAC;;EAE/D;EACAlE,SAAS,CAAC,MAAM;IACf,IAAI6C,kBAAkB,EAAE;MAAA,IAAAgT,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAM7C,eAAe,GAAG/O,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAgS,sBAAA,GAApBhS,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAiU,sBAAA,eAAvCA,sBAAA,CAAyCvC,QAAQ,GAC5GzP,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC0R,QAAQ,IAAAwC,sBAAA,GAC9CjS,oBAAoB,CAAC,CAAC,CAAC,cAAAiS,sBAAA,uBAAvBA,sBAAA,CAAyBxC,QAAQ;MACpC,MAAM6C,WAAW,GAAG7R,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAsT,uBAAA,GAAdtT,cAAc,CAAEyI,SAAS,cAAA6K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BnU,WAAW,GAAG,CAAC,CAAC,cAAAoU,uBAAA,uBAA5CA,uBAAA,CAA8CzG,OAAO,GACrD9M,cAAc,aAAdA,cAAc,wBAAAwT,uBAAA,GAAdxT,cAAc,CAAEyI,SAAS,cAAA+K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgC3G,OAAO;;MAE1C;MACA;MACA,IAAI8D,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEuC,aAAa,EAAE;QACnC;QACA1R,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACrB,kBAAkB,EAAEgB,oBAAoB,CAAC,CAAC;;EAE9C;EACA7D,SAAS,CAAC,MAAM;IACf,IAAI8C,kBAAkB,EAAE;MAAA,IAAAsT,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAMpD,eAAe,GAAG/O,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAuS,sBAAA,GAApBvS,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAwU,sBAAA,eAAvCA,sBAAA,CAAyC9C,QAAQ,GAC5GzP,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC0R,QAAQ,IAAA+C,sBAAA,GAC9CxS,oBAAoB,CAAC,CAAC,CAAC,cAAAwS,sBAAA,uBAAvBA,sBAAA,CAAyB/C,QAAQ;MACpC,MAAM6C,WAAW,GAAG7R,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAA6T,uBAAA,GAAd7T,cAAc,CAAEyI,SAAS,cAAAoL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B1U,WAAW,GAAG,CAAC,CAAC,cAAA2U,uBAAA,uBAA5CA,uBAAA,CAA8ChH,OAAO,GACrD9M,cAAc,aAAdA,cAAc,wBAAA+T,uBAAA,GAAd/T,cAAc,CAAEyI,SAAS,cAAAsL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgClH,OAAO;;MAE1C;MACA,IAAI8D,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEuC,aAAa,EAAE;QACnC;QACA1R,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACpB,kBAAkB,EAAEe,oBAAoB,CAAC,CAAC;;EAE9C;EACA7D,SAAS,CAAC,MAAM;IACf,MAAM0W,iBAAiB,GAAIC,CAAa,IAAK;MAC5C,MAAMC,cAAc,GAAGpR,QAAQ,CAACmI,cAAc,CAAC,cAAc,CAAC;;MAE9D;MACA,IAAIiJ,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,EAAE;QAChE;MACD;;MAEA;MACA;IACD,CAAC;IAEDtR,QAAQ,CAACqO,gBAAgB,CAAC,OAAO,EAAE6C,iBAAiB,CAAC;IAErD,OAAO,MAAM;MACZlR,QAAQ,CAACsO,mBAAmB,CAAC,OAAO,EAAE4C,iBAAiB,CAAC;IACzD,CAAC;EACF,CAAC,EAAE,CAAC7S,oBAAoB,CAAC,CAAC;EAC1B;EACA7D,SAAS,CAAC,MAAM;IACf,MAAM+W,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAI7R,UAAU,CAAC0N,OAAO,EAAE;QACvB;QACA1N,UAAU,CAAC0N,OAAO,CAACzG,KAAK,CAAC6K,MAAM,GAAG,MAAM;QACxC,MAAMC,aAAa,GAAG/R,UAAU,CAAC0N,OAAO,CAACvM,YAAY;QACrD,MAAM6Q,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGF,aAAa,GAAGC,eAAe;QAGpD5L,iBAAiB,CAAC6L,YAAY,CAAC;;QAE/B;QACA,IAAI5L,YAAY,CAACqH,OAAO,EAAE;UACzB;UACA,IAAIrH,YAAY,CAACqH,OAAO,CAACwE,YAAY,EAAE;YACtC7L,YAAY,CAACqH,OAAO,CAACwE,YAAY,CAAC,CAAC;UACpC;UACA;UACAhO,UAAU,CAAC,MAAM;YAChB,IAAImC,YAAY,CAACqH,OAAO,IAAIrH,YAAY,CAACqH,OAAO,CAACwE,YAAY,EAAE;cAC9D7L,YAAY,CAACqH,OAAO,CAACwE,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDL,iBAAiB,CAAC,CAAC;IAGnB,MAAMM,QAAQ,GAAG,CAChBjO,UAAU,CAAC2N,iBAAiB,EAAE,EAAE,CAAC,EACjC3N,UAAU,CAAC2N,iBAAiB,EAAE,GAAG,CAAC,EAClC3N,UAAU,CAAC2N,iBAAiB,EAAE,GAAG,CAAC,EAClC3N,UAAU,CAAC2N,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIO,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAIrS,UAAU,CAAC0N,OAAO,IAAI3K,MAAM,CAACuP,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCpO,UAAU,CAAC2N,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFO,cAAc,CAACG,OAAO,CAACvS,UAAU,CAAC0N,OAAO,CAAC;IAC3C;IAGA,IAAI1N,UAAU,CAAC0N,OAAO,IAAI3K,MAAM,CAACyP,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CtO,UAAU,CAAC2N,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFQ,gBAAgB,CAACE,OAAO,CAACvS,UAAU,CAAC0N,OAAO,EAAE;QAC5C+E,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACzO,YAAY,CAAC;MAC9B,IAAIgO,cAAc,EAAE;QACnBA,cAAc,CAACU,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIT,gBAAgB,EAAE;QACrBA,gBAAgB,CAACS,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAACpW,WAAW,CAAC,CAAC;EACjB;EACA;;EAEA,SAASqW,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EACA,MAAMC,iBAAiB,GAAGA,CAAClM,QAAgB,GAAG,eAAe,KAAK;IACjE,QAAQA,QAAQ;MACf,KAAK,aAAa;QACjB,OAAO;UAAErF,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,cAAc;QAClB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,aAAa;QACjB,OAAO;UAAEA,GAAG,EAAErF,QAAQ,KAAK,EAAE,GAAG,gBAAgB,GAAG;QAAiB,CAAC;MACtE,KAAK,cAAc;QAClB,OAAO;UAAEqF,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,UAAU;QACd,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,WAAW;QACf,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,YAAY;QAChB,OAAO;UAAEA,GAAG,EAAE;QAAgB,CAAC;MAChC;QACC,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;IAClC;EACD,CAAC;;EAEA;EACD,MAAMwR,kBAAkB,GAAGA,CAACC,QAAgB,EAAEhF,eAAoB,EAAE8C,WAAgB,KAAK;IACxF,IAAI7R,oBAAoB,KAAK,SAAS,EAAE;MACvC;MACA,QAAQ+T,QAAQ;QACf,KAAK,gBAAgB;UACpB,OAAO,CAAAlC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEmC,cAAc,MAAKnQ,SAAS,GAAGgO,WAAW,CAACmC,cAAc,GAAGjF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiF,cAAc;QAChH,KAAK,eAAe;UACnB;UACA,OAAO,CAAAnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoC,4BAA4B,MAAKpQ,SAAS,GAAGgO,WAAW,CAACoC,4BAA4B,GAAGlF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkF,4BAA4B;QAC1J,KAAK,UAAU;UACd,OAAO,CAAApC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqC,QAAQ,MAAKrQ,SAAS,GAAGgO,WAAW,CAACqC,QAAQ,GAAGnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmF,QAAQ;QAC9F,KAAK,eAAe;UACnB,OAAO,CAAArC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,aAAa,MAAKzN,SAAS,GAAGgO,WAAW,CAACP,aAAa,GAAGvC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEuC,aAAa;QAC7G;UACC,OAAOvC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACA,IAAIA,QAAQ,KAAK,eAAe,EAAE;QACjC,OAAOhF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkF,4BAA4B;MACrD;MACA,OAAOlF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;IACnC;EACD,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAACrT,OAAY,EAAEiO,eAAoB,EAAE8C,WAAgB,EAAE/N,IAAS,EAAExB,GAAQ,KAAK;IACzGxB,OAAO,CAAC+G,KAAK,CAACF,QAAQ,GAAG,UAAU;IACnC7G,OAAO,CAAC+G,KAAK,CAAC/D,IAAI,GAAG,GAAGA,IAAI,IAAI;IAChChD,OAAO,CAAC+G,KAAK,CAACvF,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC9BxB,OAAO,CAAC+G,KAAK,CAACwI,KAAK,GAAG,GAAGtB,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,IAAI,IAAI,CAAC,CAAC;IACpDtT,OAAO,CAAC+G,KAAK,CAAC6K,MAAM,GAAG,GAAG3D,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,IAAI,IAAI;IACnDtT,OAAO,CAAC+G,KAAK,CAACC,eAAe,GAAGiH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsF,KAAK;IACtDvT,OAAO,CAAC+G,KAAK,CAACgI,YAAY,GAAG,KAAK;IAClC/O,OAAO,CAAC+G,KAAK,CAACyM,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC1CxT,OAAO,CAAC+G,KAAK,CAAC0M,UAAU,GAAG,MAAM;IACjCzT,OAAO,CAAC+G,KAAK,CAAC2M,aAAa,GAAG,MAAM,CAAC,CAAC;IACtC1T,OAAO,CAAC2T,SAAS,GAAG,EAAE;IAEtB,IAAI,CAAA1F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,MAAK,MAAM,IAAI,CAAA3F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,MAAK,UAAU,EAAE;MAC7E,MAAMC,QAAQ,GAAGzT,QAAQ,CAAC0T,aAAa,CAAC,MAAM,CAAC;MAC/CD,QAAQ,CAACE,SAAS,GAAG9F,eAAe,CAAC2F,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MAChEC,QAAQ,CAAC9M,KAAK,CAAC0E,KAAK,GAAG,OAAO;MAC9BoI,QAAQ,CAAC9M,KAAK,CAACiN,QAAQ,GAAG,MAAM;MAChCH,QAAQ,CAAC9M,KAAK,CAACqE,UAAU,GAAG,MAAM;MAClCyI,QAAQ,CAAC9M,KAAK,CAACwE,SAAS,GAAG0C,eAAe,CAAC2F,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;MAChFC,QAAQ,CAAC9M,KAAK,CAACyB,OAAO,GAAG,MAAM;MAC/BqL,QAAQ,CAAC9M,KAAK,CAACkN,UAAU,GAAG,QAAQ;MACpCJ,QAAQ,CAAC9M,KAAK,CAACmN,cAAc,GAAG,QAAQ;MACxCL,QAAQ,CAAC9M,KAAK,CAACwI,KAAK,GAAG,MAAM;MAC7BsE,QAAQ,CAAC9M,KAAK,CAAC6K,MAAM,GAAG,MAAM;MAC9B5R,OAAO,CAACmU,WAAW,CAACN,QAAQ,CAAC;IAC9B;;IAEA;IACA;IACA,MAAMO,qBAAqB,GAAGpB,kBAAkB,CAAC,gBAAgB,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;IAChG,MAAMsD,WAAW,GAAGnV,oBAAoB,KAAK,SAAS,GAClDkV,qBAAqB,KAAK,KAAK,IAAI,CAACpU,OAAO,CAACsU,aAAa,GACzDrG,eAAe,IAAIjP,gBAAgB,IAAI,CAACgB,OAAO,CAACsU,aAAc;IAElE,IAAID,WAAW,EAAE;MACPrU,OAAO,CAACuU,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxCxU,OAAO,CAACuU,SAAS,CAAC9L,MAAM,CAAC,yBAAyB,CAAC;IACvD,CAAC,MAAM;MACHzI,OAAO,CAACuU,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;MAC3CzI,OAAO,CAACuU,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACpD;;IAEN;IACAxU,OAAO,CAAC+G,KAAK,CAACyB,OAAO,GAAG,MAAM;IAC9BxI,OAAO,CAAC+G,KAAK,CAAC2M,aAAa,GAAG,MAAM;;IAEpC;IACA;IACA;IACA,MAAMe,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;IACvF,IAAI0D,aAAa,EAAE;MAClB3V,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACA;IAAA;;IAGD;IACA;IACA,IAAI,CAACkB,OAAO,CAAC0U,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,MAAMC,UAAU,GAAG3U,OAAO,CAAC4U,SAAS,CAAC,IAAI,CAAgB;MACzD;MACA,IAAI5U,OAAO,CAACsU,aAAa,KAAKvR,SAAS,EAAE;QAC9B4R,UAAU,CAASL,aAAa,GAAGtU,OAAO,CAACsU,aAAa;MAC7D;MACN,IAAItU,OAAO,CAAC6U,UAAU,EAAE;QACvB7U,OAAO,CAAC6U,UAAU,CAACC,YAAY,CAACH,UAAU,EAAE3U,OAAO,CAAC;QACpDA,OAAO,GAAG2U,UAAU;MACrB;IACD;;IAEA;IACA3U,OAAO,CAAC+G,KAAK,CAAC2M,aAAa,GAAG,MAAM;;IAEpC;IACA,MAAMqB,QAAQ,GAAG/B,kBAAkB,CAAC,UAAU,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;IAC7E,MAAMiE,WAAW,GAAIzD,CAAQ,IAAK;MACjCA,CAAC,CAAC0D,eAAe,CAAC,CAAC;MACnBtT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAImT,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACAjW,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACA,MAAM2X,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;QAC9F,IAAImE,oBAAoB,EAAE;UACzBlV,OAAO,CAACuU,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;UAC3CzI,OAAO,CAACuU,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChDxU,OAAO,CAACsU,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;IAED,MAAMa,cAAc,GAAI5D,CAAQ,IAAK;MACpCA,CAAC,CAAC0D,eAAe,CAAC,CAAC;;MAEnB;MACA;MACA,MAAMR,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;MACvF,IAAIgE,QAAQ,KAAK,kBAAkB,IAAI,CAACN,aAAa,EAAE;QACtD;MAAA;IAEF,CAAC;IAED,MAAMW,WAAW,GAAI7D,CAAQ,IAAK;MACjCA,CAAC,CAAC0D,eAAe,CAAC,CAAC;MACnBtT,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAImT,QAAQ,KAAK,kBAAkB,IAAI,CAACA,QAAQ,EAAE;QACjD;QACAjW,cAAc,CAAC,CAACC,WAAW,CAAC;;QAE5B;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACJ,MAAM0X,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;QAC1F,IAAImE,oBAAoB,EAAE;UACzBlV,OAAO,CAACuU,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;UAC3CzI,OAAO,CAACuU,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChDxU,OAAO,CAACsU,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;;IAED;IACA,IAAI,CAACtU,OAAO,CAAC0U,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,IAAIK,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACA/U,OAAO,CAACyO,gBAAgB,CAAC,WAAW,EAAEuG,WAAW,CAAC;QAClDhV,OAAO,CAACyO,gBAAgB,CAAC,UAAU,EAAE0G,cAAc,CAAC;;QAEpD;QACAnV,OAAO,CAACyO,gBAAgB,CAAC,OAAO,EAAE2G,WAAW,CAAC;MAC/C,CAAC,MAAM;QACN;QACApV,OAAO,CAACyO,gBAAgB,CAAC,OAAO,EAAE2G,WAAW,CAAC;MAC/C;;MAEA;MACApV,OAAO,CAACqV,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;IACxD;EACD,CAAC;EACDza,SAAS,CAAC,MAAM;IACf,IAAIiG,OAAO;IACX,IAAIyU,KAAK;IAET,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,MAAA,EAAAC,OAAA;QACH;QACAL,KAAK,GAAG,CAAAjY,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEyI,SAAS,KAAI,EAAE;;QAEvC;QACA,MAAM8P,WAAW,GAAG1W,oBAAoB,KAAK,SAAS,IAAI7B,cAAc,aAAdA,cAAc,gBAAAmY,uBAAA,GAAdnY,cAAc,CAAEyI,SAAS,cAAA0P,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BhZ,WAAW,GAAG,CAAC,CAAC,cAAAiZ,uBAAA,eAA5CA,uBAAA,CAA8C1P,WAAW,GAC/G1I,cAAc,CAACyI,SAAS,CAACtJ,WAAW,GAAG,CAAC,CAAC,CAASuJ,WAAW,GAC9D,EAAA2P,MAAA,GAAAJ,KAAK,cAAAI,MAAA,wBAAAC,OAAA,GAALD,MAAA,CAAQ,CAAC,CAAC,cAAAC,OAAA,uBAAVA,OAAA,CAAY5P,WAAW,KAAI,EAAE;QAEhClF,OAAO,GAAGZ,iBAAiB,CAAC2V,WAAW,IAAI,EAAE,CAAC;QAC9CrW,gBAAgB,CAACsB,OAAO,CAAC;QAEzB,IAAIA,OAAO,EAAE;UACZ;QAAA;;QAGD;QACA,MAAMgV,iBAAiB,GAAGrX,gBAAgB,KAAK,SAAS,IACvDU,oBAAoB,KAAK,SAAS,IAClCjD,KAAK,KAAK,SAAS,IAClBuC,gBAAgB,KAAK,MAAM,IAAIU,oBAAoB,KAAK,SAAU;QAEpE,IAAI2W,iBAAiB,EAAE;UAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAItB;UACA,IAAIlI,eAAe;UACnB,IAAI8C,WAAW;UAEf,IAAI7R,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAqX,sBAAA,GAApBrX,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAsZ,sBAAA,eAAvCA,sBAAA,CAAyC5H,QAAQ,EAAE;YAAA,IAAAkI,uBAAA,EAAAC,uBAAA;YAC5F;YACApI,eAAe,GAAGxP,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAAC0R,QAAQ;YAChE6C,WAAW,GAAG1T,cAAc,aAAdA,cAAc,wBAAA+Y,uBAAA,GAAd/Y,cAAc,CAAEyI,SAAS,cAAAsQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B5Z,WAAW,GAAG,CAAC,CAAC,cAAA6Z,uBAAA,uBAA5CA,uBAAA,CAA8ClM,OAAO;UACpE,CAAC,MAAM,IAAI1L,oBAAoB,aAApBA,oBAAoB,gBAAAsX,sBAAA,GAApBtX,oBAAoB,CAAG,CAAC,CAAC,cAAAsX,sBAAA,eAAzBA,sBAAA,CAA2B7H,QAAQ,EAAE;YAAA,IAAAoI,uBAAA,EAAAC,uBAAA;YAC/C;YACAtI,eAAe,GAAGxP,oBAAoB,CAAC,CAAC,CAAC,CAACyP,QAAQ;YAClD6C,WAAW,GAAG1T,cAAc,aAAdA,cAAc,wBAAAiZ,uBAAA,GAAdjZ,cAAc,CAAEyI,SAAS,cAAAwQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCpM,OAAO;UACtD,CAAC,MAAM;YAAA,IAAAqM,uBAAA,EAAAC,uBAAA;YACN;YACAxI,eAAe,GAAG;cACjBG,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACduF,IAAI,EAAE,UAAU;cAChBL,KAAK,EAAE,QAAQ;cACfD,IAAI,EAAE,IAAI;cACVJ,cAAc,EAAE,IAAI;cACpBC,4BAA4B,EAAE,IAAI;cAClCC,QAAQ,EAAE,kBAAkB;cAC5B5C,aAAa,EAAE;YAChB,CAAC;YACDO,WAAW,GAAG,CAAA1T,cAAc,aAAdA,cAAc,wBAAAmZ,uBAAA,GAAdnZ,cAAc,CAAEyI,SAAS,cAAA0Q,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4Bha,WAAW,GAAG,CAAC,CAAC,cAAAia,uBAAA,uBAA5CA,uBAAA,CAA8CtM,OAAO,KAAI,CAAC,CAAC;UAC1E;UACA,MAAM9D,OAAO,GAAG8H,UAAU,CAAC,EAAA6H,gBAAA,GAAA/H,eAAe,cAAA+H,gBAAA,uBAAfA,gBAAA,CAAiB5H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAM9H,OAAO,GAAG6H,UAAU,CAAC,EAAA8H,iBAAA,GAAAhI,eAAe,cAAAgI,iBAAA,uBAAfA,iBAAA,CAAiB5H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMqI,kBAAkB,GAAGvI,UAAU,CAAC,EAAA+H,iBAAA,GAAAjI,eAAe,cAAAiI,iBAAA,uBAAfA,iBAAA,CAAiB5C,IAAI,KAAI,IAAI,CAAC;;UAEpE;UACAzT,cAAc,CAAC6W,kBAAkB,CAAC;UAElC,IAAI1T,IAAI,EAAExB,GAAG;UACb,IAAIX,OAAO,EAAE;YACZ,MAAM+D,IAAI,GAAG/D,OAAO,CAACgE,qBAAqB,CAAC,CAAC;YAC5C7B,IAAI,GAAG4B,IAAI,CAAC4B,CAAC,GAAGH,OAAO;YACvB7E,GAAG,GAAGoD,IAAI,CAAC8B,CAAC,IAAIJ,OAAO,GAAG,CAAC,GAAG,CAACA,OAAO,GAAGlF,IAAI,CAACuV,GAAG,CAACrQ,OAAO,CAAC,CAAC;;YAE3D;YACA,MAAMgI,QAAQ,GAAGlI,sBAAsB,CAACxB,IAAI,EAAE8R,kBAAkB,EAAErQ,OAAO,EAAEC,OAAO,CAAC;YACnF7G,gBAAgB,CAAC6O,QAAQ,CAAC;UAC3B;;UAEA;UACA,MAAMhG,eAAe,GAAGlI,QAAQ,CAACmI,cAAc,CAAC,cAAc,CAAC;UAC/D,IAAID,eAAe,EAAE;YACpBtI,OAAO,GAAGsI,eAAe;YACzB;UACD,CAAC,MAAM;YACN;YACAtI,OAAO,GAAGI,QAAQ,CAAC0T,aAAa,CAAC,KAAK,CAAC;YACvC9T,OAAO,CAACuE,EAAE,GAAG,cAAc,CAAC,CAAC;YAC7BvE,OAAO,CAACsU,aAAa,GAAG,KAAK,CAAC,CAAC;YAC/BlU,QAAQ,CAACyF,IAAI,CAACsO,WAAW,CAACnU,OAAO,CAAC;UACnC;UAEAA,OAAO,CAAC+G,KAAK,CAAC6P,MAAM,GAAG,SAAS;UAChC5W,OAAO,CAAC+G,KAAK,CAAC2M,aAAa,GAAG,MAAM,CAAC,CAAC;;UAEtC;UACA1T,OAAO,CAAC+G,KAAK,CAACyM,MAAM,GAAG,MAAM;;UAE7B;UACA,KAAA2C,iBAAA,GAAIlI,eAAe,cAAAkI,iBAAA,eAAfA,iBAAA,CAAiB3F,aAAa,EAAE;YACnC1R,cAAc,CAAC,IAAI,CAAC;UACrB;;UAEA;UACAuU,kBAAkB,CAACrT,OAAO,EAAEiO,eAAe,EAAE8C,WAAW,EAAE/N,IAAI,EAAExB,GAAG,CAAC;;UAEpE;UACA,MAAMiT,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE8C,WAAW,CAAC;UACvF,IAAI0D,aAAa,EAAE;YAClB3V,cAAc,CAAC,IAAI,CAAC;UACrB,CAAC,MAAM;YACN;UAAA;;UAGD;QACD;MACD,CAAC,CAAC,OAAO4C,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACD,CAAC;IAED6T,iBAAiB,CAAC,CAAC;IAEnB,OAAO,MAAM;MACZ,MAAMjN,eAAe,GAAGlI,QAAQ,CAACmI,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACuO,OAAO,GAAG,IAAI;QAC9BvO,eAAe,CAACwO,WAAW,GAAG,IAAI;QAClCxO,eAAe,CAACyO,UAAU,GAAG,IAAI;MAClC;IACD,CAAC;EACF,CAAC,EAAE,CACF1Z,cAAc,EACdoB,oBAAoB,EACpBhB,kBAAkB,EAClBC,kBAAkB,EAClBwB,oBAAoB,EACpB1C;EACA;EAAA,CACA,CAAC;EACF,MAAMwa,cAAc,GAAG,CAAA3Z,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAEyI,SAAS,cAAA1H,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgC4Y,OAAO,cAAA3Y,uBAAA,uBAAvCA,uBAAA,CAAyC4Y,cAAc,KAAI,KAAK;EAEvF,SAASC,mBAAmBA,CAAChY,cAAmB,EAAE;IAAA,IAAAiY,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACjD,IAAInY,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA9B,cAAc,aAAdA,cAAc,wBAAA+Z,uBAAA,GAAd/Z,cAAc,CAAEyI,SAAS,cAAAsR,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCJ,OAAO,cAAAK,uBAAA,uBAAvCA,uBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAAChY,cAAc,CAAC;EAC5D,MAAMsY,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIQ,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACC7b,OAAA,CAACP,aAAa;QACbsc,OAAO,EAAC,MAAM;QACdpC,KAAK,EAAE7Y,UAAW;QAClBoK,QAAQ,EAAC,QAAQ;QACjB8Q,UAAU,EAAEnb,WAAW,GAAG,CAAE;QAC5Bob,EAAE,EAAE;UACH5Q,eAAe,EAAE,aAAa;UAC9BH,QAAQ,EAAE,oBAAoB;UAC9B,+BAA+B,EAAE;YAChCG,eAAe,EAAE5H,aAAa,CAAE;UACjC;QACD,CAAE;QACFyY,UAAU,eAAElc,OAAA,CAACV,MAAM;UAAC8L,KAAK,EAAE;YAAE+Q,UAAU,EAAE;UAAS;QAAE;UAAA1N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxDwN,UAAU,eAAEpc,OAAA,CAACV,MAAM;UAAC8L,KAAK,EAAE;YAAE+Q,UAAU,EAAE;UAAS;QAAE;UAAA1N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACA,IAAIiN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACC7b,OAAA,CAACX,GAAG;QAAC4c,EAAE,EAAE;UAAEpP,OAAO,EAAE,MAAM;UAAEyL,UAAU,EAAE,QAAQ;UAAE+D,YAAY,EAAE,QAAQ;UAAEC,GAAG,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAM,CAAE;QAAAC,QAAA,EAGrGC,KAAK,CAACC,IAAI,CAAC;UAAE9L,MAAM,EAAE9P;QAAW,CAAC,CAAC,CAACgN,GAAG,CAAC,CAAC6O,CAAC,EAAEC,KAAK,kBAChD5c,OAAA;UAECoL,KAAK,EAAE;YACNwI,KAAK,EAAE,MAAM;YACbqC,MAAM,EAAE,KAAK;YACb5K,eAAe,EAAEuR,KAAK,KAAK/b,WAAW,GAAG,CAAC,GAAG4C,aAAa,GAAG,SAAS;YAAE;YACxE2P,YAAY,EAAE;UACf;QAAE,GANGwJ,KAAK;UAAAnO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;IACA,IAAIiN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACC7b,OAAA,CAACX,GAAG;QAAC4c,EAAE,EAAE;UAAEpP,OAAO,EAAE,MAAM;UAAEyL,UAAU,EAAE,QAAQ;UAAE+D,YAAY,EAAE;QAAa,CAAE;QAAAG,QAAA,eAC9Exc,OAAA,CAACL,UAAU;UAACsc,EAAE,EAAE;YAAEM,OAAO,EAAE,KAAK;YAAEzM,KAAK,EAAErM;UAAc,CAAE;UAAA+Y,QAAA,GAAC,OACpD,EAAC3b,WAAW,EAAC,MAAI,EAACC,UAAU;QAAA;UAAA2N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAIiN,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACC7b,OAAA,CAACX,GAAG;QAAAmd,QAAA,eACHxc,OAAA,CAACL,UAAU;UAACoc,OAAO,EAAC,OAAO;UAAAS,QAAA,eAC1Bxc,OAAA,CAACR,cAAc;YACduc,OAAO,EAAC,aAAa;YACrBc,KAAK,EAAE7b,QAAS;YAChBib,EAAE,EAAE;cACHhG,MAAM,EAAE,KAAK;cACX7C,YAAY,EAAE,MAAM;cACpB0J,MAAM,EAAE,UAAU;cACpB,0BAA0B,EAAE;gBAC3BzR,eAAe,EAAE5H,aAAa,CAAE;cACjC;YACD;UAAE;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACC5O,OAAA,CAAAE,SAAA;IAAAsc,QAAA,GACE7Y,aAAa,iBACb3D,OAAA;MAAAwc,QAAA,EAcEpZ,WAAW,iBACXpD,OAAA,CAACN,OAAO;QACP6U,IAAI,EAAEwI,OAAO,CAAClZ,aAAa,CAAC,IAAIkZ,OAAO,CAAC3c,QAAQ,CAAE;QAClDA,QAAQ,EAAEA,QAAS;QACnBK,OAAO,EAAEA,CAAA,KAAM;UACd;UACA;QAAA,CACC;QACF0O,YAAY,EAAEA,YAAa;QAC3BG,eAAe,EAAEA,eAAgB;QACjC0N,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EACbpZ,aAAa,GACV;UACAgC,GAAG,EAAEhC,aAAa,CAACgC,GAAG,GAAE,EAAE,IAAG2M,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAACsP,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,GAAGuC,IAAI,CAACuV,GAAG,CAACxI,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC;UAChJmE,IAAI,EAAExD,aAAa,CAACwD,IAAI,GAAE,EAAE,GAAEmL,UAAU,CAACvP,YAAY,IAAI,GAAG;QAC5D,CAAC,GACDmE,SACH;QACD6U,EAAE,EAAE;UACH;UACA;UACA;UACA,gBAAgB,EAAE7b,QAAQ,GAAG,MAAM,GAAG,MAAM;UAC5C,8CAA8C,EAAE;YAC/CyX,MAAM,EAAE,IAAI;YACZ;YACA,GAAG1E,WAAW;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,GAAGiE,iBAAiB,CAAC,CAAA/V,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkO,QAAQ,KAAI,eAAe,CAAC;YACnE1J,GAAG,EAAE,GAAG,CAAC,CAAAhC,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEgC,GAAG,KAAI,CAAC,KAC5B3C,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC3CsP,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAClC,CAACsP,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,GAChCuC,IAAI,CAACuV,GAAG,CAACxI,UAAU,CAACtP,YAAY,IAAI,GAAG,CAAC,CAAC,GAC1C,CAAC,CAAC,eAAe;YACrBmE,IAAI,EAAE,GAAG,CAAC,CAAAxD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEwD,IAAI,KAAI,CAAC,KAAKpE,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC9EuP,UAAU,CAACvP,YAAY,CAAC,IAAI,CAAC,GAC9B,CAAC,CAAE,eAAe;YACrBia,QAAQ,EAAE;UACX;QACD,CAAE;QACFC,iBAAiB,EAAE,IAAK;QAAAX,QAAA,gBAExBxc,OAAA;UAAKoL,KAAK,EAAE;YAAEiR,YAAY,EAAE,KAAK;YAAExP,OAAO,EAAE;UAAO,CAAE;UAAA2P,QAAA,EACnD,CAAApb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEgc,aAAa,kBAC9Bpd,OAAA,CAACT,UAAU;YACV8d,OAAO,EAAEA,CAAA,KAAM;cACd;cACA;YAAA,CACC;YACFpB,EAAE,EAAE;cACH/Q,QAAQ,EAAE,OAAO;cACjBoS,SAAS,EAAE,iCAAiC;cAC5CjW,IAAI,EAAE,MAAM;cACZsE,KAAK,EAAE,MAAM;cACbmR,MAAM,EAAE,OAAO;cACfS,UAAU,EAAE,iBAAiB;cAC7BC,MAAM,EAAE,gBAAgB;cACxB3F,MAAM,EAAE,QAAQ;cAChBzE,YAAY,EAAE,MAAM;cACpBmJ,OAAO,EAAE;YACV,CAAE;YAAAC,QAAA,eAEFxc,OAAA,CAACJ,SAAS;cAACqc,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAE3N,KAAK,EAAE;cAAO;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACN5O,OAAA,CAACF,gBAAgB;UAEpB4d,GAAG,EAAElT,YAAa;UAClBY,KAAK,EAAE;YAAEuS,SAAS,EAAEjN,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG;UAAQ,CAAE;UAC3EvK,OAAO,EAAE;YACR4W,eAAe,EAAE,CAACtT,cAAc;YAChCuT,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAA1B,QAAA,eAECxc,OAAA;YAAKoL,KAAK,EAAE;cACXuS,SAAS,EAAEjN,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;cAC/D2L,QAAQ,EAAExM,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;cACvEqC,KAAK,EAAElD,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGnK,SAAS;cAC7D0V,MAAM,EAAEpM,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGtJ;YAClC,CAAE;YAAAoV,QAAA,eACDxc,OAAA,CAACX,GAAG;cAAC+L,KAAK,EAAE;gBACXmR,OAAO,EAAE7L,cAAc,CAAC,CAAC,GAAG,GAAG,GAC7Ba,WAAW,CAAC,CAAC,GAAG,GAAG,GAAI,CAAAlQ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8c,OAAO,KAAI,MAAO;gBAC7DlI,MAAM,EAAEvF,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGmD,aAAa;gBACjDD,KAAK,EAAElD,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAGnK,SAAS;gBAC7D0V,MAAM,EAAEpM,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGtJ;cAClC,CAAE;cAAAoV,QAAA,gBACDxc,OAAA,CAACX,GAAG;gBACHqe,GAAG,EAAEvZ,UAAW;gBAChB0I,OAAO,EAAC,MAAM;gBACduR,aAAa,EAAC,QAAQ;gBACtBC,QAAQ,EAAC,MAAM;gBACf9F,cAAc,EAAC,QAAQ;gBACvB0D,EAAE,EAAE;kBACHrI,KAAK,EAAErC,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;kBACtCgL,OAAO,EAAEhL,WAAW,CAAC,CAAC,GAAG,GAAG,GAAGnK;gBAChC,CAAE;gBAAAoV,QAAA,GAEDtb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE4M,GAAG,CAAEwQ,SAAc,IACpCA,SAAS,CAACvN,WAAW,CAACjD,GAAG,CAAC,CAACyQ,SAAc,EAAEC,QAAgB,kBAC1Dxe,OAAA,CAACX,GAAG;kBAEHof,SAAS,EAAC,KAAK;kBACfC,GAAG,EAAEH,SAAS,CAACtN,GAAI;kBACnB0N,GAAG,EAAEJ,SAAS,CAACK,OAAO,IAAI,OAAQ;kBAClC3C,EAAE,EAAE;oBACH0B,SAAS,EAAEW,SAAS,CAACO,cAAc,IAAIN,SAAS,CAACM,cAAc,IAAI,OAAO;oBAC1E7O,SAAS,EAAEsO,SAAS,CAACrO,SAAS,IAAI,QAAQ;oBAC1C6O,SAAS,EAAEP,SAAS,CAACQ,GAAG,IAAI,SAAS;oBACrC;oBACA9I,MAAM,EAAE,GAAGsI,SAAS,CAACzK,aAAa,IAAI,GAAG,IAAI;oBAC7CyJ,UAAU,EAAEgB,SAAS,CAAC5K,eAAe,IAAI,SAAS;oBAClDmJ,MAAM,EAAE;kBACT,CAAE;kBACFO,OAAO,EAAEA,CAAA,KAAM;oBACd,IAAIiB,SAAS,CAACU,SAAS,EAAE;sBACxB,MAAM9K,SAAS,GAAGoK,SAAS,CAACU,SAAS;sBACrC9X,MAAM,CAACqN,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;oBACxD;kBACD,CAAE;kBACF9I,KAAK,EAAE;oBAAE6P,MAAM,EAAEqD,SAAS,CAACU,SAAS,GAAG,SAAS,GAAG;kBAAU;gBAAE,GAnB1D,GAAGV,SAAS,CAACnQ,EAAE,IAAIqQ,QAAQ,EAAE;kBAAA/P,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBlC,CACD,CACF,CAAC,EAEA3N,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAE6M,GAAG,CACxB,CAACmR,SAAc,EAAErC,KAAU;kBAAA,IAAAsC,qBAAA,EAAAC,sBAAA;kBAAA,OAC1BF,SAAS,CAAC7N,IAAI,iBACbpR,OAAA,CAACL,UAAU;oBACVgJ,SAAS,EAAC,eAAe;oBACG;oBAC5BsT,EAAE,EAAE;sBACHjM,SAAS,EAAE,EAAAkP,qBAAA,GAAAD,SAAS,CAACvP,cAAc,cAAAwP,qBAAA,uBAAxBA,qBAAA,CAA0BE,UAAU,KAAI5P,SAAS,CAACQ,SAAS;sBACtEF,KAAK,EAAE,EAAAqP,sBAAA,GAAAF,SAAS,CAACvP,cAAc,cAAAyP,sBAAA,uBAAxBA,sBAAA,CAA0BpP,SAAS,KAAIP,SAAS,CAACM,KAAK;sBAC7DuP,UAAU,EAAE,UAAU;sBACtBC,SAAS,EAAE,YAAY;sBACvB/C,OAAO,EAAE;oBACV,CAAE;oBACFgD,uBAAuB,EAAErP,iBAAiB,CAAC+O,SAAS,CAAC7N,IAAI,CAAE,CAAC;kBAAA,GARvD6N,SAAS,CAAC9Q,EAAE,IAAIyO,KAAK;oBAAAnO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAS1B,CACD;gBAAA,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAEL4Q,MAAM,CAACC,IAAI,CAACzM,cAAc,CAAC,CAAClF,GAAG,CAAEmF,WAAW;gBAAA,IAAAyM,qBAAA,EAAAC,sBAAA;gBAAA,oBAC5C3f,OAAA,CAACX,GAAG;kBACHqe,GAAG,EAAEtZ,kBAAmB;kBAExB6X,EAAE,EAAE;oBACHpP,OAAO,EAAE,MAAM;oBACf0L,cAAc,EAAErB,YAAY,EAAAwI,qBAAA,GAAC1M,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAAyM,qBAAA,uBAA9BA,qBAAA,CAAgCzP,SAAS,CAAC;oBACvEoO,QAAQ,EAAE,MAAM;oBAChBvB,MAAM,EAAEpM,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO;oBACtCrF,eAAe,GAAAsU,sBAAA,GAAE3M,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAA0M,sBAAA,uBAA9BA,sBAAA,CAAgChM,eAAe;oBAChE4I,OAAO,EAAE7L,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO;oBAC3CkD,KAAK,EAAElD,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;oBACzC0C,YAAY,EAAE1C,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGtJ;kBAC3C,CAAE;kBAAAoV,QAAA,EAEDxJ,cAAc,CAACC,WAAW,CAAC,CAACnF,GAAG,CAAC,CAACG,MAAW,EAAE2O,KAAa;oBAAA,IAAAgD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;oBAAA,oBAC3DlgB,OAAA,CAACV,MAAM;sBAEN+d,OAAO,EAAEA,CAAA,KAAMtJ,kBAAkB,CAAC9F,MAAM,CAACkS,YAAY,CAAE;sBACvDpE,OAAO,EAAC,WAAW;sBACnBE,EAAE,EAAE;wBACHmE,WAAW,EAAE1P,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM;wBAC9CoM,MAAM,EAAEpM,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe;wBAClDrF,eAAe,EAAE,EAAAuU,qBAAA,GAAA3R,MAAM,CAACoS,gBAAgB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBU,qBAAqB,KAAI,SAAS;wBAC5ExQ,KAAK,EAAE,EAAA+P,sBAAA,GAAA5R,MAAM,CAACoS,gBAAgB,cAAAR,sBAAA,uBAAvBA,sBAAA,CAAyBU,eAAe,KAAI,MAAM;wBACzD/C,MAAM,EAAE,EAAAsC,sBAAA,GAAA7R,MAAM,CAACoS,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBU,iBAAiB,KAAI,aAAa;wBACnEnI,QAAQ,EAAE,EAAA0H,sBAAA,GAAA9R,MAAM,CAACoS,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBU,QAAQ,KAAI,MAAM;wBACrD7M,KAAK,EAAE,EAAAoM,sBAAA,GAAA/R,MAAM,CAACoS,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyBrO,KAAK,KAAI,MAAM;wBAC/C4K,OAAO,EAAE7L,cAAc,CAAC,CAAC,GAAG,kCAAkC,GAAG,SAAS;wBAC1EgQ,UAAU,EAAEhQ,cAAc,CAAC,CAAC,GAAG,0BAA0B,GAAG,QAAQ;wBACpEiQ,aAAa,EAAE,MAAM;wBACrBvN,YAAY,EAAE,EAAA6M,sBAAA,GAAAhS,MAAM,CAACoS,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBW,YAAY,KAAI,KAAK;wBAC5D3O,QAAQ,EAAEvB,cAAc,CAAC,CAAC,GAAG,aAAa,GAAGtJ,SAAS;wBACtDkW,SAAS,EAAE,iBAAiB;wBAAE;wBAC9B,SAAS,EAAE;0BACVjS,eAAe,EAAE,EAAA6U,sBAAA,GAAAjS,MAAM,CAACoS,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBI,qBAAqB,KAAI,SAAS;0BAAE;0BAC9EO,OAAO,EAAE,GAAG;0BAAE;0BACdvD,SAAS,EAAE,iBAAiB,CAAE;wBAC/B;sBACD,CAAE;sBAAAd,QAAA,EAEDvO,MAAM,CAAC6S;oBAAU,GAxBblE,KAAK;sBAAAnO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBH,CAAC;kBAAA,CACT;gBAAC,GAxCGqE,WAAW;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCZ,CAAC;cAAA,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC,GApIL,aAAatE,cAAc,EAAE;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqIZ,CAAC,EAElByM,cAAc,IAAIva,UAAU,GAAC,CAAC,IAAI+B,gBAAgB,KAAK,MAAM,iBAAI7C,OAAA,CAACX,GAAG;UAAAmd,QAAA,EAAEV,cAAc,CAAC;QAAC;UAAArN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAE,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACL,eAED5O,OAAA;MAAAwc,QAAA,EACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAA/N,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACP,CAAC;AAEL,CAAC;AAAC5M,EAAA,CAzzDI7B,cAAoC;EAAA,QA8CrCN,cAAc;AAAA;AAAAkhB,EAAA,GA9Cb5gB,cAAoC;AA2zD1C,eAAeA,cAAc;AAAC,IAAA4gB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}