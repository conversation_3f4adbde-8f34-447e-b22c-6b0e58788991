{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\tours\\\\stepPopup.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport useDrawerStore from '../../store/drawerStore';\nimport { Select, MenuItem, Typography } from \"@mui/material\";\nimport { warning } from \"../../assets/icons/icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StepCreationPopup = ({\n  isOpen,\n  onClose,\n  onCreateStep,\n  stepData,\n  setStepData,\n  isEditStepName,\n  stepName,\n  setStepName,\n  editType,\n  setEditType,\n  editDescription,\n  setDescription,\n  setTemplateSelectionPopup\n}) => {\n  _s();\n  const {\n    setSelectedTemplate,\n    setBannerPopup,\n    setSelectedTemplateTour,\n    selectedTemplateTour,\n    selectedTemplate,\n    steps,\n    setSelectedStepTypeHotspot,\n    selectedStepTypeHotspot,\n    setBposition\n  } = useDrawerStore(state => state);\n  if (!isOpen) return null;\n  const handleStepTypeChange = e => {\n    const selectedType = e.target.value;\n    setStepData(prevData => ({\n      ...prevData,\n      type: selectedType\n    }));\n    setEditType(selectedType); // update editType state for future reference\n  };\n  const handleCreate = () => {\n    const updatedStepData = {\n      ...stepData,\n      type: (stepData === null || stepData === void 0 ? void 0 : stepData.type) || \"Announcement\"\n    };\n\n    // If creating a NEW Banner step in a Tour, set default position to Cover Top\n    if (updatedStepData.type === \"Banner\" && selectedTemplate === \"Tour\") {\n      // Only set default position for new banners\n      setBposition(\"Cover Top\");\n    }\n    onCreateStep(updatedStepData);\n    onClose();\n    setStepData(\"\");\n  };\n  let isDuplicateStep = false;\n  if (isEditStepName) {\n    const originalStepName = stepData.stepNumber;\n    isDuplicateStep = steps.some(step => step.name === stepName && step.name !== originalStepName);\n  } else {\n    isDuplicateStep = steps.some(step => step.name === stepName);\n  }\n  const stepNameTrimmed = stepName ? stepName.trim() : '';\n  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\\s+/g, '') : editDescription ? editDescription.replace(/\\s+/g, '') : '';\n  const isCreateButtonDisabled = !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: '195px',\n      backgroundColor: '#fff',\n      borderRadius: '6px',\n      boxShadow: '0px 4px 14px 0px #00000026',\n      border: '1px solid #e0e0e0',\n      zIndex: 1000,\n      height: 'auto',\n      overflow: 'auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '10px 12px'\n        // width: '171px',\n        // maxHeight: '265px',    \n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: \"Step Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`,\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: stepName,\n            onChange: e => {\n              const newStepName = e.target.value;\n              //setStepData({ ...stepData, stepNumber: newStepName });\n              setStepName(newStepName);\n            },\n            onFocus: e => e.target.style.border = '1px solid #a8a8a8',\n            onBlur: e => e.target.style.border = '1px solid #a8a8a8',\n            style: {\n              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 9\n        }, this), isDuplicateStep && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 5\n          }, this), \"Step name should be unique\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 3\n        }, this), stepName && stepNameTrimmed.length < 3 && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 3\n          }, this), \"Guide name must be at least 3 characters.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 3\n        }, this), stepName && stepNameTrimmed.length > 20 && /*#__PURE__*/_jsxDEV(Typography, {\n          style: {\n            fontSize: \"12px\",\n            color: \"#e9a971\",\n            textAlign: \"left\",\n            top: \"100%\",\n            left: 0,\n            marginBottom: \"5px\",\n            display: \"flex\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              display: \"flex\",\n              fontSize: \"12px\",\n              alignItems: \"center\",\n              marginRight: \"4px\"\n            },\n            dangerouslySetInnerHTML: {\n              __html: warning\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 3\n          }, this), \"Step name should not exceed 20 characters.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: \"Step Type:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            value: stepData.type || editType || (selectedStepTypeHotspot === true ? \"Hotspot\" : \"Announcement\"),\n            disabled: isEditStepName,\n            onOpen: e => {\n              setTemplateSelectionPopup(true);\n            },\n            onChange: e => {\n              setStepData({\n                ...stepData,\n                type: e.target.value\n              });\n              setEditType(e.target.value);\n              // If Banner is selected in a Tour, ensure position is set to Cover Top\n              if (e.target.value === \"Banner\" && selectedTemplate === \"Tour\") {\n                setBposition(\"Cover Top\");\n              }\n            },\n            onClose: e => {\n              setTemplateSelectionPopup(false);\n            },\n            displayEmpty: true,\n            MenuProps: {\n              sx: {\n                zIndex: 9999999\n              },\n              PopoverClasses: {\n                root: 'qadpt-turstp'\n              }\n            },\n            sx: {\n              width: \"171px\",\n              padding: \"6px 10px\",\n              borderRadius: \"6px\",\n              fontSize: \"14px\",\n              height: \"30px\",\n              color: \"#000\",\n              background: \"#fff\",\n              outline: \"none\",\n              textAlign: \"left\",\n              minWidth: \"100%\",\n              \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                borderColor: \"#a8a8a8\"\n              },\n              // Prevents color change on hover\n              \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                borderColor: \"#a8a8a8\"\n              },\n              // Prevents focus color change\n              \"& .MuiSelect-select\": {\n                paddingLeft: \"0 !important\"\n              }\n            },\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Announcement\",\n              children: \"Announcement\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Banner\",\n              children: \"Banner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Tooltip\",\n              children: \"Tooltip\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"Hotspot\",\n              children: \"Hotspot\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 18\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'start',\n            marginBottom: '4px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              color: '#616365',\n              lineHeight: '18px'\n            },\n            children: \"Description:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 5\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 3\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n            placeholder: !stepData.description && !editDescription ? \"write description\" : \"\",\n            value: stepData.description || editDescription || \"\",\n            onChange: e => {\n              setStepData({\n                ...stepData,\n                description: e.target.value\n              });\n              setDescription(e.target.value);\n            },\n            style: {\n              width: \"-webkit-fill-available\",\n              padding: \"6px 10px\",\n              borderRadius: \"6px\",\n              background: \"#fff\",\n              outline: \"none\",\n              textAlign: \"left\",\n              minHeight: \"60px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 5\n          }, this), descriptionTrimmed.length > 50 && /*#__PURE__*/_jsxDEV(Typography, {\n            style: {\n              fontSize: \"12px\",\n              color: \"#e9a971\",\n              textAlign: \"left\",\n              top: \"100%\",\n              left: 0,\n              marginBottom: \"5px\",\n              display: \"flex\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                display: \"flex\",\n                fontSize: \"12px\",\n                alignItems: \"center\",\n                marginRight: \"4px\"\n              },\n              dangerouslySetInnerHTML: {\n                __html: warning\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 9\n            }, this), \"Description must be a maximum of 50 characters.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 3\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 242,\n        columnNumber: 1\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: \"0 10px 10px 10px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleCreate,\n        disabled: isCreateButtonDisabled,\n        style: {\n          width: '100%',\n          padding: '7px 10px',\n          backgroundColor: \"var(--primarycolor)\",\n          // Change color when disabled\n          opacity: isCreateButtonDisabled ? \"0.5\" : \"1\",\n          color: '#fff',\n          border: 'none',\n          borderRadius: '7px',\n          cursor: 'pointer',\n          display: \"block\"\n        },\n        children: isEditStepName ? \"Update\" : \"Create\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 298,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 297,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(StepCreationPopup, \"Xk2Gz6+Dt9ncW9/2Swd+xNGyqGs=\", false, function () {\n  return [useDrawerStore];\n});\n_c = StepCreationPopup;\nexport default StepCreationPopup;\nvar _c;\n$RefreshReg$(_c, \"StepCreationPopup\");", "map": {"version": 3, "names": ["React", "useDrawerStore", "Select", "MenuItem", "Typography", "warning", "jsxDEV", "_jsxDEV", "StepCreationPopup", "isOpen", "onClose", "onCreateStep", "stepData", "setStepData", "isEditStepName", "<PERSON><PERSON><PERSON>", "setStepName", "editType", "setEditType", "editDescription", "setDescription", "setTemplateSelectionPopup", "_s", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "selectedTemplateTour", "selectedTemplate", "steps", "setSelectedStepTypeHotspot", "selectedStepTypeHotspot", "setBposition", "state", "handleStepTypeChange", "e", "selectedType", "target", "value", "prevData", "type", "handleCreate", "updatedStepData", "isDuplicateStep", "originalStepName", "<PERSON><PERSON><PERSON><PERSON>", "some", "step", "name", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "trim", "descriptionTrimmed", "description", "replace", "isCreateButtonDisabled", "length", "style", "width", "backgroundColor", "borderRadius", "boxShadow", "border", "zIndex", "height", "overflow", "children", "padding", "textAlign", "marginBottom", "color", "lineHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onChange", "newStepName", "onFocus", "onBlur", "fontSize", "top", "left", "display", "alignItems", "marginRight", "dangerouslySetInnerHTML", "__html", "disabled", "onOpen", "displayEmpty", "MenuProps", "sx", "PopoverClasses", "root", "background", "outline", "min<PERSON><PERSON><PERSON>", "borderColor", "paddingLeft", "placeholder", "minHeight", "onClick", "opacity", "cursor", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/tours/stepPopup.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport useDrawerS<PERSON>, { DrawerState } from '../../store/drawerStore';\r\nimport { Select, MenuItem, Typography } from \"@mui/material\";\r\nimport { ForkLeft } from '@mui/icons-material';\r\nimport {warning} from \"../../assets/icons/icons\";\r\n\r\nconst StepCreationPopup: React.FC<{ isOpen: boolean; onClose: () => void; onCreateStep:any,stepData:any,setStepData:any,isEditStepName:any,stepName:any,setStepName:any,editType:any,setEditType:any,editDescription:any,setDescription:any,setTemplateSelectionPopup:any}> = ({ isOpen, onClose,onCreateStep,stepData,setStepData,isEditStepName,stepName,setStepName,editType,setEditType,editDescription,setDescription,setTemplateSelectionPopup}) => {    const {\r\n        setSelectedTemplate,\r\n        setBannerPopup,\r\n        setSelectedTemplateTour,\r\n  selectedTemplateTour,\r\n  selectedTemplate,\r\n  steps,\r\n  setSelectedStepTypeHotspot,\r\n  selectedStepTypeHotspot,\r\n  setBposition,\r\n    } = useDrawerStore((state: DrawerState) => state);\r\n\r\n  if (!isOpen) return null;\r\n  const handleStepTypeChange = (e: React.ChangeEvent<{ value: unknown }>) => {\r\n    const selectedType = e.target.value;\r\n    setStepData((prevData: any) => ({\r\n      ...prevData,\r\n      type: selectedType,\r\n    }));\r\n    setEditType(selectedType); // update editType state for future reference\r\n  };\r\n  const handleCreate = () => {\r\n    const updatedStepData =\r\n    {\r\n      ...stepData,\r\n      type: stepData?.type||\"Announcement\",\r\n      \r\n      }\r\n\r\n    // If creating a NEW Banner step in a Tour, set default position to Cover Top\r\n    if (updatedStepData.type === \"Banner\" && selectedTemplate === \"Tour\") {\r\n      // Only set default position for new banners\r\n      setBposition(\"Cover Top\");\r\n    }\r\n\r\n    onCreateStep(updatedStepData);\r\n      onClose();\r\n      setStepData(\"\");\r\n  };\r\n  let isDuplicateStep = false;\r\n\r\n\r\n  if (isEditStepName) {\r\n    \r\n    const originalStepName = stepData.stepNumber;\r\n    \r\n    isDuplicateStep = steps.some(step => \r\n      step.name === stepName && step.name !== originalStepName\r\n    );\r\n  } else {\r\n    isDuplicateStep = steps.some(step => step.name === stepName);\r\n    }\r\n\r\n  const stepNameTrimmed = stepName ? stepName.trim() : '';\r\n  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\\s+/g, '') : editDescription ? editDescription.replace(/\\s+/g, '') : '';\r\n  const isCreateButtonDisabled =\r\n    !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;\r\n  return (\r\n    <div style={{\r\n      width: '195px',\r\n      backgroundColor: '#fff',\r\n      borderRadius: '6px',\r\n      boxShadow: '0px 4px 14px 0px #00000026',\r\n      border: '1px solid #e0e0e0',\r\n      zIndex: 1000,\r\n      height: 'auto',\r\n      overflow:'auto'\r\n    }}>\r\n      <div style={{\r\n              padding: '10px 12px',\r\n        // width: '171px',\r\n        // maxHeight: '265px',    \r\n      }}>\r\n        <div>\r\n        <div style={{\r\n          textAlign: 'start',\r\n          marginBottom:'4px'\r\n        }}>\r\n          <span style={{\r\n            color: '#616365',\r\n            lineHeight: '18px'  \r\n          }}>Step Name</span>\r\n        </div>\r\n\r\n        <div className={`step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`}>\r\n          <input\r\n            type=\"text\"\r\n            value={stepName}\r\n            onChange={(e) => {\r\n              const newStepName = e.target.value;\r\n              //setStepData({ ...stepData, stepNumber: newStepName });\r\n              setStepName(newStepName);\r\n            }}\r\n            onFocus={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            onBlur={(e) => e.target.style.border = '1px solid #a8a8a8'}\r\n            style={{\r\n              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'\r\n            }}\r\n          />\r\n        </div>\r\n\r\n{/* Error message for duplicate step name */}\r\n{isDuplicateStep && (\r\n  <Typography\r\n    style={{\r\n      fontSize: \"12px\",\r\n      color: \"#e9a971\",\r\n      textAlign: \"left\",\r\n      top: \"100%\",\r\n      left: 0,\r\n      marginBottom: \"5px\",\r\n      display: \"flex\",\r\n    }}\r\n  >\r\n    <span\r\n      style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n      dangerouslySetInnerHTML={{ __html: warning }}\r\n    />\r\n    Step name should be unique\r\n  </Typography>\r\n)}\r\n\r\n        {stepName && stepNameTrimmed.length < 3 && (\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n            Guide name must be at least 3 characters.\r\n            </Typography>\r\n          )}  \r\n          {stepName && stepNameTrimmed.length > 20 && (\r\n\r\n  <Typography\r\n  style={{\r\n    fontSize: \"12px\",\r\n    color: \"#e9a971\",\r\n    textAlign: \"left\",\r\n    top: \"100%\",\r\n    left: 0,\r\n    marginBottom: \"5px\",\r\n    display: \"flex\",\r\n  }}\r\n>\r\n  <span\r\n    style={{ display: \"flex\", fontSize: \"12px\", alignItems: \"center\", marginRight: \"4px\" }}\r\n    dangerouslySetInnerHTML={{ __html: warning }}\r\n  />\r\n    Step name should not exceed 20 characters.\r\n  </Typography>\r\n)}\r\n      </div>\r\n        {selectedTemplate === \"Tour\" && (\r\n\r\n\r\n          <div style={{\r\n            marginBottom: '12px',\r\n          }}>\r\n            <div style={{\r\n              textAlign: 'start',\r\n              marginBottom: '4px'\r\n            }}>\r\n              <span style={{\r\n                color: '#616365',\r\n                lineHeight: '18px'\r\n              }}>Step Type:</span>\r\n            </div>\r\n       \r\n            \r\n            <div>\r\n              <Select\r\n               value={stepData.type || editType || (selectedStepTypeHotspot === true ? \"Hotspot\" : \"Announcement\")}\r\n                disabled={isEditStepName}\r\n                onOpen={(e) => {\r\n                  setTemplateSelectionPopup(true);\r\n                }}\r\n                onChange={(e) => {\r\n                  setStepData({ ...stepData, type: e.target.value });\r\n                  setEditType(e.target.value);\r\n                  // If Banner is selected in a Tour, ensure position is set to Cover Top\r\n                  if (e.target.value === \"Banner\" && selectedTemplate === \"Tour\") {\r\n                    setBposition(\"Cover Top\");\r\n                  }\r\n                }\r\n                }\r\n                onClose={(e) => {\r\n                  setTemplateSelectionPopup(false);\r\n                }}\r\n                displayEmpty\r\n                MenuProps={{\r\n                  sx: {\r\n                    zIndex: 9999999, \r\n                  },\r\n                  PopoverClasses: {\r\n                    root: 'qadpt-turstp', \r\n                  },\r\n                }}\r\n                sx={{\r\n                  width: \"171px\",\r\n                  padding: \"6px 10px\",\r\n                  borderRadius: \"6px\",\r\n                  fontSize: \"14px\",\r\n                  height: \"30px\",\r\n                  color: \"#000\",\r\n                  background: \"#fff\",\r\n                  outline: \"none\",\r\n                  textAlign: \"left\",\r\n                  minWidth: \"100%\",\r\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents focus color change\r\n                  \"& .MuiSelect-select\": { paddingLeft: \"0 !important\" },\r\n                 \r\n                }}\r\n              >\r\n                <MenuItem value=\"Announcement\">Announcement</MenuItem>\r\n                <MenuItem value=\"Banner\">Banner</MenuItem>\r\n                <MenuItem value=\"Tooltip\">Tooltip</MenuItem>\r\n                 <MenuItem value=\"Hotspot\">Hotspot</MenuItem>\r\n              </Select>\r\n            </div>\r\n          \r\n\r\n          </div>\r\n        )}\r\n\r\n<div style={{ marginBottom: '12px' }}>\r\n  <div style={{ textAlign: 'start', marginBottom: '4px' }}>\r\n    <span style={{ color: '#616365', lineHeight: '18px' }}>Description:</span>\r\n  </div>\r\n\r\n  <div className={`step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`}>\r\n    <textarea\r\n      placeholder={\r\n        (!stepData.description && !editDescription) ? \"write description\" : \"\"\r\n      }\r\n      value={stepData.description || editDescription || \"\"}\r\n      onChange={(e) => {\r\n        setStepData({ ...stepData, description: e.target.value });\r\n        setDescription(e.target.value);\r\n      }}\r\n      style={{\r\n        width: \"-webkit-fill-available\",\r\n        padding: \"6px 10px\",\r\n        borderRadius: \"6px\",\r\n        background: \"#fff\",\r\n        outline: \"none\",\r\n        textAlign: \"left\",\r\n        minHeight: \"60px\",\r\n      }}\r\n    />\r\n\r\n    {descriptionTrimmed.length > 50 && (\r\n      <Typography\r\n        style={{\r\n          fontSize: \"12px\",\r\n          color: \"#e9a971\",\r\n          textAlign: \"left\",\r\n          top: \"100%\",\r\n          left: 0,\r\n          marginBottom: \"5px\",\r\n          display: \"flex\",\r\n        }}\r\n      >\r\n        <span\r\n          style={{\r\n            display: \"flex\",\r\n            fontSize: \"12px\",\r\n            alignItems: \"center\",\r\n            marginRight: \"4px\"\r\n          }}\r\n          dangerouslySetInnerHTML={{ __html: warning }}\r\n        />\r\n        Description must be a maximum of 50 characters.\r\n      </Typography>\r\n    )}\r\n  </div>\r\n</div>\r\n\r\n       \r\n      </div>\r\n      <div style={{padding: \"0 10px 10px 10px\"}}>\r\n      <button\r\n          onClick={handleCreate}\r\n          disabled={isCreateButtonDisabled}\r\n          style={{\r\n            width: '100%',\r\n            padding: '7px 10px',\r\n            backgroundColor: \"var(--primarycolor)\", // Change color when disabled\r\n            opacity:isCreateButtonDisabled?\"0.5\":\"1\",\r\n            color: '#fff',\r\n            border: 'none',\r\n            borderRadius: '7px',\r\n            cursor: 'pointer',\r\n            display:\"block\",\r\n          }}\r\n        >\r\n          {isEditStepName ? \"Update\" : \"Create\"}\r\n        </button>\r\n        </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default StepCreationPopup;\r\n\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAA+B,OAAO;AAClD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE,SAASC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,eAAe;AAE5D,SAAQC,OAAO,QAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,iBAAqQ,GAAGA,CAAC;EAAEC,MAAM;EAAEC,OAAO;EAACC,YAAY;EAACC,QAAQ;EAACC,WAAW;EAACC,cAAc;EAACC,QAAQ;EAACC,WAAW;EAACC,QAAQ;EAACC,WAAW;EAACC,eAAe;EAACC,cAAc;EAACC;AAAyB,CAAC,KAAK;EAAAC,EAAA;EAAK,MAAM;IAC7bC,mBAAmB;IACnBC,cAAc;IACdC,uBAAuB;IAC7BC,oBAAoB;IACpBC,gBAAgB;IAChBC,KAAK;IACLC,0BAA0B;IAC1BC,uBAAuB;IACvBC;EACE,CAAC,GAAG9B,cAAc,CAAE+B,KAAkB,IAAKA,KAAK,CAAC;EAEnD,IAAI,CAACvB,MAAM,EAAE,OAAO,IAAI;EACxB,MAAMwB,oBAAoB,GAAIC,CAAwC,IAAK;IACzE,MAAMC,YAAY,GAAGD,CAAC,CAACE,MAAM,CAACC,KAAK;IACnCxB,WAAW,CAAEyB,QAAa,KAAM;MAC9B,GAAGA,QAAQ;MACXC,IAAI,EAAEJ;IACR,CAAC,CAAC,CAAC;IACHjB,WAAW,CAACiB,YAAY,CAAC,CAAC,CAAC;EAC7B,CAAC;EACD,MAAMK,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,eAAe,GACrB;MACE,GAAG7B,QAAQ;MACX2B,IAAI,EAAE,CAAA3B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,IAAI,KAAE;IAEtB,CAAC;;IAEH;IACA,IAAIE,eAAe,CAACF,IAAI,KAAK,QAAQ,IAAIZ,gBAAgB,KAAK,MAAM,EAAE;MACpE;MACAI,YAAY,CAAC,WAAW,CAAC;IAC3B;IAEApB,YAAY,CAAC8B,eAAe,CAAC;IAC3B/B,OAAO,CAAC,CAAC;IACTG,WAAW,CAAC,EAAE,CAAC;EACnB,CAAC;EACD,IAAI6B,eAAe,GAAG,KAAK;EAG3B,IAAI5B,cAAc,EAAE;IAElB,MAAM6B,gBAAgB,GAAG/B,QAAQ,CAACgC,UAAU;IAE5CF,eAAe,GAAGd,KAAK,CAACiB,IAAI,CAACC,IAAI,IAC/BA,IAAI,CAACC,IAAI,KAAKhC,QAAQ,IAAI+B,IAAI,CAACC,IAAI,KAAKJ,gBAC1C,CAAC;EACH,CAAC,MAAM;IACLD,eAAe,GAAGd,KAAK,CAACiB,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,KAAKhC,QAAQ,CAAC;EAC5D;EAEF,MAAMiC,eAAe,GAAGjC,QAAQ,GAAGA,QAAQ,CAACkC,IAAI,CAAC,CAAC,GAAG,EAAE;EACvD,MAAMC,kBAAkB,GAAGtC,QAAQ,CAACuC,WAAW,GAAGvC,QAAQ,CAACuC,WAAW,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAGjC,eAAe,GAAGA,eAAe,CAACiC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,EAAE;EACvJ,MAAMC,sBAAsB,GAC1B,CAACtC,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,CAAC,IAAIN,eAAe,CAACM,MAAM,GAAG,EAAE,IAAIZ,eAAe,IAAIQ,kBAAkB,CAACI,MAAM,GAAG,EAAE,IAAIN,eAAe,CAACM,MAAM,GAAG,EAAE;EAC5J,oBACE/C,OAAA;IAAKgD,KAAK,EAAE;MACVC,KAAK,EAAE,OAAO;MACdC,eAAe,EAAE,MAAM;MACvBC,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE,4BAA4B;MACvCC,MAAM,EAAE,mBAAmB;MAC3BC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE,MAAM;MACdC,QAAQ,EAAC;IACX,CAAE;IAAAC,QAAA,gBACAzD,OAAA;MAAKgD,KAAK,EAAE;QACJU,OAAO,EAAE;QACf;QACA;MACF,CAAE;MAAAD,QAAA,gBACAzD,OAAA;QAAAyD,QAAA,gBACAzD,OAAA;UAAKgD,KAAK,EAAE;YACVW,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAC;UACf,CAAE;UAAAH,QAAA,eACAzD,OAAA;YAAMgD,KAAK,EAAE;cACXa,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,EAAC;UAAS;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAENlE,OAAA;UAAKmE,SAAS,EAAE,cAAchC,eAAe,GAAG,aAAa,GAAG,EAAE,EAAG;UAAAsB,QAAA,eACnEzD,OAAA;YACEgC,IAAI,EAAC,MAAM;YACXF,KAAK,EAAEtB,QAAS;YAChB4D,QAAQ,EAAGzC,CAAC,IAAK;cACf,MAAM0C,WAAW,GAAG1C,CAAC,CAACE,MAAM,CAACC,KAAK;cAClC;cACArB,WAAW,CAAC4D,WAAW,CAAC;YAC1B,CAAE;YACFC,OAAO,EAAG3C,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,GAAG,mBAAoB;YAC5DkB,MAAM,EAAG5C,CAAC,IAAKA,CAAC,CAACE,MAAM,CAACmB,KAAK,CAACK,MAAM,GAAG,mBAAoB;YAC3DL,KAAK,EAAE;cACLK,MAAM,EAAElB,eAAe,IAAIM,eAAe,CAACM,MAAM,GAAG,EAAE,GAAG,eAAe,GAAG;YAC7E;UAAE;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAGb/B,eAAe,iBACdnC,OAAA,CAACH,UAAU;UACTmD,KAAK,EAAE;YACLwB,QAAQ,EAAE,MAAM;YAChBX,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBc,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPd,YAAY,EAAE,KAAK;YACnBe,OAAO,EAAE;UACX,CAAE;UAAAlB,QAAA,gBAEFzD,OAAA;YACEgD,KAAK,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAEjF;YAAQ;UAAE;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,8BAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EAEQ1D,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,CAAC,iBAC7C/C,OAAA,CAACH,UAAU;UACXmD,KAAK,EAAE;YACLwB,QAAQ,EAAE,MAAM;YAChBX,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBc,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPd,YAAY,EAAE,KAAK;YACnBe,OAAO,EAAE;UACX,CAAE;UAAAlB,QAAA,gBAEFzD,OAAA;YACEgD,KAAK,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAEjF;YAAQ;UAAE;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,6CAEQ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb,EACA1D,QAAQ,IAAIiC,eAAe,CAACM,MAAM,GAAG,EAAE,iBAEhD/C,OAAA,CAACH,UAAU;UACXmD,KAAK,EAAE;YACLwB,QAAQ,EAAE,MAAM;YAChBX,KAAK,EAAE,SAAS;YAChBF,SAAS,EAAE,MAAM;YACjBc,GAAG,EAAE,MAAM;YACXC,IAAI,EAAE,CAAC;YACPd,YAAY,EAAE,KAAK;YACnBe,OAAO,EAAE;UACX,CAAE;UAAAlB,QAAA,gBAEFzD,OAAA;YACEgD,KAAK,EAAE;cAAE2B,OAAO,EAAE,MAAM;cAAEH,QAAQ,EAAE,MAAM;cAAEI,UAAU,EAAE,QAAQ;cAAEC,WAAW,EAAE;YAAM,CAAE;YACvFC,uBAAuB,EAAE;cAAEC,MAAM,EAAEjF;YAAQ;UAAE;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,8CAEF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC,EACH9C,gBAAgB,KAAK,MAAM,iBAG1BpB,OAAA;QAAKgD,KAAK,EAAE;UACVY,YAAY,EAAE;QAChB,CAAE;QAAAH,QAAA,gBACAzD,OAAA;UAAKgD,KAAK,EAAE;YACVW,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAE;UAChB,CAAE;UAAAH,QAAA,eACAzD,OAAA;YAAMgD,KAAK,EAAE;cACXa,KAAK,EAAE,SAAS;cAChBC,UAAU,EAAE;YACd,CAAE;YAAAL,QAAA,EAAC;UAAU;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC,eAGNlE,OAAA;UAAAyD,QAAA,eACEzD,OAAA,CAACL,MAAM;YACNmC,KAAK,EAAEzB,QAAQ,CAAC2B,IAAI,IAAItB,QAAQ,KAAKa,uBAAuB,KAAK,IAAI,GAAG,SAAS,GAAG,cAAc,CAAE;YACnGyD,QAAQ,EAAEzE,cAAe;YACzB0E,MAAM,EAAGtD,CAAC,IAAK;cACbb,yBAAyB,CAAC,IAAI,CAAC;YACjC,CAAE;YACFsD,QAAQ,EAAGzC,CAAC,IAAK;cACfrB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAE2B,IAAI,EAAEL,CAAC,CAACE,MAAM,CAACC;cAAM,CAAC,CAAC;cAClDnB,WAAW,CAACgB,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;cAC3B;cACA,IAAIH,CAAC,CAACE,MAAM,CAACC,KAAK,KAAK,QAAQ,IAAIV,gBAAgB,KAAK,MAAM,EAAE;gBAC9DI,YAAY,CAAC,WAAW,CAAC;cAC3B;YACF,CACC;YACDrB,OAAO,EAAGwB,CAAC,IAAK;cACdb,yBAAyB,CAAC,KAAK,CAAC;YAClC,CAAE;YACFoE,YAAY;YACZC,SAAS,EAAE;cACTC,EAAE,EAAE;gBACF9B,MAAM,EAAE;cACV,CAAC;cACD+B,cAAc,EAAE;gBACdC,IAAI,EAAE;cACR;YACF,CAAE;YACFF,EAAE,EAAE;cACFnC,KAAK,EAAE,OAAO;cACdS,OAAO,EAAE,UAAU;cACnBP,YAAY,EAAE,KAAK;cACnBqB,QAAQ,EAAE,MAAM;cAChBjB,MAAM,EAAE,MAAM;cACdM,KAAK,EAAE,MAAM;cACb0B,UAAU,EAAE,MAAM;cAClBC,OAAO,EAAE,MAAM;cACf7B,SAAS,EAAE,MAAM;cACjB8B,QAAQ,EAAE,MAAM;cAChB,0CAA0C,EAAE;gBAAEC,WAAW,EAAE;cAAU,CAAC;cAAE;cACxE,gDAAgD,EAAE;gBAAEA,WAAW,EAAE;cAAU,CAAC;cAAE;cAC9E,qBAAqB,EAAE;gBAAEC,WAAW,EAAE;cAAe;YAEvD,CAAE;YAAAlC,QAAA,gBAEFzD,OAAA,CAACJ,QAAQ;cAACkC,KAAK,EAAC,cAAc;cAAA2B,QAAA,EAAC;YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eACtDlE,OAAA,CAACJ,QAAQ;cAACkC,KAAK,EAAC,QAAQ;cAAA2B,QAAA,EAAC;YAAM;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC1ClE,OAAA,CAACJ,QAAQ;cAACkC,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAC3ClE,OAAA,CAACJ,QAAQ;cAACkC,KAAK,EAAC,SAAS;cAAA2B,QAAA,EAAC;YAAO;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGH,CACN,eAETlE,OAAA;QAAKgD,KAAK,EAAE;UAAEY,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACnCzD,OAAA;UAAKgD,KAAK,EAAE;YAAEW,SAAS,EAAE,OAAO;YAAEC,YAAY,EAAE;UAAM,CAAE;UAAAH,QAAA,eACtDzD,OAAA;YAAMgD,KAAK,EAAE;cAAEa,KAAK,EAAE,SAAS;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAL,QAAA,EAAC;UAAY;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENlE,OAAA;UAAKmE,SAAS,EAAE,cAAcxB,kBAAkB,CAACI,MAAM,GAAG,EAAE,GAAG,aAAa,GAAG,EAAE,EAAG;UAAAU,QAAA,gBAClFzD,OAAA;YACE4F,WAAW,EACR,CAACvF,QAAQ,CAACuC,WAAW,IAAI,CAAChC,eAAe,GAAI,mBAAmB,GAAG,EACrE;YACDkB,KAAK,EAAEzB,QAAQ,CAACuC,WAAW,IAAIhC,eAAe,IAAI,EAAG;YACrDwD,QAAQ,EAAGzC,CAAC,IAAK;cACfrB,WAAW,CAAC;gBAAE,GAAGD,QAAQ;gBAAEuC,WAAW,EAAEjB,CAAC,CAACE,MAAM,CAACC;cAAM,CAAC,CAAC;cACzDjB,cAAc,CAACc,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;YAChC,CAAE;YACFkB,KAAK,EAAE;cACLC,KAAK,EAAE,wBAAwB;cAC/BS,OAAO,EAAE,UAAU;cACnBP,YAAY,EAAE,KAAK;cACnBoC,UAAU,EAAE,MAAM;cAClBC,OAAO,EAAE,MAAM;cACf7B,SAAS,EAAE,MAAM;cACjBkC,SAAS,EAAE;YACb;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAEDvB,kBAAkB,CAACI,MAAM,GAAG,EAAE,iBAC7B/C,OAAA,CAACH,UAAU;YACTmD,KAAK,EAAE;cACLwB,QAAQ,EAAE,MAAM;cAChBX,KAAK,EAAE,SAAS;cAChBF,SAAS,EAAE,MAAM;cACjBc,GAAG,EAAE,MAAM;cACXC,IAAI,EAAE,CAAC;cACPd,YAAY,EAAE,KAAK;cACnBe,OAAO,EAAE;YACX,CAAE;YAAAlB,QAAA,gBAEFzD,OAAA;cACEgD,KAAK,EAAE;gBACL2B,OAAO,EAAE,MAAM;gBACfH,QAAQ,EAAE,MAAM;gBAChBI,UAAU,EAAE,QAAQ;gBACpBC,WAAW,EAAE;cACf,CAAE;cACFC,uBAAuB,EAAE;gBAAEC,MAAM,EAAEjF;cAAQ;YAAE;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,mDAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGK,CAAC,eACNlE,OAAA;MAAKgD,KAAK,EAAE;QAACU,OAAO,EAAE;MAAkB,CAAE;MAAAD,QAAA,eAC1CzD,OAAA;QACI8F,OAAO,EAAE7D,YAAa;QACtB+C,QAAQ,EAAElC,sBAAuB;QACjCE,KAAK,EAAE;UACLC,KAAK,EAAE,MAAM;UACbS,OAAO,EAAE,UAAU;UACnBR,eAAe,EAAE,qBAAqB;UAAE;UACxC6C,OAAO,EAACjD,sBAAsB,GAAC,KAAK,GAAC,GAAG;UACxCe,KAAK,EAAE,MAAM;UACbR,MAAM,EAAE,MAAM;UACdF,YAAY,EAAE,KAAK;UACnB6C,MAAM,EAAE,SAAS;UACjBrB,OAAO,EAAC;QACV,CAAE;QAAAlB,QAAA,EAEDlD,cAAc,GAAG,QAAQ,GAAG;MAAQ;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnD,EAAA,CAvTId,iBAAqQ;EAAA,QAUnQP,cAAc;AAAA;AAAAuG,EAAA,GAVhBhG,iBAAqQ;AAyT3Q,eAAeA,iBAAiB;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}