{"ast": null, "code": "import React,{useEffect}from\"react\";import useDrawerStore from\"../../store/drawerStore\";import AnnouncementPopup from\"../GuidesPreview/AnnouncementPreview\";import HotspotPreview from\"../GuidesPreview/HotspotPreview\";import TooltiplastUserview from\"../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview\";import BannerStepPreview from\"./BannerStepPreview\";import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TourPreview=_ref=>{var _savedGuideData$Guide,_savedGuideData$Guide2,_savedGuideData$Guide17,_savedGuideData$Guide18,_savedGuideData$Guide19,_savedGuideData$Guide20,_savedGuideData$Guide21,_savedGuideData$Guide22,_savedGuideData$Guide23,_savedGuideData$Guide24,_savedGuideData$Guide25,_savedGuideData$Guide26,_savedGuideData$Guide27,_savedGuideData$Guide28,_savedGuideData$Guide29,_savedGuideData$Guide30,_savedGuideData$Guide31,_savedGuideData$Guide32,_savedGuideData$Guide33,_savedGuideData$Guide34,_savedGuideData$Guide35,_savedGuideData$Guide36,_savedGuideData$Guide37,_savedGuideData$Guide38,_savedGuideData$Guide39,_savedGuideData$Guide40,_savedGuideData$Guide41,_savedGuideData$Guide42,_savedGuideData$Guide43,_savedGuideData$Guide44,_savedGuideData$Guide45,_savedGuideData$Guide46,_savedGuideData$Guide47,_savedGuideData$Guide48,_savedGuideData$Guide49,_savedGuideData$Guide50,_savedGuideData$Guide51,_savedGuideData$Guide52,_savedGuideData$Guide53,_savedGuideData$Guide54,_savedGuideData$Guide55,_savedGuideData$Guide56,_savedGuideData$Guide57,_savedGuideData$Guide58,_savedGuideData$Guide59,_savedGuideData$Guide60,_savedGuideData$Guide61,_savedGuideData$Guide62,_savedGuideData$Guide63,_savedGuideData$Guide64,_savedGuideData$Guide65,_savedGuideData$Guide66,_savedGuideData$Guide67,_savedGuideData$Guide68,_savedGuideData$Guide69,_savedGuideData$Guide70,_savedGuideData$Guide71,_savedGuideData$Guide72,_savedGuideData$Guide73,_savedGuideData$Guide74,_savedGuideData$Guide75,_savedGuideData$Guide76,_savedGuideData$Guide77,_savedGuideData$Guide78,_savedGuideData$Guide79,_savedGuideData$Guide80,_savedGuideData$Guide81,_savedGuideData$Guide82,_savedGuideData$Guide83,_savedGuideData$Guide84,_savedGuideData$Guide85,_savedGuideData$Guide86,_savedGuideData$Guide87,_savedGuideData$Guide88,_savedGuideData$Guide89,_savedGuideData$Guide90,_savedGuideData$Guide91,_savedGuideData$Guide92,_savedGuideData$Guide93,_savedGuideData$Guide94,_savedGuideData$Guide95,_savedGuideData$Guide96,_savedGuideData$Guide97,_savedGuideData$Guide98,_savedGuideData$Guide99,_savedGuideData$Guide100,_savedGuideData$Guide101,_savedGuideData$Guide102,_savedGuideData$Guide103,_savedGuideData$Guide104,_savedGuideData$Guide105,_savedGuideData$Guide106,_savedGuideData$Guide107,_savedGuideData$Guide108,_savedGuideData$Guide109,_savedGuideData$Guide110,_savedGuideData$Guide111,_savedGuideData$Guide112;let{selectedTemplate,handlecloseBannerPopup,backgroundC,Bposition,bpadding,Bbordercolor,BborderSize,guideStep,anchorEl,onClose,onPrevious,onContinue,title,text,imageUrl,videoUrl,previousButtonLabel,continueButtonLabel,currentStep,totalSteps,onDontShowAgain,progress,textFieldProperties,imageProperties,customButton,modalProperties,canvasProperties,htmlSnippet,previousButtonStyles,continueButtonStyles,OverlayValue,savedGuideData}=_ref;const{setSelectedTemplate,setBannerPopup,setSelectedTemplateTour,setSteps,steps,setTooltipCount,tooltipCount,HotspotGuideDetails,announcementPreview,setAnnouncementPreview,bannerPreview,setBannerPreview,tooltipPreview,setTooltipPreview,hotspotPreview,setHotspotPreview,setCurrentStep,setOpenTooltip,ProgressColor,setProgressColor}=useDrawerStore(state=>state);const stepType=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide=savedGuideData.GuideStep)===null||_savedGuideData$Guide===void 0?void 0:(_savedGuideData$Guide2=_savedGuideData$Guide[currentStep-1])===null||_savedGuideData$Guide2===void 0?void 0:_savedGuideData$Guide2.StepType;// Note: AI data synchronization is now handled in the store's setCurrentStep function\n// to avoid infinite loops and ensure proper timing\nuseEffect(()=>{// Clean up any existing hotspot before setting new preview\nconst existingHotspot=document.getElementById(\"hotspotBlink\");if(existingHotspot){existingHotspot.style.display=\"none\";existingHotspot.remove();}// Reset all previews before setting the specific one\nsetAnnouncementPreview(false);setBannerPreview(false);setTooltipPreview(false);setHotspotPreview(false);// Set the correct preview based on stepType\nif(stepType===\"Announcement\"){setAnnouncementPreview(true);resetHeightofBanner(\"\",0,0,0);}else if(stepType===\"Banner\"){var _savedGuideData$Guide3,_savedGuideData$Guide4,_savedGuideData$Guide5,_savedGuideData$Guide6,_savedGuideData$Guide7,_savedGuideData$Guide8,_savedGuideData$Guide9,_savedGuideData$Guide10,_savedGuideData$Guide11;setBannerPreview(true);// Get the current Canvas position from the step\nconst currentPosition=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide3=savedGuideData.GuideStep)===null||_savedGuideData$Guide3===void 0?void 0:(_savedGuideData$Guide4=_savedGuideData$Guide3[currentStep-1])===null||_savedGuideData$Guide4===void 0?void 0:(_savedGuideData$Guide5=_savedGuideData$Guide4.Canvas)===null||_savedGuideData$Guide5===void 0?void 0:_savedGuideData$Guide5.Position;// Use the current position value to properly apply Push Down effect\nresetHeightofBanner(currentPosition,parseInt((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide6=savedGuideData.GuideStep)===null||_savedGuideData$Guide6===void 0?void 0:(_savedGuideData$Guide7=_savedGuideData$Guide6[currentStep-1])===null||_savedGuideData$Guide7===void 0?void 0:(_savedGuideData$Guide8=_savedGuideData$Guide7.Canvas)===null||_savedGuideData$Guide8===void 0?void 0:_savedGuideData$Guide8.Padding)||\"0\"),parseInt((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide9=savedGuideData.GuideStep)===null||_savedGuideData$Guide9===void 0?void 0:(_savedGuideData$Guide10=_savedGuideData$Guide9[currentStep-1])===null||_savedGuideData$Guide10===void 0?void 0:(_savedGuideData$Guide11=_savedGuideData$Guide10.Canvas)===null||_savedGuideData$Guide11===void 0?void 0:_savedGuideData$Guide11.BorderSize)||\"0\"),0);}else if(stepType===\"Tooltip\"){setTooltipPreview(true);resetHeightofBanner(\"\",0,0,0);}else if(stepType===\"Hotspot\"){var _savedGuideData$Guide12,_savedGuideData$Guide13,_savedGuideData$Guide14,_savedGuideData$Guide15;setHotspotPreview(true);setOpenTooltip(false);resetHeightofBanner(\"\",0,0,0);// Initialize tour hotspot metadata for proper functionality\ninitializeTourHotspotMetadata();// Ensure pulse animation is enabled for tour hotspots\n// useDrawerStore.setState({ pulseAnimationsH: true });\n// Debug logging for tour hotspot initialization\nconsole.log(\"Tour hotspot initialized:\",{stepType,currentStep,hotspotData:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide12=savedGuideData.GuideStep)===null||_savedGuideData$Guide12===void 0?void 0:(_savedGuideData$Guide13=_savedGuideData$Guide12[currentStep-1])===null||_savedGuideData$Guide13===void 0?void 0:_savedGuideData$Guide13.Hotspot,elementPath:savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide14=savedGuideData.GuideStep)===null||_savedGuideData$Guide14===void 0?void 0:(_savedGuideData$Guide15=_savedGuideData$Guide14[currentStep-1])===null||_savedGuideData$Guide15===void 0?void 0:_savedGuideData$Guide15.ElementPath});}},[stepType,currentStep]);// Dependencies ensure effect runs when stepType or currentStep changes\n// Initialize tour hotspot metadata for HotspotPreview compatibility\nconst initializeTourHotspotMetadata=()=>{var _savedGuideData$Guide16;const currentStepData=savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide16=savedGuideData.GuideStep)===null||_savedGuideData$Guide16===void 0?void 0:_savedGuideData$Guide16[currentStep-1];if(currentStepData&&currentStepData.StepType===\"Hotspot\"){var _Canvas,_Canvas2,_Canvas3,_Canvas4,_Canvas5,_Canvas6,_Canvas7,_Canvas8,_Canvas9,_Canvas10,_Design,_Design$GotoNext,_Design2,_Design2$GotoNext,_Design3,_Design3$GotoNext,_Design4,_Design4$GotoNext;const hotspotProps=currentStepData.Hotspot||{};// Create metadata structure for HotspotPreview\n// Extract containers from saved guide data\nconst containers=[];// Add RTE containers from TextFieldProperties\nif(currentStepData.TextFieldProperties&&currentStepData.TextFieldProperties.length>0){currentStepData.TextFieldProperties.forEach(textField=>{containers.push({id:textField.Id||crypto.randomUUID(),type:\"rte\",placeholder:\"Start typing here...\",rteBoxValue:textField.Text||\"\",style:{backgroundColor:\"transparent\"}});});}// Add button containers from ButtonSection\nif(currentStepData.ButtonSection&&currentStepData.ButtonSection.length>0){currentStepData.ButtonSection.forEach(section=>{var _section$CustomButton;containers.push({id:section.Id||crypto.randomUUID(),type:\"button\",buttons:((_section$CustomButton=section.CustomButtons)===null||_section$CustomButton===void 0?void 0:_section$CustomButton.map(button=>{var _button$ButtonAction,_button$ButtonAction2,_button$ButtonAction3,_button$ButtonPropert,_button$ButtonPropert2,_button$ButtonPropert3;return{id:button.ButtonId||crypto.randomUUID(),name:button.ButtonName||\"Button\",type:button.ButtonStyle||\"primary\",position:button.Alignment||\"center\",actions:{value:((_button$ButtonAction=button.ButtonAction)===null||_button$ButtonAction===void 0?void 0:_button$ButtonAction.Action)||\"close\",targetURL:((_button$ButtonAction2=button.ButtonAction)===null||_button$ButtonAction2===void 0?void 0:_button$ButtonAction2.TargetUrl)||\"\",tab:((_button$ButtonAction3=button.ButtonAction)===null||_button$ButtonAction3===void 0?void 0:_button$ButtonAction3.ActionValue)||\"same-tab\"},style:{backgroundColor:((_button$ButtonPropert=button.ButtonProperties)===null||_button$ButtonPropert===void 0?void 0:_button$ButtonPropert.ButtonBackgroundColor)||\"#5F9EA0\",color:((_button$ButtonPropert2=button.ButtonProperties)===null||_button$ButtonPropert2===void 0?void 0:_button$ButtonPropert2.ButtonTextColor)||\"#ffffff\",borderColor:((_button$ButtonPropert3=button.ButtonProperties)===null||_button$ButtonPropert3===void 0?void 0:_button$ButtonPropert3.ButtonBorderColor)||\"#5F9EA0\"}};}))||[],style:{backgroundColor:\"transparent\"}});});}// Add image containers from ImageProperties\nif(currentStepData.ImageProperties&&currentStepData.ImageProperties.length>0){currentStepData.ImageProperties.forEach(imageProperty=>{var _imageProperty$Custom;containers.push({id:imageProperty.Id||crypto.randomUUID(),type:\"image\",images:((_imageProperty$Custom=imageProperty.CustomImage)===null||_imageProperty$Custom===void 0?void 0:_imageProperty$Custom.map(image=>({url:image.Url||\"\",altText:image.AltText||\"\"})))||[],style:{backgroundColor:\"transparent\"}});});}const tourHotspotMetadata={containers:containers,stepName:currentStepData.StepTitle||`Step ${currentStep}`,stepDescription:\"\",stepType:\"Hotspot\",currentStep:currentStep,stepId:currentStepData.StepId||crypto.randomUUID(),xpath:{value:currentStepData.ElementPath||\"\",PossibleElementPath:currentStepData.PossibleElementPath||\"\",position:{x:0,y:0}},id:crypto.randomUUID(),hotspots:{XPosition:hotspotProps.XPosition||\"4\",YPosition:hotspotProps.YPosition||\"4\",Type:hotspotProps.Type||\"Question\",Color:hotspotProps.Color||\"yellow\",Size:hotspotProps.Size||\"16\",PulseAnimation:hotspotProps.PulseAnimation!==false,stopAnimationUponInteraction:hotspotProps.stopAnimationUponInteraction!==false,ShowUpon:hotspotProps.ShowUpon||\"Clicking Hotspot\",ShowByDefault:hotspotProps.ShowByDefault||false},canvas:{position:((_Canvas=currentStepData.Canvas)===null||_Canvas===void 0?void 0:_Canvas.Position)||\"auto\",autoposition:((_Canvas2=currentStepData.Canvas)===null||_Canvas2===void 0?void 0:_Canvas2.AutoPosition)||false,xaxis:((_Canvas3=currentStepData.Canvas)===null||_Canvas3===void 0?void 0:_Canvas3.XAxis)||\"0\",yaxis:((_Canvas4=currentStepData.Canvas)===null||_Canvas4===void 0?void 0:_Canvas4.YAxis)||\"0\",width:((_Canvas5=currentStepData.Canvas)===null||_Canvas5===void 0?void 0:_Canvas5.Width)||\"300px\",padding:((_Canvas6=currentStepData.Canvas)===null||_Canvas6===void 0?void 0:_Canvas6.Padding)||\"16\",borderRadius:((_Canvas7=currentStepData.Canvas)===null||_Canvas7===void 0?void 0:_Canvas7.BorderRadius)||\"8\",borderSize:((_Canvas8=currentStepData.Canvas)===null||_Canvas8===void 0?void 0:_Canvas8.BorderSize)||\"1\",borderColor:((_Canvas9=currentStepData.Canvas)===null||_Canvas9===void 0?void 0:_Canvas9.BorderColor)||\"transparent\",backgroundColor:((_Canvas10=currentStepData.Canvas)===null||_Canvas10===void 0?void 0:_Canvas10.BackgroundColor)||\"#ffffff\"},design:{gotoNext:{ButtonId:((_Design=currentStepData.Design)===null||_Design===void 0?void 0:(_Design$GotoNext=_Design.GotoNext)===null||_Design$GotoNext===void 0?void 0:_Design$GotoNext.ButtonId)||\"\",ElementPath:((_Design2=currentStepData.Design)===null||_Design2===void 0?void 0:(_Design2$GotoNext=_Design2.GotoNext)===null||_Design2$GotoNext===void 0?void 0:_Design2$GotoNext.ElementPath)||\"\",NextStep:((_Design3=currentStepData.Design)===null||_Design3===void 0?void 0:(_Design3$GotoNext=_Design3.GotoNext)===null||_Design3$GotoNext===void 0?void 0:_Design3$GotoNext.NextStep)||\"\",ButtonName:((_Design4=currentStepData.Design)===null||_Design4===void 0?void 0:(_Design4$GotoNext=_Design4.GotoNext)===null||_Design4$GotoNext===void 0?void 0:_Design4$GotoNext.ButtonName)||\"\"},element:{progress:\"\",isDismiss:false,progressSelectedOption:1,progressColor:\"var(--primarycolor)\"}}};// Update store with tour hotspot metadata\nconst store=useDrawerStore.getState();const updatedMetadata=[...store.toolTipGuideMetaData];// Ensure array has enough entries\nwhile(updatedMetadata.length<currentStep){updatedMetadata.push({containers:[],stepName:`Step ${updatedMetadata.length+1}`,stepDescription:\"\",stepType:\"Hotspot\",currentStep:updatedMetadata.length+1,stepId:crypto.randomUUID(),xpath:{value:\"\",PossibleElementPath:\"\",position:{x:0,y:0}},id:crypto.randomUUID(),hotspots:{XPosition:\"4\",YPosition:\"4\",Type:\"Question\",Color:\"yellow\",Size:\"16\",PulseAnimation:true,stopAnimationUponInteraction:true,ShowUpon:\"Clicking Hotspot\",ShowByDefault:false},canvas:{position:\"auto\",autoposition:false,xaxis:\"0\",yaxis:\"0\",width:\"300px\",padding:\"16\",borderRadius:\"8\",borderSize:\"1\",borderColor:\"transparent\",backgroundColor:\"#ffffff\"},design:{gotoNext:{ButtonId:\"\",ElementPath:\"\",NextStep:\"\",ButtonName:\"\"},element:{progress:\"\",isDismiss:false,progressSelectedOption:1,progressColor:\"var(--primarycolor)\"}}});}// Set metadata for current step\nupdatedMetadata[currentStep-1]=tourHotspotMetadata;// Update store\nuseDrawerStore.setState({toolTipGuideMetaData:updatedMetadata,elementSelected:true});// Use the function with skipOverlayReset to preserve user settings\nuseDrawerStore.getState().setSelectedTemplateTour(\"Hotspot\",true);}};const resetHeightofBanner=function(position){let padding=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0;let border=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let top=arguments.length>3&&arguments[3]!==undefined?arguments[3]:55;const styleExTag=document.getElementById(\"dynamic-body-style\");if(styleExTag){document.head.removeChild(styleExTag);}let styleTag=document.getElementById(\"dynamic-body-style\");const bodyElement=document.body;bodyElement.classList.add(\"dynamic-body-style\");if(!styleTag){styleTag=document.createElement(\"style\");styleTag.id=\"dynamic-body-style\";let styles=`\n                    .dynamic-body-style {\n                        padding-top: ${top}px !important;\n                        max-height:calc(100% - 55px);\n                    }\n\n                    `;// Add styles for body and nested elements\nif(position===\"Push Down\"){// Inside the \"Push Down\" condition:\nconst banner=document.getElementById(\"guide-popup\");const bannerHeight=(banner===null||banner===void 0?void 0:banner.offsetHeight)||49;// Include padding and border in the height calculation\nconst paddingValue=parseInt(padding.toString())||0;const borderValue=parseInt(border.toString())||0;// Only add additionalHeight if banner is null\nconst additionalHeight=paddingValue*2+borderValue*2;// Use bannerHeight + additionalHeight only if banner is null\nconst height=banner?bannerHeight:bannerHeight+additionalHeight;styles=`\n                        .dynamic-body-style {\n                            padding-top: ${height}px !important;\n                            max-height:calc(100% - 55px);\n                        }\n                       .dynamic-body-style header {\n\t\t\t\t\t\ttop: ${height}px !important;\n\t\t\t\t\t}\n\n                        \t\t.dynamic-body-style .page-sidebar {\n\t\t\t\t\t\tpadding-top: ${height}px !important;\n\t\t\t\t\t}\n                        `;}styleTag.innerHTML=styles;document.head.appendChild(styleTag);}};return/*#__PURE__*/_jsxs(\"div\",{children:[OverlayValue&&!bannerPreview&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.5)',zIndex:998,pointerEvents:\"none\"}}),bannerPreview&&/*#__PURE__*/_jsx(BannerStepPreview,{showBannerenduser:\"\",setShowBannerenduser:\"\",initialGuideData:savedGuideData,setInitialGuideData:\"\",onClose:handlecloseBannerPopup,backgroundC:backgroundC// Pass the current position from the current step\n,Bposition:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide17=savedGuideData.GuideStep)===null||_savedGuideData$Guide17===void 0?void 0:(_savedGuideData$Guide18=_savedGuideData$Guide17[currentStep-1])===null||_savedGuideData$Guide18===void 0?void 0:(_savedGuideData$Guide19=_savedGuideData$Guide18.Canvas)===null||_savedGuideData$Guide19===void 0?void 0:_savedGuideData$Guide19.Position)||\"Cover Top\",bpadding:bpadding,Bbordercolor:Bbordercolor,BborderSize:BborderSize,totalSteps:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide20=savedGuideData.GuideStep)===null||_savedGuideData$Guide20===void 0?void 0:_savedGuideData$Guide20.length)||1,ProgressColor:ProgressColor,savedGuideData:savedGuideData,progress:currentStep/((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide21=savedGuideData.GuideStep)===null||_savedGuideData$Guide21===void 0?void 0:_savedGuideData$Guide21.length)||1)*100}),announcementPreview&&/*#__PURE__*/_jsx(AnnouncementPopup,{selectedTemplate:selectedTemplate,handlecloseBannerPopup:handlecloseBannerPopup,guideStep:guideStep,anchorEl:document.body,onClose:onClose,onPrevious:()=>{},onContinue:()=>{},title:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide22=savedGuideData.GuideStep)===null||_savedGuideData$Guide22===void 0?void 0:(_savedGuideData$Guide23=_savedGuideData$Guide22[currentStep])===null||_savedGuideData$Guide23===void 0?void 0:_savedGuideData$Guide23.StepTitle)||\"\",text:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide24=savedGuideData.GuideStep)===null||_savedGuideData$Guide24===void 0?void 0:(_savedGuideData$Guide25=_savedGuideData$Guide24[currentStep])===null||_savedGuideData$Guide25===void 0?void 0:(_savedGuideData$Guide26=_savedGuideData$Guide25.TextFieldProperties)===null||_savedGuideData$Guide26===void 0?void 0:(_savedGuideData$Guide27=_savedGuideData$Guide26[0])===null||_savedGuideData$Guide27===void 0?void 0:_savedGuideData$Guide27.Text)||\"\",imageUrl:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide28=savedGuideData.GuideStep)===null||_savedGuideData$Guide28===void 0?void 0:(_savedGuideData$Guide29=_savedGuideData$Guide28[currentStep])===null||_savedGuideData$Guide29===void 0?void 0:(_savedGuideData$Guide30=_savedGuideData$Guide29.ImageProperties)===null||_savedGuideData$Guide30===void 0?void 0:(_savedGuideData$Guide31=_savedGuideData$Guide30[0])===null||_savedGuideData$Guide31===void 0?void 0:(_savedGuideData$Guide32=_savedGuideData$Guide31.CustomImage)===null||_savedGuideData$Guide32===void 0?void 0:(_savedGuideData$Guide33=_savedGuideData$Guide32[0])===null||_savedGuideData$Guide33===void 0?void 0:_savedGuideData$Guide33.Url)||\"\",previousButtonLabel:\"Back\",continueButtonLabel:\"Next\",currentStep:currentStep,totalSteps:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide34=savedGuideData.GuideStep)===null||_savedGuideData$Guide34===void 0?void 0:_savedGuideData$Guide34.length)||1,onDontShowAgain:()=>{},progress:currentStep/((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide35=savedGuideData.GuideStep)===null||_savedGuideData$Guide35===void 0?void 0:_savedGuideData$Guide35.length)||1)*100,textFieldProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide36=savedGuideData.GuideStep)===null||_savedGuideData$Guide36===void 0?void 0:(_savedGuideData$Guide37=_savedGuideData$Guide36[currentStep-1])===null||_savedGuideData$Guide37===void 0?void 0:_savedGuideData$Guide37.TextFieldProperties)||[],imageProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide38=savedGuideData.GuideStep)===null||_savedGuideData$Guide38===void 0?void 0:(_savedGuideData$Guide39=_savedGuideData$Guide38[currentStep-1])===null||_savedGuideData$Guide39===void 0?void 0:_savedGuideData$Guide39.ImageProperties)||[],customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide40=savedGuideData.GuideStep)===null||_savedGuideData$Guide40===void 0?void 0:(_savedGuideData$Guide41=_savedGuideData$Guide40[currentStep-1])===null||_savedGuideData$Guide41===void 0?void 0:(_savedGuideData$Guide42=_savedGuideData$Guide41.ButtonSection)===null||_savedGuideData$Guide42===void 0?void 0:_savedGuideData$Guide42.flatMap(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id}))))||[],modalProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide43=savedGuideData.GuideStep)===null||_savedGuideData$Guide43===void 0?void 0:(_savedGuideData$Guide44=_savedGuideData$Guide43[currentStep-1])===null||_savedGuideData$Guide44===void 0?void 0:_savedGuideData$Guide44.Modal)||{},canvasProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide45=savedGuideData.GuideStep)===null||_savedGuideData$Guide45===void 0?void 0:(_savedGuideData$Guide46=_savedGuideData$Guide45[currentStep-1])===null||_savedGuideData$Guide46===void 0?void 0:_savedGuideData$Guide46.Canvas)||{},htmlSnippet:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide47=savedGuideData.GuideStep)===null||_savedGuideData$Guide47===void 0?void 0:(_savedGuideData$Guide48=_savedGuideData$Guide47[currentStep-1])===null||_savedGuideData$Guide48===void 0?void 0:(_savedGuideData$Guide49=_savedGuideData$Guide48.TextFieldProperties)===null||_savedGuideData$Guide49===void 0?void 0:(_savedGuideData$Guide50=_savedGuideData$Guide49[0])===null||_savedGuideData$Guide50===void 0?void 0:_savedGuideData$Guide50.Text)||\"\",OverlayValue:OverlayValue,savedGuideData:savedGuideData,backgroundC:backgroundC,Bposition:Bposition,bpadding:bpadding,Bbordercolor:Bbordercolor,BborderSize:BborderSize,ProgressColor:ProgressColor}),hotspotPreview&&/*#__PURE__*/_jsx(HotspotPreview,{isHotspotPopupOpen:true,showHotspotenduser:true,handleHotspotHover:()=>{// Enable hover functionality for tour preview\nconsole.log(\"Tour hotspot hover detected\");},handleHotspotClick:()=>{// Enable click functionality for tour preview\nconsole.log(\"Tour hotspot click detected\");setOpenTooltip(true);},guideStep:guideStep,onClose:onClose,title:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide51=savedGuideData.GuideStep)===null||_savedGuideData$Guide51===void 0?void 0:(_savedGuideData$Guide52=_savedGuideData$Guide51[currentStep-1])===null||_savedGuideData$Guide52===void 0?void 0:_savedGuideData$Guide52.StepType)||\"\",text:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide53=savedGuideData.GuideStep)===null||_savedGuideData$Guide53===void 0?void 0:(_savedGuideData$Guide54=_savedGuideData$Guide53[currentStep])===null||_savedGuideData$Guide54===void 0?void 0:(_savedGuideData$Guide55=_savedGuideData$Guide54.TextFieldProperties)===null||_savedGuideData$Guide55===void 0?void 0:(_savedGuideData$Guide56=_savedGuideData$Guide55[0])===null||_savedGuideData$Guide56===void 0?void 0:_savedGuideData$Guide56.Text)||\"\",imageUrl:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide57=savedGuideData.GuideStep)===null||_savedGuideData$Guide57===void 0?void 0:(_savedGuideData$Guide58=_savedGuideData$Guide57[currentStep])===null||_savedGuideData$Guide58===void 0?void 0:(_savedGuideData$Guide59=_savedGuideData$Guide58.ImageProperties)===null||_savedGuideData$Guide59===void 0?void 0:(_savedGuideData$Guide60=_savedGuideData$Guide59[0])===null||_savedGuideData$Guide60===void 0?void 0:(_savedGuideData$Guide61=_savedGuideData$Guide60.CustomImage)===null||_savedGuideData$Guide61===void 0?void 0:(_savedGuideData$Guide62=_savedGuideData$Guide61[0])===null||_savedGuideData$Guide62===void 0?void 0:_savedGuideData$Guide62.Url)||\"\",onPrevious:()=>{},onContinue:()=>{},currentStep:currentStep// Adjust currentStep for display (1-based index)\n,totalSteps:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide63=savedGuideData.GuideStep)===null||_savedGuideData$Guide63===void 0?void 0:_savedGuideData$Guide63.length)||1,onDontShowAgain:()=>{},progress:currentStep/((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide64=savedGuideData.GuideStep)===null||_savedGuideData$Guide64===void 0?void 0:_savedGuideData$Guide64.length)||1)*100,textFieldProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide65=savedGuideData.GuideStep)===null||_savedGuideData$Guide65===void 0?void 0:(_savedGuideData$Guide66=_savedGuideData$Guide65[currentStep-1])===null||_savedGuideData$Guide66===void 0?void 0:_savedGuideData$Guide66.TextFieldProperties)||[],imageProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide67=savedGuideData.GuideStep)===null||_savedGuideData$Guide67===void 0?void 0:(_savedGuideData$Guide68=_savedGuideData$Guide67[currentStep-1])===null||_savedGuideData$Guide68===void 0?void 0:_savedGuideData$Guide68.ImageProperties)||[],customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide69=savedGuideData.GuideStep)===null||_savedGuideData$Guide69===void 0?void 0:(_savedGuideData$Guide70=_savedGuideData$Guide69[currentStep-1])===null||_savedGuideData$Guide70===void 0?void 0:(_savedGuideData$Guide71=_savedGuideData$Guide70.ButtonSection)===null||_savedGuideData$Guide71===void 0?void 0:(_savedGuideData$Guide72=_savedGuideData$Guide71.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide72===void 0?void 0:_savedGuideData$Guide72.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide73=savedGuideData.GuideStep)===null||_savedGuideData$Guide73===void 0?void 0:(_savedGuideData$Guide74=_savedGuideData$Guide73[currentStep-1])===null||_savedGuideData$Guide74===void 0?void 0:_savedGuideData$Guide74.Modal)||{},canvasProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide75=savedGuideData.GuideStep)===null||_savedGuideData$Guide75===void 0?void 0:(_savedGuideData$Guide76=_savedGuideData$Guide75[currentStep-1])===null||_savedGuideData$Guide76===void 0?void 0:_savedGuideData$Guide76.Canvas)||{},htmlSnippet:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide77=savedGuideData.GuideStep)===null||_savedGuideData$Guide77===void 0?void 0:(_savedGuideData$Guide78=_savedGuideData$Guide77[currentStep-1])===null||_savedGuideData$Guide78===void 0?void 0:(_savedGuideData$Guide79=_savedGuideData$Guide78.TextFieldProperties)===null||_savedGuideData$Guide79===void 0?void 0:(_savedGuideData$Guide80=_savedGuideData$Guide79[0])===null||_savedGuideData$Guide80===void 0?void 0:_savedGuideData$Guide80.Text)||\"\",OverlayValue:OverlayValue,savedGuideData:savedGuideData,hotspotProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide81=savedGuideData.GuideStep)===null||_savedGuideData$Guide81===void 0?void 0:(_savedGuideData$Guide82=_savedGuideData$Guide81[currentStep-1])===null||_savedGuideData$Guide82===void 0?void 0:_savedGuideData$Guide82.Hotspot)||{},anchorEl:document.body}),tooltipPreview&&/*#__PURE__*/_jsx(TooltiplastUserview,{guideStep:guideStep,onClose:onClose,title:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide83=savedGuideData.GuideStep)===null||_savedGuideData$Guide83===void 0?void 0:(_savedGuideData$Guide84=_savedGuideData$Guide83[currentStep])===null||_savedGuideData$Guide84===void 0?void 0:_savedGuideData$Guide84.StepTitle)||\"\",text:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide85=savedGuideData.GuideStep)===null||_savedGuideData$Guide85===void 0?void 0:(_savedGuideData$Guide86=_savedGuideData$Guide85[currentStep])===null||_savedGuideData$Guide86===void 0?void 0:(_savedGuideData$Guide87=_savedGuideData$Guide86.TextFieldProperties)===null||_savedGuideData$Guide87===void 0?void 0:(_savedGuideData$Guide88=_savedGuideData$Guide87[0])===null||_savedGuideData$Guide88===void 0?void 0:_savedGuideData$Guide88.Text)||\"\",imageUrl:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide89=savedGuideData.GuideStep)===null||_savedGuideData$Guide89===void 0?void 0:(_savedGuideData$Guide90=_savedGuideData$Guide89[currentStep])===null||_savedGuideData$Guide90===void 0?void 0:(_savedGuideData$Guide91=_savedGuideData$Guide90.ImageProperties)===null||_savedGuideData$Guide91===void 0?void 0:(_savedGuideData$Guide92=_savedGuideData$Guide91[0])===null||_savedGuideData$Guide92===void 0?void 0:(_savedGuideData$Guide93=_savedGuideData$Guide92.CustomImage)===null||_savedGuideData$Guide93===void 0?void 0:(_savedGuideData$Guide94=_savedGuideData$Guide93[0])===null||_savedGuideData$Guide94===void 0?void 0:_savedGuideData$Guide94.Url)||\"\",onPrevious:()=>{},onContinue:()=>{},currentStep:currentStep// Adjust currentStep for display (1-based index)\n,totalSteps:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide95=savedGuideData.GuideStep)===null||_savedGuideData$Guide95===void 0?void 0:_savedGuideData$Guide95.length)||1,onDontShowAgain:()=>{},progress:(currentStep+1)/((savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide96=savedGuideData.GuideStep)===null||_savedGuideData$Guide96===void 0?void 0:_savedGuideData$Guide96.length)||1)*100,textFieldProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide97=savedGuideData.GuideStep)===null||_savedGuideData$Guide97===void 0?void 0:(_savedGuideData$Guide98=_savedGuideData$Guide97[currentStep-1])===null||_savedGuideData$Guide98===void 0?void 0:_savedGuideData$Guide98.TextFieldProperties)||[],imageProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide99=savedGuideData.GuideStep)===null||_savedGuideData$Guide99===void 0?void 0:(_savedGuideData$Guide100=_savedGuideData$Guide99[currentStep-1])===null||_savedGuideData$Guide100===void 0?void 0:_savedGuideData$Guide100.ImageProperties)||[],customButton:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide101=savedGuideData.GuideStep)===null||_savedGuideData$Guide101===void 0?void 0:(_savedGuideData$Guide102=_savedGuideData$Guide101[currentStep-1])===null||_savedGuideData$Guide102===void 0?void 0:(_savedGuideData$Guide103=_savedGuideData$Guide102.ButtonSection)===null||_savedGuideData$Guide103===void 0?void 0:(_savedGuideData$Guide104=_savedGuideData$Guide103.map(section=>section.CustomButtons.map(button=>({...button,ContainerId:section.Id// Attach the container ID for grouping\n}))))===null||_savedGuideData$Guide104===void 0?void 0:_savedGuideData$Guide104.reduce((acc,curr)=>acc.concat(curr),[]))||[],modalProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide105=savedGuideData.GuideStep)===null||_savedGuideData$Guide105===void 0?void 0:(_savedGuideData$Guide106=_savedGuideData$Guide105[currentStep-1])===null||_savedGuideData$Guide106===void 0?void 0:_savedGuideData$Guide106.Modal)||{},canvasProperties:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide107=savedGuideData.GuideStep)===null||_savedGuideData$Guide107===void 0?void 0:(_savedGuideData$Guide108=_savedGuideData$Guide107[currentStep-1])===null||_savedGuideData$Guide108===void 0?void 0:_savedGuideData$Guide108.Canvas)||{},htmlSnippet:(savedGuideData===null||savedGuideData===void 0?void 0:(_savedGuideData$Guide109=savedGuideData.GuideStep)===null||_savedGuideData$Guide109===void 0?void 0:(_savedGuideData$Guide110=_savedGuideData$Guide109[currentStep-1])===null||_savedGuideData$Guide110===void 0?void 0:(_savedGuideData$Guide111=_savedGuideData$Guide110.TextFieldProperties)===null||_savedGuideData$Guide111===void 0?void 0:(_savedGuideData$Guide112=_savedGuideData$Guide111[0])===null||_savedGuideData$Guide112===void 0?void 0:_savedGuideData$Guide112.Text)||\"\",OverlayValue:OverlayValue,savedGuideData:savedGuideData//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\n,anchorEl:document.body,previousButtonLabel:\"\",continueButtonLabel:\"\"})]});};export default TourPreview;", "map": {"version": 3, "names": ["React", "useEffect", "useDrawerStore", "AnnouncementPopup", "HotspotPreview", "TooltiplastUserview", "BannerStepPreview", "jsx", "_jsx", "jsxs", "_jsxs", "TourPreview", "_ref", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "_savedGuideData$Guide37", "_savedGuideData$Guide38", "_savedGuideData$Guide39", "_savedGuideData$Guide40", "_savedGuideData$Guide41", "_savedGuideData$Guide42", "_savedGuideData$Guide43", "_savedGuideData$Guide44", "_savedGuideData$Guide45", "_savedGuideData$Guide46", "_savedGuideData$Guide47", "_savedGuideData$Guide48", "_savedGuideData$Guide49", "_savedGuideData$Guide50", "_savedGuideData$Guide51", "_savedGuideData$Guide52", "_savedGuideData$Guide53", "_savedGuideData$Guide54", "_savedGuideData$Guide55", "_savedGuideData$Guide56", "_savedGuideData$Guide57", "_savedGuideData$Guide58", "_savedGuideData$Guide59", "_savedGuideData$Guide60", "_savedGuideData$Guide61", "_savedGuideData$Guide62", "_savedGuideData$Guide63", "_savedGuideData$Guide64", "_savedGuideData$Guide65", "_savedGuideData$Guide66", "_savedGuideData$Guide67", "_savedGuideData$Guide68", "_savedGuideData$Guide69", "_savedGuideData$Guide70", "_savedGuideData$Guide71", "_savedGuideData$Guide72", "_savedGuideData$Guide73", "_savedGuideData$Guide74", "_savedGuideData$Guide75", "_savedGuideData$Guide76", "_savedGuideData$Guide77", "_savedGuideData$Guide78", "_savedGuideData$Guide79", "_savedGuideData$Guide80", "_savedGuideData$Guide81", "_savedGuideData$Guide82", "_savedGuideData$Guide83", "_savedGuideData$Guide84", "_savedGuideData$Guide85", "_savedGuideData$Guide86", "_savedGuideData$Guide87", "_savedGuideData$Guide88", "_savedGuideData$Guide89", "_savedGuideData$Guide90", "_savedGuideData$Guide91", "_savedGuideData$Guide92", "_savedGuideData$Guide93", "_savedGuideData$Guide94", "_savedGuideData$Guide95", "_savedGuideData$Guide96", "_savedGuideData$Guide97", "_savedGuideData$Guide98", "_savedGuideData$Guide99", "_savedGuideData$Guide100", "_savedGuideData$Guide101", "_savedGuideData$Guide102", "_savedGuideData$Guide103", "_savedGuideData$Guide104", "_savedGuideData$Guide105", "_savedGuideData$Guide106", "_savedGuideData$Guide107", "_savedGuideData$Guide108", "_savedGuideData$Guide109", "_savedGuideData$Guide110", "_savedGuideData$Guide111", "_savedGuideData$Guide112", "selectedTemplate", "handlecloseBannerPopup", "backgroundC", "Bposition", "bpadding", "Bbordercolor", "BborderSize", "guideStep", "anchorEl", "onClose", "onPrevious", "onContinue", "title", "text", "imageUrl", "videoUrl", "previousButtonLabel", "continueButtonLabel", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "HotspotGuideDetails", "announcementPreview", "setAnnouncementPreview", "bannerPreview", "setBannerPreview", "tooltipPreview", "setTooltipPreview", "hotspotPreview", "setHotspotPreview", "setCurrentStep", "setOpenTooltip", "ProgressColor", "setProgressColor", "state", "stepType", "GuideStep", "StepType", "existingHotspot", "document", "getElementById", "style", "display", "remove", "resetHeightofBanner", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "currentPosition", "<PERSON><PERSON>", "Position", "parseInt", "Padding", "BorderSize", "_savedGuideData$Guide12", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "_savedGuideData$Guide15", "initializeTourHotspotMetadata", "console", "log", "hotspotData", "Hotspot", "elementPath", "<PERSON>ement<PERSON><PERSON>", "_savedGuideData$Guide16", "currentStepData", "_<PERSON><PERSON>", "_Canvas2", "_Canvas3", "_Canvas4", "_Canvas5", "_Canvas6", "_Canvas7", "_Canvas8", "_Canvas9", "_Canvas10", "_Design", "_Design$GotoNext", "_Design2", "_Design2$GotoNext", "_Design3", "_Design3$GotoNext", "_Design4", "_Design4$GotoNext", "hotspotProps", "containers", "TextFieldProperties", "length", "for<PERSON>ach", "textField", "push", "id", "Id", "crypto", "randomUUID", "type", "placeholder", "rteBoxValue", "Text", "backgroundColor", "ButtonSection", "section", "_section$CustomButton", "buttons", "CustomButtons", "map", "button", "_button$ButtonAction", "_button$ButtonAction2", "_button$ButtonAction3", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "ButtonId", "name", "ButtonName", "ButtonStyle", "position", "Alignment", "actions", "value", "ButtonAction", "Action", "targetURL", "TargetUrl", "tab", "ActionValue", "ButtonProperties", "ButtonBackgroundColor", "color", "ButtonTextColor", "borderColor", "ButtonBorderColor", "ImageProperties", "imageProperty", "_imageProperty$Custom", "images", "CustomImage", "image", "url", "Url", "altText", "AltText", "tourHotspotMetadata", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "stepDescription", "stepId", "StepId", "xpath", "PossibleElementPath", "x", "y", "hotspots", "XPosition", "YPosition", "Type", "Color", "Size", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "ShowByDefault", "canvas", "autoposition", "AutoPosition", "xaxis", "XAxis", "yaxis", "YA<PERSON>s", "width", "<PERSON><PERSON><PERSON>", "padding", "borderRadius", "BorderRadius", "borderSize", "BorderColor", "BackgroundColor", "design", "gotoNext", "Design", "GotoNext", "NextStep", "element", "<PERSON><PERSON><PERSON><PERSON>", "progressSelectedOption", "progressColor", "store", "getState", "updatedMetadata", "toolTipGuideMetaData", "setState", "elementSelected", "arguments", "undefined", "border", "top", "styleExTag", "head", "<PERSON><PERSON><PERSON><PERSON>", "styleTag", "bodyElement", "body", "classList", "add", "createElement", "styles", "banner", "bannerHeight", "offsetHeight", "paddingValue", "toString", "borderValue", "additionalHeight", "height", "innerHTML", "append<PERSON><PERSON><PERSON>", "children", "left", "right", "bottom", "zIndex", "pointerEvents", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "setInitialGuideData", "flatMap", "ContainerId", "Modal", "isHotspotPopupOpen", "showHotspotenduser", "handleHotspotHover", "handleHotspotClick", "reduce", "acc", "curr", "concat", "hotspotProperties"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/tours/tourPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Popover, Button, Typography, Box, LinearProgress, DialogActions,IconButton } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { PopoverOrigin } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport AnnouncementPopup from \"../GuidesPreview/AnnouncementPreview\";\r\nimport HotspotPreview from \"../GuidesPreview/HotspotPreview\";\r\nimport TooltiplastUserview from \"../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview\";\r\nimport BannerStepPreview from \"./BannerStepPreview\";\r\ninterface PopupProps {\r\n    handlecloseBannerPopup: any;\r\n    guideStep: any[];\r\n    anchorEl: null | HTMLElement;\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonLabel: string;\r\n    continueButtonLabel: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    backgroundC: any;\r\n    Bposition: any;\r\n    bpadding: any;\r\n    Bbordercolor: any;\r\n    BborderSize: any;\r\n    savedGuideData: GuideData | null;\r\n    selectedTemplate:any}\r\n\r\n\r\n\r\n    const TourPreview: React.FC<PopupProps> = ({\r\n        selectedTemplate,\r\n        handlecloseBannerPopup,\r\n        backgroundC,\r\n        Bposition,\r\n        bpadding,\r\n        Bbordercolor,\r\n        BborderSize,\r\n        guideStep,\r\n        anchorEl,\r\n        onClose,\r\n        onPrevious,\r\n        onContinue,\r\n        title,\r\n        text,\r\n        imageUrl,\r\n        videoUrl,\r\n        previousButtonLabel,\r\n        continueButtonLabel,\r\n        currentStep,\r\n        totalSteps,\r\n        onDontShowAgain,\r\n        progress,\r\n        textFieldProperties,\r\n        imageProperties,\r\n        customButton,\r\n        modalProperties,\r\n        canvasProperties,\r\n        htmlSnippet,\r\n        previousButtonStyles,\r\n        continueButtonStyles,\r\n        OverlayValue,\r\n        savedGuideData\r\n    }) => {\r\n        const {\r\n            setSelectedTemplate,\r\n            setBannerPopup,\r\n          setSelectedTemplateTour,\r\n          setSteps,\r\n          steps,\r\n          setTooltipCount,\r\n          tooltipCount,\r\n            HotspotGuideDetails,\r\n            announcementPreview, setAnnouncementPreview,\r\n            bannerPreview, setBannerPreview,\r\n            tooltipPreview, setTooltipPreview,\r\n            hotspotPreview, setHotspotPreview,\r\n            setCurrentStep,\r\n            setOpenTooltip,\r\n            ProgressColor,\r\n            setProgressColor\r\n        } = useDrawerStore((state: DrawerState) => state);\r\n\r\n        const stepType = savedGuideData?.GuideStep?.[currentStep - 1]?.StepType;\r\n\r\n        // Note: AI data synchronization is now handled in the store's setCurrentStep function\r\n        // to avoid infinite loops and ensure proper timing\r\n\r\n        useEffect(() => {\r\n            // Clean up any existing hotspot before setting new preview\r\n            const existingHotspot = document.getElementById(\"hotspotBlink\");\r\n            if (existingHotspot) {\r\n                existingHotspot.style.display = \"none\";\r\n                existingHotspot.remove();\r\n            }\r\n            // Reset all previews before setting the specific one\r\n            setAnnouncementPreview(false);\r\n            setBannerPreview(false);\r\n            setTooltipPreview(false);\r\n            setHotspotPreview(false);\r\n\r\n\r\n\r\n            // Set the correct preview based on stepType\r\n            if (stepType === \"Announcement\") {\r\n                setAnnouncementPreview(true);\r\n                resetHeightofBanner(\"\",0,0,0)\r\n            } else if (stepType === \"Banner\") {\r\n                setBannerPreview(true);\r\n                  // Get the current Canvas position from the step\r\n                  const currentPosition = savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position;\r\n                  // Use the current position value to properly apply Push Down effect\r\n                  resetHeightofBanner(\r\n                      currentPosition,\r\n                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Padding||\"0\"),\r\n                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.BorderSize||\"0\"),    0\r\n                                  );\r\n            } else if (stepType === \"Tooltip\") {\r\n                setTooltipPreview(true);\r\n                resetHeightofBanner(\"\",0,0,0)\r\n            } else if(stepType === \"Hotspot\") {\r\n                setHotspotPreview(true);\r\n                setOpenTooltip(false)\r\n                resetHeightofBanner(\"\",0,0,0)\r\n\r\n                // Initialize tour hotspot metadata for proper functionality\r\n                initializeTourHotspotMetadata();\r\n\r\n                // Ensure pulse animation is enabled for tour hotspots\r\n                // useDrawerStore.setState({ pulseAnimationsH: true });\r\n\r\n                // Debug logging for tour hotspot initialization\r\n                console.log(\"Tour hotspot initialized:\", {\r\n                    stepType,\r\n                    currentStep,\r\n                    hotspotData: savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot,\r\n                    elementPath: savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n                });\r\n            }\r\n        }, [stepType, currentStep]);  // Dependencies ensure effect runs when stepType or currentStep changes\r\n\r\n        // Initialize tour hotspot metadata for HotspotPreview compatibility\r\n        const initializeTourHotspotMetadata = () => {\r\n            const currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n            if (currentStepData && currentStepData.StepType === \"Hotspot\") {\r\n                const hotspotProps = (currentStepData as any).Hotspot || {};\r\n\r\n                // Create metadata structure for HotspotPreview\r\n                // Extract containers from saved guide data\r\n                const containers: any[] = [];\r\n\r\n                // Add RTE containers from TextFieldProperties\r\n                if (currentStepData.TextFieldProperties && currentStepData.TextFieldProperties.length > 0) {\r\n                    currentStepData.TextFieldProperties.forEach((textField: any) => {\r\n                        containers.push({\r\n                            id: textField.Id || crypto.randomUUID(),\r\n                            type: \"rte\",\r\n                            placeholder: \"Start typing here...\",\r\n                            rteBoxValue: textField.Text || \"\",\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                // Add button containers from ButtonSection\r\n                if (currentStepData.ButtonSection && currentStepData.ButtonSection.length > 0) {\r\n                    currentStepData.ButtonSection.forEach((section: any) => {\r\n                        containers.push({\r\n                            id: section.Id || crypto.randomUUID(),\r\n                            type: \"button\",\r\n                            buttons: section.CustomButtons?.map((button: any) => ({\r\n                                id: button.ButtonId || crypto.randomUUID(),\r\n                                name: button.ButtonName || \"Button\",\r\n                                type: button.ButtonStyle || \"primary\",\r\n                                position: button.Alignment || \"center\",\r\n                                actions: {\r\n                                    value: button.ButtonAction?.Action || \"close\",\r\n                                    targetURL: button.ButtonAction?.TargetUrl || \"\",\r\n                                    tab: button.ButtonAction?.ActionValue || \"same-tab\"\r\n                                },\r\n                                style: {\r\n                                    backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#5F9EA0\",\r\n                                    color: button.ButtonProperties?.ButtonTextColor || \"#ffffff\",\r\n                                    borderColor: button.ButtonProperties?.ButtonBorderColor || \"#5F9EA0\",\r\n                                }\r\n                            })) || [],\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                // Add image containers from ImageProperties\r\n                if (currentStepData.ImageProperties && currentStepData.ImageProperties.length > 0) {\r\n                    currentStepData.ImageProperties.forEach((imageProperty: any) => {\r\n                        containers.push({\r\n                            id: imageProperty.Id || crypto.randomUUID(),\r\n                            type: \"image\",\r\n                            images: imageProperty.CustomImage?.map((image: any) => ({\r\n                                url: image.Url || \"\",\r\n                                altText: image.AltText || \"\",\r\n                            })) || [],\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                const tourHotspotMetadata = {\r\n                    containers: containers,\r\n                    stepName: (currentStepData as any).StepTitle || `Step ${currentStep}`,\r\n                    stepDescription: \"\",\r\n                    stepType: \"Hotspot\" as const,\r\n                    currentStep: currentStep,\r\n                    stepId: (currentStepData as any).StepId || crypto.randomUUID(),\r\n                    xpath: {\r\n                        value: (currentStepData as any).ElementPath || \"\",\r\n                        PossibleElementPath: (currentStepData as any).PossibleElementPath || \"\",\r\n                        position: { x: 0, y: 0 }\r\n                    },\r\n                    id: crypto.randomUUID(),\r\n                    hotspots: {\r\n                        XPosition: (hotspotProps as any).XPosition || \"4\",\r\n                        YPosition: (hotspotProps as any).YPosition || \"4\",\r\n                        Type: (hotspotProps as any).Type || \"Question\",\r\n                        Color: (hotspotProps as any).Color || \"yellow\",\r\n                        Size: (hotspotProps as any).Size || \"16\",\r\n                        PulseAnimation: (hotspotProps as any).PulseAnimation !== false,\r\n                        stopAnimationUponInteraction: (hotspotProps as any).stopAnimationUponInteraction !== false,\r\n                        ShowUpon: (hotspotProps as any).ShowUpon || \"Clicking Hotspot\",\r\n                        ShowByDefault: (hotspotProps as any).ShowByDefault || false,\r\n                    },\r\n                    canvas: {\r\n                        position: ((currentStepData as any).Canvas?.Position || \"auto\"),\r\n                        autoposition: ((currentStepData as any).Canvas?.AutoPosition || false),\r\n                        xaxis: ((currentStepData as any).Canvas?.XAxis || \"0\"),\r\n                        yaxis: ((currentStepData as any).Canvas?.YAxis || \"0\"),\r\n                        width: ((currentStepData as any).Canvas?.Width || \"300px\"),\r\n                        padding: ((currentStepData as any).Canvas?.Padding || \"16\"),\r\n                        borderRadius: ((currentStepData as any).Canvas?.BorderRadius || \"8\"),\r\n                        borderSize: ((currentStepData as any).Canvas?.BorderSize || \"1\"),\r\n                        borderColor: ((currentStepData as any).Canvas?.BorderColor || \"transparent\"),\r\n                        backgroundColor: ((currentStepData as any).Canvas?.BackgroundColor || \"#ffffff\"),\r\n                    },\r\n                    design: {\r\n                        gotoNext: {\r\n                            ButtonId: ((currentStepData as any).Design?.GotoNext?.ButtonId || \"\"),\r\n                            ElementPath: ((currentStepData as any).Design?.GotoNext?.ElementPath || \"\"),\r\n                            NextStep: ((currentStepData as any).Design?.GotoNext?.NextStep || \"\"),\r\n                            ButtonName: ((currentStepData as any).Design?.GotoNext?.ButtonName || \"\"),\r\n                        },\r\n                        element: {\r\n                            progress: \"\",\r\n                            isDismiss: false,\r\n                            progressSelectedOption: 1,\r\n                            progressColor: \"var(--primarycolor)\",\r\n                        },\r\n                    },\r\n                };\r\n\r\n                // Update store with tour hotspot metadata\r\n                const store = useDrawerStore.getState();\r\n                const updatedMetadata = [...store.toolTipGuideMetaData];\r\n\r\n                // Ensure array has enough entries\r\n                while (updatedMetadata.length < currentStep) {\r\n                    updatedMetadata.push({\r\n                        containers: [],\r\n                        stepName: `Step ${updatedMetadata.length + 1}`,\r\n                        stepDescription: \"\",\r\n                        stepType: \"Hotspot\",\r\n                        currentStep: updatedMetadata.length + 1,\r\n                        stepId: crypto.randomUUID(),\r\n                        xpath: { value: \"\", PossibleElementPath: \"\", position: { x: 0, y: 0 } },\r\n                        id: crypto.randomUUID(),\r\n                        hotspots: {\r\n                            XPosition: \"4\",\r\n                            YPosition: \"4\",\r\n                            Type: \"Question\",\r\n                            Color: \"yellow\",\r\n                            Size: \"16\",\r\n                            PulseAnimation: true,\r\n                            stopAnimationUponInteraction: true,\r\n                            ShowUpon: \"Clicking Hotspot\",\r\n                            ShowByDefault: false,\r\n                        },\r\n                        canvas: {\r\n                            position: \"auto\",\r\n                            autoposition: false,\r\n                            xaxis: \"0\",\r\n                            yaxis: \"0\",\r\n                            width: \"300px\",\r\n                            padding: \"16\",\r\n                            borderRadius: \"8\",\r\n                            borderSize: \"1\",\r\n                            borderColor: \"transparent\",\r\n                            backgroundColor: \"#ffffff\",\r\n                        },\r\n                        design: {\r\n                            gotoNext: {\r\n                                ButtonId: \"\",\r\n                                ElementPath: \"\",\r\n                                NextStep: \"\",\r\n                                ButtonName: \"\",\r\n                            },\r\n                            element: {\r\n                                progress: \"\",\r\n                                isDismiss: false,\r\n                                progressSelectedOption: 1,\r\n                                progressColor: \"var(--primarycolor)\",\r\n                            },\r\n                        },\r\n                    });\r\n                }\r\n\r\n                // Set metadata for current step\r\n                updatedMetadata[currentStep - 1] = tourHotspotMetadata as any;\r\n\r\n                // Update store\r\n                useDrawerStore.setState({\r\n                    toolTipGuideMetaData: updatedMetadata,\r\n                    elementSelected: true\r\n                });\r\n                // Use the function with skipOverlayReset to preserve user settings\r\n                useDrawerStore.getState().setSelectedTemplateTour(\"Hotspot\", true);\r\n            }\r\n        };\r\n\r\n        const resetHeightofBanner = (position : any,padding: any = 0,border : any =0, top: any = 55) => {\r\n            const styleExTag = document.getElementById(\"dynamic-body-style\");\r\n            if (styleExTag) {\r\n                document.head.removeChild(styleExTag);\r\n            }\r\n            let styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n                const bodyElement = document.body;\r\n\r\n                bodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n                if (!styleTag) {\r\n                    styleTag = document.createElement(\"style\");\r\n                    styleTag.id = \"dynamic-body-style\";\r\n\r\n                    let styles = `\r\n                    .dynamic-body-style {\r\n                        padding-top: ${top}px !important;\r\n                        max-height:calc(100% - 55px);\r\n                    }\r\n\r\n                    `;\r\n                // Add styles for body and nested elements\r\n                if (position === \"Push Down\")\r\n                    {\r\n                        // Inside the \"Push Down\" condition:\r\n    const banner = document.getElementById(\"guide-popup\");\r\n    const bannerHeight = banner?.offsetHeight || 49;\r\n    // Include padding and border in the height calculation\r\n    const paddingValue = parseInt(padding.toString()) || 0;\r\n    const borderValue = parseInt(border.toString()) || 0;\r\n    // Only add additionalHeight if banner is null\r\n    const additionalHeight = (paddingValue * 2) + (borderValue * 2);\r\n    // Use bannerHeight + additionalHeight only if banner is null\r\n    const height = banner ? bannerHeight : bannerHeight + additionalHeight;\r\n\r\n                        styles = `\r\n                        .dynamic-body-style {\r\n                            padding-top: ${height}px !important;\r\n                            max-height:calc(100% - 55px);\r\n                        }\r\n                       .dynamic-body-style header {\r\n\t\t\t\t\t\ttop: ${height}px !important;\r\n\t\t\t\t\t}\r\n\r\n                        \t\t.dynamic-body-style .page-sidebar {\r\n\t\t\t\t\t\tpadding-top: ${height}px !important;\r\n\t\t\t\t\t}\r\n                        `;\r\n\r\n\r\n                    }\r\n\r\n                    styleTag.innerHTML = styles;\r\n                    document.head.appendChild(styleTag);\r\n                }\r\n        }\r\n\r\n        return (\r\n            <div>\r\n                {OverlayValue && !bannerPreview && (\r\n                    <div style={{\r\n                        position: 'fixed',\r\n                        top: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        bottom: 0,\r\n                        backgroundColor: 'rgba(0, 0, 0, 0.5)',\r\n                        zIndex: 998,\r\n                        pointerEvents: \"none\"\r\n                    }} />\r\n                )}\r\n      {bannerPreview  && (\r\n                 <BannerStepPreview\r\n                 showBannerenduser=\"\"\r\n                 setShowBannerenduser=\"\"\r\n                 initialGuideData={savedGuideData}\r\n                 setInitialGuideData=\"\"\r\n                 onClose={handlecloseBannerPopup}\r\n                 backgroundC={backgroundC}\r\n                // Pass the current position from the current step\r\n                Bposition={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position || \"Cover Top\"}                 bpadding={bpadding}\r\n                 Bbordercolor={Bbordercolor}\r\n                        BborderSize={BborderSize}\r\n                        totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                        ProgressColor={ProgressColor}\r\n                        savedGuideData={savedGuideData}\r\n                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n             />\r\n            )}\r\n                {announcementPreview &&(\r\n                    <AnnouncementPopup\r\n                        selectedTemplate={selectedTemplate}\r\n                        handlecloseBannerPopup={handlecloseBannerPopup}\r\n                        guideStep={guideStep}\r\n                        anchorEl={document.body}\r\n                        onClose={onClose}\r\n                        onPrevious={() => {}}\r\n                        onContinue={() => {}}\r\n                        title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || \"\"}\r\n                        text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                        imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n                        previousButtonLabel=\"Back\"\r\n                        continueButtonLabel=\"Next\"\r\n                        currentStep={currentStep}\r\n                        totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                        onDontShowAgain={() => {}}\r\n                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n                        textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n                        imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n                        customButton={\r\n                            savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.flatMap(section =>\r\n                                section.CustomButtons.map(button => ({\r\n                                    ...button,\r\n                                    ContainerId: section.Id,\r\n                                }))\r\n                            ) || []\r\n                        }\r\n                        modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n                        canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n                        htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                        OverlayValue={OverlayValue}\r\n                        savedGuideData={savedGuideData}\r\n                        backgroundC={backgroundC}\r\n                        Bposition={Bposition}\r\n                        bpadding={bpadding}\r\n                        Bbordercolor={Bbordercolor}\r\n                        BborderSize={BborderSize}\r\n                        ProgressColor={ProgressColor}\r\n                    />\r\n                )}\r\n\r\n                {hotspotPreview && (\r\n                   <HotspotPreview\r\n                   isHotspotPopupOpen={true}\r\n                   showHotspotenduser={true}\r\n                   handleHotspotHover={() => {\r\n                       // Enable hover functionality for tour preview\r\n                       console.log(\"Tour hotspot hover detected\");\r\n                   }}\r\n                   handleHotspotClick={() => {\r\n                       // Enable click functionality for tour preview\r\n                       console.log(\"Tour hotspot click detected\");\r\n                       setOpenTooltip(true);\r\n                   }}\r\n                   guideStep={guideStep}\r\n                   onClose={onClose}\r\n                   title={savedGuideData?.GuideStep?.[currentStep-1]?.StepType || \"\"}\r\n                   text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                   imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n                   onPrevious={() => {}}\r\n                   onContinue={() => {}}\r\n                   currentStep={currentStep} // Adjust currentStep for display (1-based index)\r\n                   totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                   onDontShowAgain={() => {}}\r\n                   progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n                   textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n                   imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n                   customButton={\r\n                       savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>\r\n                           section.CustomButtons.map((button) => ({\r\n                               ...button,\r\n                               ContainerId: section.Id, // Attach the container ID for grouping\r\n                           }))\r\n                       )?.reduce((acc, curr) => acc.concat(curr), []) || []\r\n                   }\r\n                   modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n                   canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n                   htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                   OverlayValue={OverlayValue}\r\n                   savedGuideData={savedGuideData}\r\n                   hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n                   anchorEl={document.body}\r\n\r\n               />\r\n                )}\r\n\r\n\r\n                {tooltipPreview && (\r\n\r\n\r\n                    <TooltiplastUserview\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\ttitle={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || \"\"}\r\n\t\t\t\ttext={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n\t\t\t\timageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n\t\t\t\tonPrevious={() => {}}\r\n\t\t\t\tonContinue={() => {}}\r\n\t\t\t\tcurrentStep={currentStep} // Adjust currentStep for display (1-based index)\r\n\t\t\t\ttotalSteps={savedGuideData?.GuideStep?.length || 1}\r\n\t\t\t\tonDontShowAgain={() => {}}\r\n\t\t\t\tprogress={((currentStep + 1) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc, curr) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n\t\t\t\tcanvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n\t\t\t\thtmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\t//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t\tanchorEl={document.body}\r\n\t\t\t\tpreviousButtonLabel={\"\"}\r\n\t\t\t\tcontinueButtonLabel={\"\"}\r\n\t\t\t/>\r\n\r\n)}\r\n\r\n\r\n                {/* Add similar logic for Banner, Tooltip, and Hotspot if needed */}\r\n            </div>\r\n        );\r\n    };\r\n\r\n    export default TourPreview;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAcC,SAAS,KAAQ,OAAO,CAIlD,MAAO,CAAAC,cAAc,KAAuB,yBAAyB,CAErE,MAAO,CAAAC,iBAAiB,KAAM,sCAAsC,CACpE,MAAO,CAAAC,cAAc,KAAM,iCAAiC,CAC5D,MAAO,CAAAC,mBAAmB,KAAM,0DAA0D,CAC1F,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAqDhD,KAAM,CAAAC,WAAiC,CAAGC,IAAA,EAiCpC,KAAAC,qBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,CAAAC,wBAAA,IAjCqC,CACvCC,gBAAgB,CAChBC,sBAAsB,CACtBC,WAAW,CACXC,SAAS,CACTC,QAAQ,CACRC,YAAY,CACZC,WAAW,CACXC,SAAS,CACTC,QAAQ,CACRC,OAAO,CACPC,UAAU,CACVC,UAAU,CACVC,KAAK,CACLC,IAAI,CACJC,QAAQ,CACRC,QAAQ,CACRC,mBAAmB,CACnBC,mBAAmB,CACnBC,WAAW,CACXC,UAAU,CACVC,eAAe,CACfC,QAAQ,CACRC,mBAAmB,CACnBC,eAAe,CACfC,YAAY,CACZC,eAAe,CACfC,gBAAgB,CAChBC,WAAW,CACXC,oBAAoB,CACpBC,oBAAoB,CACpBC,YAAY,CACZC,cACJ,CAAC,CAAAlI,IAAA,CACG,KAAM,CACFmI,mBAAmB,CACnBC,cAAc,CAChBC,uBAAuB,CACvBC,QAAQ,CACRC,KAAK,CACLC,eAAe,CACfC,YAAY,CACVC,mBAAmB,CACnBC,mBAAmB,CAAEC,sBAAsB,CAC3CC,aAAa,CAAEC,gBAAgB,CAC/BC,cAAc,CAAEC,iBAAiB,CACjCC,cAAc,CAAEC,iBAAiB,CACjCC,cAAc,CACdC,cAAc,CACdC,aAAa,CACbC,gBACJ,CAAC,CAAGhK,cAAc,CAAEiK,KAAkB,EAAKA,KAAK,CAAC,CAEjD,KAAM,CAAAC,QAAQ,CAAGtB,cAAc,SAAdA,cAAc,kBAAAjI,qBAAA,CAAdiI,cAAc,CAAEuB,SAAS,UAAAxJ,qBAAA,kBAAAC,sBAAA,CAAzBD,qBAAA,CAA4BoH,WAAW,CAAG,CAAC,CAAC,UAAAnH,sBAAA,iBAA5CA,sBAAA,CAA8CwJ,QAAQ,CAEvE;AACA;AAEArK,SAAS,CAAC,IAAM,CACZ;AACA,KAAM,CAAAsK,eAAe,CAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC,CAC/D,GAAIF,eAAe,CAAE,CACjBA,eAAe,CAACG,KAAK,CAACC,OAAO,CAAG,MAAM,CACtCJ,eAAe,CAACK,MAAM,CAAC,CAAC,CAC5B,CACA;AACApB,sBAAsB,CAAC,KAAK,CAAC,CAC7BE,gBAAgB,CAAC,KAAK,CAAC,CACvBE,iBAAiB,CAAC,KAAK,CAAC,CACxBE,iBAAiB,CAAC,KAAK,CAAC,CAIxB;AACA,GAAIM,QAAQ,GAAK,cAAc,CAAE,CAC7BZ,sBAAsB,CAAC,IAAI,CAAC,CAC5BqB,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjC,CAAC,IAAM,IAAIT,QAAQ,GAAK,QAAQ,CAAE,KAAAU,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC9B5B,gBAAgB,CAAC,IAAI,CAAC,CACpB;AACA,KAAM,CAAA6B,eAAe,CAAGzC,cAAc,SAAdA,cAAc,kBAAAgC,sBAAA,CAAdhC,cAAc,CAAEuB,SAAS,UAAAS,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4B7C,WAAW,CAAG,CAAC,CAAC,UAAA8C,sBAAA,kBAAAC,sBAAA,CAA5CD,sBAAA,CAA8CS,MAAM,UAAAR,sBAAA,iBAApDA,sBAAA,CAAsDS,QAAQ,CACtF;AACAZ,mBAAmB,CACfU,eAAe,CACfG,QAAQ,CAAC,CAAA5C,cAAc,SAAdA,cAAc,kBAAAmC,sBAAA,CAAdnC,cAAc,CAAEuB,SAAS,UAAAY,sBAAA,kBAAAC,sBAAA,CAAzBD,sBAAA,CAA4BhD,WAAW,CAAG,CAAC,CAAC,UAAAiD,sBAAA,kBAAAC,sBAAA,CAA5CD,sBAAA,CAA8CM,MAAM,UAAAL,sBAAA,iBAApDA,sBAAA,CAAsDQ,OAAO,GAAE,GAAG,CAAC,CAC5ED,QAAQ,CAAC,CAAA5C,cAAc,SAAdA,cAAc,kBAAAsC,sBAAA,CAAdtC,cAAc,CAAEuB,SAAS,UAAAe,sBAAA,kBAAAC,uBAAA,CAAzBD,sBAAA,CAA4BnD,WAAW,CAAG,CAAC,CAAC,UAAAoD,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CG,MAAM,UAAAF,uBAAA,iBAApDA,uBAAA,CAAsDM,UAAU,GAAE,GAAG,CAAC,CAAK,CACxE,CAAC,CACvB,CAAC,IAAM,IAAIxB,QAAQ,GAAK,SAAS,CAAE,CAC/BR,iBAAiB,CAAC,IAAI,CAAC,CACvBiB,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjC,CAAC,IAAM,IAAGT,QAAQ,GAAK,SAAS,CAAE,KAAAyB,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAAAC,uBAAA,CAC9BlC,iBAAiB,CAAC,IAAI,CAAC,CACvBE,cAAc,CAAC,KAAK,CAAC,CACrBa,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAE7B;AACAoB,6BAA6B,CAAC,CAAC,CAE/B;AACA;AAEA;AACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAE,CACrC/B,QAAQ,CACRnC,WAAW,CACXmE,WAAW,CAAEtD,cAAc,SAAdA,cAAc,kBAAA+C,uBAAA,CAAd/C,cAAc,CAAEuB,SAAS,UAAAwB,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B5D,WAAW,CAAG,CAAC,CAAC,UAAA6D,uBAAA,iBAA5CA,uBAAA,CAA8CO,OAAO,CAClEC,WAAW,CAAExD,cAAc,SAAdA,cAAc,kBAAAiD,uBAAA,CAAdjD,cAAc,CAAEuB,SAAS,UAAA0B,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B9D,WAAW,CAAG,CAAC,CAAC,UAAA+D,uBAAA,iBAA5CA,uBAAA,CAA8CO,WAC/D,CAAC,CAAC,CACN,CACJ,CAAC,CAAE,CAACnC,QAAQ,CAAEnC,WAAW,CAAC,CAAC,CAAG;AAE9B;AACA,KAAM,CAAAgE,6BAA6B,CAAGA,CAAA,GAAM,KAAAO,uBAAA,CACxC,KAAM,CAAAC,eAAe,CAAG3D,cAAc,SAAdA,cAAc,kBAAA0D,uBAAA,CAAd1D,cAAc,CAAEuB,SAAS,UAAAmC,uBAAA,iBAAzBA,uBAAA,CAA4BvE,WAAW,CAAG,CAAC,CAAC,CACpE,GAAIwE,eAAe,EAAIA,eAAe,CAACnC,QAAQ,GAAK,SAAS,CAAE,KAAAoC,OAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,gBAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAAC,iBAAA,CAAAC,QAAA,CAAAC,iBAAA,CAC3D,KAAM,CAAAC,YAAY,CAAInB,eAAe,CAASJ,OAAO,EAAI,CAAC,CAAC,CAE3D;AACA;AACA,KAAM,CAAAwB,UAAiB,CAAG,EAAE,CAE5B;AACA,GAAIpB,eAAe,CAACqB,mBAAmB,EAAIrB,eAAe,CAACqB,mBAAmB,CAACC,MAAM,CAAG,CAAC,CAAE,CACvFtB,eAAe,CAACqB,mBAAmB,CAACE,OAAO,CAAEC,SAAc,EAAK,CAC5DJ,UAAU,CAACK,IAAI,CAAC,CACZC,EAAE,CAAEF,SAAS,CAACG,EAAE,EAAIC,MAAM,CAACC,UAAU,CAAC,CAAC,CACvCC,IAAI,CAAE,KAAK,CACXC,WAAW,CAAE,sBAAsB,CACnCC,WAAW,CAAER,SAAS,CAACS,IAAI,EAAI,EAAE,CACjChE,KAAK,CAAE,CACHiE,eAAe,CAAE,aACrB,CACJ,CAAC,CAAC,CACN,CAAC,CAAC,CACN,CAEA;AACA,GAAIlC,eAAe,CAACmC,aAAa,EAAInC,eAAe,CAACmC,aAAa,CAACb,MAAM,CAAG,CAAC,CAAE,CAC3EtB,eAAe,CAACmC,aAAa,CAACZ,OAAO,CAAEa,OAAY,EAAK,KAAAC,qBAAA,CACpDjB,UAAU,CAACK,IAAI,CAAC,CACZC,EAAE,CAAEU,OAAO,CAACT,EAAE,EAAIC,MAAM,CAACC,UAAU,CAAC,CAAC,CACrCC,IAAI,CAAE,QAAQ,CACdQ,OAAO,CAAE,EAAAD,qBAAA,CAAAD,OAAO,CAACG,aAAa,UAAAF,qBAAA,iBAArBA,qBAAA,CAAuBG,GAAG,CAAEC,MAAW,OAAAC,oBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,OAAM,CAClDrB,EAAE,CAAEe,MAAM,CAACO,QAAQ,EAAIpB,MAAM,CAACC,UAAU,CAAC,CAAC,CAC1CoB,IAAI,CAAER,MAAM,CAACS,UAAU,EAAI,QAAQ,CACnCpB,IAAI,CAAEW,MAAM,CAACU,WAAW,EAAI,SAAS,CACrCC,QAAQ,CAAEX,MAAM,CAACY,SAAS,EAAI,QAAQ,CACtCC,OAAO,CAAE,CACLC,KAAK,CAAE,EAAAb,oBAAA,CAAAD,MAAM,CAACe,YAAY,UAAAd,oBAAA,iBAAnBA,oBAAA,CAAqBe,MAAM,GAAI,OAAO,CAC7CC,SAAS,CAAE,EAAAf,qBAAA,CAAAF,MAAM,CAACe,YAAY,UAAAb,qBAAA,iBAAnBA,qBAAA,CAAqBgB,SAAS,GAAI,EAAE,CAC/CC,GAAG,CAAE,EAAAhB,qBAAA,CAAAH,MAAM,CAACe,YAAY,UAAAZ,qBAAA,iBAAnBA,qBAAA,CAAqBiB,WAAW,GAAI,UAC7C,CAAC,CACD5F,KAAK,CAAE,CACHiE,eAAe,CAAE,EAAAW,qBAAA,CAAAJ,MAAM,CAACqB,gBAAgB,UAAAjB,qBAAA,iBAAvBA,qBAAA,CAAyBkB,qBAAqB,GAAI,SAAS,CAC5EC,KAAK,CAAE,EAAAlB,sBAAA,CAAAL,MAAM,CAACqB,gBAAgB,UAAAhB,sBAAA,iBAAvBA,sBAAA,CAAyBmB,eAAe,GAAI,SAAS,CAC5DC,WAAW,CAAE,EAAAnB,sBAAA,CAAAN,MAAM,CAACqB,gBAAgB,UAAAf,sBAAA,iBAAvBA,sBAAA,CAAyBoB,iBAAiB,GAAI,SAC/D,CACJ,CAAC,EAAC,CAAC,GAAI,EAAE,CACTlG,KAAK,CAAE,CACHiE,eAAe,CAAE,aACrB,CACJ,CAAC,CAAC,CACN,CAAC,CAAC,CACN,CAEA;AACA,GAAIlC,eAAe,CAACoE,eAAe,EAAIpE,eAAe,CAACoE,eAAe,CAAC9C,MAAM,CAAG,CAAC,CAAE,CAC/EtB,eAAe,CAACoE,eAAe,CAAC7C,OAAO,CAAE8C,aAAkB,EAAK,KAAAC,qBAAA,CAC5DlD,UAAU,CAACK,IAAI,CAAC,CACZC,EAAE,CAAE2C,aAAa,CAAC1C,EAAE,EAAIC,MAAM,CAACC,UAAU,CAAC,CAAC,CAC3CC,IAAI,CAAE,OAAO,CACbyC,MAAM,CAAE,EAAAD,qBAAA,CAAAD,aAAa,CAACG,WAAW,UAAAF,qBAAA,iBAAzBA,qBAAA,CAA2B9B,GAAG,CAAEiC,KAAU,GAAM,CACpDC,GAAG,CAAED,KAAK,CAACE,GAAG,EAAI,EAAE,CACpBC,OAAO,CAAEH,KAAK,CAACI,OAAO,EAAI,EAC9B,CAAC,CAAC,CAAC,GAAI,EAAE,CACT5G,KAAK,CAAE,CACHiE,eAAe,CAAE,aACrB,CACJ,CAAC,CAAC,CACN,CAAC,CAAC,CACN,CAEA,KAAM,CAAA4C,mBAAmB,CAAG,CACxB1D,UAAU,CAAEA,UAAU,CACtB2D,QAAQ,CAAG/E,eAAe,CAASgF,SAAS,EAAI,QAAQxJ,WAAW,EAAE,CACrEyJ,eAAe,CAAE,EAAE,CACnBtH,QAAQ,CAAE,SAAkB,CAC5BnC,WAAW,CAAEA,WAAW,CACxB0J,MAAM,CAAGlF,eAAe,CAASmF,MAAM,EAAIvD,MAAM,CAACC,UAAU,CAAC,CAAC,CAC9DuD,KAAK,CAAE,CACH7B,KAAK,CAAGvD,eAAe,CAASF,WAAW,EAAI,EAAE,CACjDuF,mBAAmB,CAAGrF,eAAe,CAASqF,mBAAmB,EAAI,EAAE,CACvEjC,QAAQ,CAAE,CAAEkC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAC3B,CAAC,CACD7D,EAAE,CAAEE,MAAM,CAACC,UAAU,CAAC,CAAC,CACvB2D,QAAQ,CAAE,CACNC,SAAS,CAAGtE,YAAY,CAASsE,SAAS,EAAI,GAAG,CACjDC,SAAS,CAAGvE,YAAY,CAASuE,SAAS,EAAI,GAAG,CACjDC,IAAI,CAAGxE,YAAY,CAASwE,IAAI,EAAI,UAAU,CAC9CC,KAAK,CAAGzE,YAAY,CAASyE,KAAK,EAAI,QAAQ,CAC9CC,IAAI,CAAG1E,YAAY,CAAS0E,IAAI,EAAI,IAAI,CACxCC,cAAc,CAAG3E,YAAY,CAAS2E,cAAc,GAAK,KAAK,CAC9DC,4BAA4B,CAAG5E,YAAY,CAAS4E,4BAA4B,GAAK,KAAK,CAC1FC,QAAQ,CAAG7E,YAAY,CAAS6E,QAAQ,EAAI,kBAAkB,CAC9DC,aAAa,CAAG9E,YAAY,CAAS8E,aAAa,EAAI,KAC1D,CAAC,CACDC,MAAM,CAAE,CACJ9C,QAAQ,CAAG,EAAAnD,OAAA,CAACD,eAAe,CAASjB,MAAM,UAAAkB,OAAA,iBAA/BA,OAAA,CAAiCjB,QAAQ,GAAI,MAAO,CAC/DmH,YAAY,CAAG,EAAAjG,QAAA,CAACF,eAAe,CAASjB,MAAM,UAAAmB,QAAA,iBAA/BA,QAAA,CAAiCkG,YAAY,GAAI,KAAM,CACtEC,KAAK,CAAG,EAAAlG,QAAA,CAACH,eAAe,CAASjB,MAAM,UAAAoB,QAAA,iBAA/BA,QAAA,CAAiCmG,KAAK,GAAI,GAAI,CACtDC,KAAK,CAAG,EAAAnG,QAAA,CAACJ,eAAe,CAASjB,MAAM,UAAAqB,QAAA,iBAA/BA,QAAA,CAAiCoG,KAAK,GAAI,GAAI,CACtDC,KAAK,CAAG,EAAApG,QAAA,CAACL,eAAe,CAASjB,MAAM,UAAAsB,QAAA,iBAA/BA,QAAA,CAAiCqG,KAAK,GAAI,OAAQ,CAC1DC,OAAO,CAAG,EAAArG,QAAA,CAACN,eAAe,CAASjB,MAAM,UAAAuB,QAAA,iBAA/BA,QAAA,CAAiCpB,OAAO,GAAI,IAAK,CAC3D0H,YAAY,CAAG,EAAArG,QAAA,CAACP,eAAe,CAASjB,MAAM,UAAAwB,QAAA,iBAA/BA,QAAA,CAAiCsG,YAAY,GAAI,GAAI,CACpEC,UAAU,CAAG,EAAAtG,QAAA,CAACR,eAAe,CAASjB,MAAM,UAAAyB,QAAA,iBAA/BA,QAAA,CAAiCrB,UAAU,GAAI,GAAI,CAChE+E,WAAW,CAAG,EAAAzD,QAAA,CAACT,eAAe,CAASjB,MAAM,UAAA0B,QAAA,iBAA/BA,QAAA,CAAiCsG,WAAW,GAAI,aAAc,CAC5E7E,eAAe,CAAG,EAAAxB,SAAA,CAACV,eAAe,CAASjB,MAAM,UAAA2B,SAAA,iBAA/BA,SAAA,CAAiCsG,eAAe,GAAI,SAC1E,CAAC,CACDC,MAAM,CAAE,CACJC,QAAQ,CAAE,CACNlE,QAAQ,CAAG,EAAArC,OAAA,CAACX,eAAe,CAASmH,MAAM,UAAAxG,OAAA,kBAAAC,gBAAA,CAA/BD,OAAA,CAAiCyG,QAAQ,UAAAxG,gBAAA,iBAAzCA,gBAAA,CAA2CoC,QAAQ,GAAI,EAAG,CACrElD,WAAW,CAAG,EAAAe,QAAA,CAACb,eAAe,CAASmH,MAAM,UAAAtG,QAAA,kBAAAC,iBAAA,CAA/BD,QAAA,CAAiCuG,QAAQ,UAAAtG,iBAAA,iBAAzCA,iBAAA,CAA2ChB,WAAW,GAAI,EAAG,CAC3EuH,QAAQ,CAAG,EAAAtG,QAAA,CAACf,eAAe,CAASmH,MAAM,UAAApG,QAAA,kBAAAC,iBAAA,CAA/BD,QAAA,CAAiCqG,QAAQ,UAAApG,iBAAA,iBAAzCA,iBAAA,CAA2CqG,QAAQ,GAAI,EAAG,CACrEnE,UAAU,CAAG,EAAAjC,QAAA,CAACjB,eAAe,CAASmH,MAAM,UAAAlG,QAAA,kBAAAC,iBAAA,CAA/BD,QAAA,CAAiCmG,QAAQ,UAAAlG,iBAAA,iBAAzCA,iBAAA,CAA2CgC,UAAU,GAAI,EAC1E,CAAC,CACDoE,OAAO,CAAE,CACL3L,QAAQ,CAAE,EAAE,CACZ4L,SAAS,CAAE,KAAK,CAChBC,sBAAsB,CAAE,CAAC,CACzBC,aAAa,CAAE,qBACnB,CACJ,CACJ,CAAC,CAED;AACA,KAAM,CAAAC,KAAK,CAAGjU,cAAc,CAACkU,QAAQ,CAAC,CAAC,CACvC,KAAM,CAAAC,eAAe,CAAG,CAAC,GAAGF,KAAK,CAACG,oBAAoB,CAAC,CAEvD;AACA,MAAOD,eAAe,CAACtG,MAAM,CAAG9F,WAAW,CAAE,CACzCoM,eAAe,CAACnG,IAAI,CAAC,CACjBL,UAAU,CAAE,EAAE,CACd2D,QAAQ,CAAE,QAAQ6C,eAAe,CAACtG,MAAM,CAAG,CAAC,EAAE,CAC9C2D,eAAe,CAAE,EAAE,CACnBtH,QAAQ,CAAE,SAAS,CACnBnC,WAAW,CAAEoM,eAAe,CAACtG,MAAM,CAAG,CAAC,CACvC4D,MAAM,CAAEtD,MAAM,CAACC,UAAU,CAAC,CAAC,CAC3BuD,KAAK,CAAE,CAAE7B,KAAK,CAAE,EAAE,CAAE8B,mBAAmB,CAAE,EAAE,CAAEjC,QAAQ,CAAE,CAAEkC,CAAC,CAAE,CAAC,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAC,CACvE7D,EAAE,CAAEE,MAAM,CAACC,UAAU,CAAC,CAAC,CACvB2D,QAAQ,CAAE,CACNC,SAAS,CAAE,GAAG,CACdC,SAAS,CAAE,GAAG,CACdC,IAAI,CAAE,UAAU,CAChBC,KAAK,CAAE,QAAQ,CACfC,IAAI,CAAE,IAAI,CACVC,cAAc,CAAE,IAAI,CACpBC,4BAA4B,CAAE,IAAI,CAClCC,QAAQ,CAAE,kBAAkB,CAC5BC,aAAa,CAAE,KACnB,CAAC,CACDC,MAAM,CAAE,CACJ9C,QAAQ,CAAE,MAAM,CAChB+C,YAAY,CAAE,KAAK,CACnBE,KAAK,CAAE,GAAG,CACVE,KAAK,CAAE,GAAG,CACVE,KAAK,CAAE,OAAO,CACdE,OAAO,CAAE,IAAI,CACbC,YAAY,CAAE,GAAG,CACjBE,UAAU,CAAE,GAAG,CACf5C,WAAW,CAAE,aAAa,CAC1BhC,eAAe,CAAE,SACrB,CAAC,CACD+E,MAAM,CAAE,CACJC,QAAQ,CAAE,CACNlE,QAAQ,CAAE,EAAE,CACZlD,WAAW,CAAE,EAAE,CACfuH,QAAQ,CAAE,EAAE,CACZnE,UAAU,CAAE,EAChB,CAAC,CACDoE,OAAO,CAAE,CACL3L,QAAQ,CAAE,EAAE,CACZ4L,SAAS,CAAE,KAAK,CAChBC,sBAAsB,CAAE,CAAC,CACzBC,aAAa,CAAE,qBACnB,CACJ,CACJ,CAAC,CAAC,CACN,CAEA;AACAG,eAAe,CAACpM,WAAW,CAAG,CAAC,CAAC,CAAGsJ,mBAA0B,CAE7D;AACArR,cAAc,CAACqU,QAAQ,CAAC,CACpBD,oBAAoB,CAAED,eAAe,CACrCG,eAAe,CAAE,IACrB,CAAC,CAAC,CACF;AACAtU,cAAc,CAACkU,QAAQ,CAAC,CAAC,CAACnL,uBAAuB,CAAC,SAAS,CAAE,IAAI,CAAC,CACtE,CACJ,CAAC,CAED,KAAM,CAAA4B,mBAAmB,CAAG,QAAAA,CAACgF,QAAc,CAAqD,IAApD,CAAAuD,OAAY,CAAAqB,SAAA,CAAA1G,MAAA,IAAA0G,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,CAAC,IAAC,CAAAE,MAAY,CAAAF,SAAA,CAAA1G,MAAA,IAAA0G,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAE,CAAC,IAAE,CAAAG,GAAQ,CAAAH,SAAA,CAAA1G,MAAA,IAAA0G,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,EAAE,CACvF,KAAM,CAAAI,UAAU,CAAGrK,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC,CAChE,GAAIoK,UAAU,CAAE,CACZrK,QAAQ,CAACsK,IAAI,CAACC,WAAW,CAACF,UAAU,CAAC,CACzC,CACA,GAAI,CAAAG,QAAQ,CAAGxK,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB,CAC5E,KAAM,CAAAwK,WAAW,CAAGzK,QAAQ,CAAC0K,IAAI,CAEjCD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC,CAE/C,GAAI,CAACJ,QAAQ,CAAE,CACXA,QAAQ,CAAGxK,QAAQ,CAAC6K,aAAa,CAAC,OAAO,CAAC,CAC1CL,QAAQ,CAAC7G,EAAE,CAAG,oBAAoB,CAElC,GAAI,CAAAmH,MAAM,CAAG;AACjC;AACA,uCAAuCV,GAAG;AAC1C;AACA;AACA;AACA,qBAAqB,CACL;AACA,GAAI/E,QAAQ,GAAK,WAAW,CACxB,CACI;AACpB,KAAM,CAAA0F,MAAM,CAAG/K,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC,CACrD,KAAM,CAAA+K,YAAY,CAAG,CAAAD,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEE,YAAY,GAAI,EAAE,CAC/C;AACA,KAAM,CAAAC,YAAY,CAAGhK,QAAQ,CAAC0H,OAAO,CAACuC,QAAQ,CAAC,CAAC,CAAC,EAAI,CAAC,CACtD,KAAM,CAAAC,WAAW,CAAGlK,QAAQ,CAACiJ,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC,EAAI,CAAC,CACpD;AACA,KAAM,CAAAE,gBAAgB,CAAIH,YAAY,CAAG,CAAC,CAAKE,WAAW,CAAG,CAAE,CAC/D;AACA,KAAM,CAAAE,MAAM,CAAGP,MAAM,CAAGC,YAAY,CAAGA,YAAY,CAAGK,gBAAgB,CAElDP,MAAM,CAAG;AACjC;AACA,2CAA2CQ,MAAM;AACjD;AACA;AACA;AACA,aAAaA,MAAM;AACnB;AACA;AACA;AACA,qBAAqBA,MAAM;AAC3B;AACA,yBAAyB,CAGL,CAEAd,QAAQ,CAACe,SAAS,CAAGT,MAAM,CAC3B9K,QAAQ,CAACsK,IAAI,CAACkB,WAAW,CAAChB,QAAQ,CAAC,CACvC,CACR,CAAC,CAED,mBACItU,KAAA,QAAAuV,QAAA,EACKpN,YAAY,EAAI,CAACY,aAAa,eAC3BjJ,IAAA,QAAKkK,KAAK,CAAE,CACRmF,QAAQ,CAAE,OAAO,CACjB+E,GAAG,CAAE,CAAC,CACNsB,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTzH,eAAe,CAAE,oBAAoB,CACrC0H,MAAM,CAAE,GAAG,CACXC,aAAa,CAAE,MACnB,CAAE,CAAE,CACP,CACV7M,aAAa,eACHjJ,IAAA,CAACF,iBAAiB,EAClBiW,iBAAiB,CAAC,EAAE,CACpBC,oBAAoB,CAAC,EAAE,CACvBC,gBAAgB,CAAE3N,cAAe,CACjC4N,mBAAmB,CAAC,EAAE,CACtBlP,OAAO,CAAER,sBAAuB,CAChCC,WAAW,CAAEA,WACd;AAAA,CACAC,SAAS,CAAE,CAAA4B,cAAc,SAAdA,cAAc,kBAAA/H,uBAAA,CAAd+H,cAAc,CAAEuB,SAAS,UAAAtJ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BkH,WAAW,CAAG,CAAC,CAAC,UAAAjH,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CwK,MAAM,UAAAvK,uBAAA,iBAApDA,uBAAA,CAAsDwK,QAAQ,GAAI,WAAY,CAAiBtE,QAAQ,CAAEA,QAAS,CAC5HC,YAAY,CAAEA,YAAa,CACpBC,WAAW,CAAEA,WAAY,CACzBa,UAAU,CAAE,CAAAY,cAAc,SAAdA,cAAc,kBAAA5H,uBAAA,CAAd4H,cAAc,CAAEuB,SAAS,UAAAnJ,uBAAA,iBAAzBA,uBAAA,CAA2B6M,MAAM,GAAI,CAAE,CACnD9D,aAAa,CAAEA,aAAc,CAC7BnB,cAAc,CAAEA,cAAe,CAC/BV,QAAQ,CAAIH,WAAW,EAAK,CAAAa,cAAc,SAAdA,cAAc,kBAAA3H,uBAAA,CAAd2H,cAAc,CAAEuB,SAAS,UAAAlJ,uBAAA,iBAAzBA,uBAAA,CAA2B4M,MAAM,GAAI,CAAC,CAAC,CAAI,GAAI,CACrF,CACD,CACIxE,mBAAmB,eAChB/I,IAAA,CAACL,iBAAiB,EACd4G,gBAAgB,CAAEA,gBAAiB,CACnCC,sBAAsB,CAAEA,sBAAuB,CAC/CM,SAAS,CAAEA,SAAU,CACrBC,QAAQ,CAAEiD,QAAQ,CAAC0K,IAAK,CACxB1N,OAAO,CAAEA,OAAQ,CACjBC,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBC,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBC,KAAK,CAAE,CAAAmB,cAAc,SAAdA,cAAc,kBAAA1H,uBAAA,CAAd0H,cAAc,CAAEuB,SAAS,UAAAjJ,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B6G,WAAW,CAAC,UAAA5G,uBAAA,iBAAxCA,uBAAA,CAA0CoQ,SAAS,GAAI,EAAG,CACjE7J,IAAI,CAAE,CAAAkB,cAAc,SAAdA,cAAc,kBAAAxH,uBAAA,CAAdwH,cAAc,CAAEuB,SAAS,UAAA/I,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B2G,WAAW,CAAC,UAAA1G,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CuM,mBAAmB,UAAAtM,uBAAA,kBAAAC,uBAAA,CAA7DD,uBAAA,CAAgE,CAAC,CAAC,UAAAC,uBAAA,iBAAlEA,uBAAA,CAAoEiN,IAAI,GAAI,EAAG,CACrF7G,QAAQ,CAAE,CAAAiB,cAAc,SAAdA,cAAc,kBAAApH,uBAAA,CAAdoH,cAAc,CAAEuB,SAAS,UAAA3I,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BuG,WAAW,CAAC,UAAAtG,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CkP,eAAe,UAAAjP,uBAAA,kBAAAC,uBAAA,CAAzDD,uBAAA,CAA4D,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9DD,uBAAA,CAAgEoP,WAAW,UAAAnP,uBAAA,kBAAAC,uBAAA,CAA3ED,uBAAA,CAA8E,CAAC,CAAC,UAAAC,uBAAA,iBAAhFA,uBAAA,CAAkFqP,GAAG,GAAI,EAAG,CACtGrJ,mBAAmB,CAAC,MAAM,CAC1BC,mBAAmB,CAAC,MAAM,CAC1BC,WAAW,CAAEA,WAAY,CACzBC,UAAU,CAAE,CAAAY,cAAc,SAAdA,cAAc,kBAAA9G,uBAAA,CAAd8G,cAAc,CAAEuB,SAAS,UAAArI,uBAAA,iBAAzBA,uBAAA,CAA2B+L,MAAM,GAAI,CAAE,CACnD5F,eAAe,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC1BC,QAAQ,CAAIH,WAAW,EAAK,CAAAa,cAAc,SAAdA,cAAc,kBAAA7G,uBAAA,CAAd6G,cAAc,CAAEuB,SAAS,UAAApI,uBAAA,iBAAzBA,uBAAA,CAA2B8L,MAAM,GAAI,CAAC,CAAC,CAAI,GAAI,CAC3E1F,mBAAmB,CAAE,CAAAS,cAAc,SAAdA,cAAc,kBAAA5G,uBAAA,CAAd4G,cAAc,CAAEuB,SAAS,UAAAnI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B+F,WAAW,CAAG,CAAC,CAAC,UAAA9F,uBAAA,iBAA5CA,uBAAA,CAA8C2L,mBAAmB,GAAI,EAAG,CAC7FxF,eAAe,CAAE,CAAAQ,cAAc,SAAdA,cAAc,kBAAA1G,uBAAA,CAAd0G,cAAc,CAAEuB,SAAS,UAAAjI,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B6F,WAAW,CAAG,CAAC,CAAC,UAAA5F,uBAAA,iBAA5CA,uBAAA,CAA8CwO,eAAe,GAAI,EAAG,CACrFtI,YAAY,CACR,CAAAO,cAAc,SAAdA,cAAc,kBAAAxG,uBAAA,CAAdwG,cAAc,CAAEuB,SAAS,UAAA/H,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B2F,WAAW,CAAG,CAAC,CAAC,UAAA1F,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CqM,aAAa,UAAApM,uBAAA,iBAA3DA,uBAAA,CAA6DmU,OAAO,CAAC9H,OAAO,EACxEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAACC,MAAM,GAAK,CACjC,GAAGA,MAAM,CACT0H,WAAW,CAAE/H,OAAO,CAACT,EACzB,CAAC,CAAC,CACN,CAAC,GAAI,EACR,CACD5F,eAAe,CAAE,CAAAM,cAAc,SAAdA,cAAc,kBAAArG,uBAAA,CAAdqG,cAAc,CAAEuB,SAAS,UAAA5H,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BwF,WAAW,CAAG,CAAC,CAAC,UAAAvF,uBAAA,iBAA5CA,uBAAA,CAA8CmU,KAAK,GAAI,CAAC,CAAE,CAC3EpO,gBAAgB,CAAE,CAAAK,cAAc,SAAdA,cAAc,kBAAAnG,uBAAA,CAAdmG,cAAc,CAAEuB,SAAS,UAAA1H,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BsF,WAAW,CAAG,CAAC,CAAC,UAAArF,uBAAA,iBAA5CA,uBAAA,CAA8C4I,MAAM,GAAI,CAAC,CAAE,CAC7E9C,WAAW,CAAE,CAAAI,cAAc,SAAdA,cAAc,kBAAAjG,uBAAA,CAAdiG,cAAc,CAAEuB,SAAS,UAAAxH,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BoF,WAAW,CAAG,CAAC,CAAC,UAAAnF,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CgL,mBAAmB,UAAA/K,uBAAA,kBAAAC,uBAAA,CAAjED,uBAAA,CAAoE,CAAC,CAAC,UAAAC,uBAAA,iBAAtEA,uBAAA,CAAwE0L,IAAI,GAAI,EAAG,CAChG7F,YAAY,CAAEA,YAAa,CAC3BC,cAAc,CAAEA,cAAe,CAC/B7B,WAAW,CAAEA,WAAY,CACzBC,SAAS,CAAEA,SAAU,CACrBC,QAAQ,CAAEA,QAAS,CACnBC,YAAY,CAAEA,YAAa,CAC3BC,WAAW,CAAEA,WAAY,CACzB4C,aAAa,CAAEA,aAAc,CAChC,CACJ,CAEAJ,cAAc,eACZrJ,IAAA,CAACJ,cAAc,EACf0W,kBAAkB,CAAE,IAAK,CACzBC,kBAAkB,CAAE,IAAK,CACzBC,kBAAkB,CAAEA,CAAA,GAAM,CACtB;AACA9K,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC9C,CAAE,CACF8K,kBAAkB,CAAEA,CAAA,GAAM,CACtB;AACA/K,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC,CAC1CnC,cAAc,CAAC,IAAI,CAAC,CACxB,CAAE,CACF1C,SAAS,CAAEA,SAAU,CACrBE,OAAO,CAAEA,OAAQ,CACjBG,KAAK,CAAE,CAAAmB,cAAc,SAAdA,cAAc,kBAAA7F,uBAAA,CAAd6F,cAAc,CAAEuB,SAAS,UAAApH,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BgF,WAAW,CAAC,CAAC,CAAC,UAAA/E,uBAAA,iBAA1CA,uBAAA,CAA4CoH,QAAQ,GAAI,EAAG,CAClE1C,IAAI,CAAE,CAAAkB,cAAc,SAAdA,cAAc,kBAAA3F,uBAAA,CAAd2F,cAAc,CAAEuB,SAAS,UAAAlH,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B8E,WAAW,CAAC,UAAA7E,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0C0K,mBAAmB,UAAAzK,uBAAA,kBAAAC,uBAAA,CAA7DD,uBAAA,CAAgE,CAAC,CAAC,UAAAC,uBAAA,iBAAlEA,uBAAA,CAAoEoL,IAAI,GAAI,EAAG,CACrF7G,QAAQ,CAAE,CAAAiB,cAAc,SAAdA,cAAc,kBAAAvF,uBAAA,CAAduF,cAAc,CAAEuB,SAAS,UAAA9G,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B0E,WAAW,CAAC,UAAAzE,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CqN,eAAe,UAAApN,uBAAA,kBAAAC,uBAAA,CAAzDD,uBAAA,CAA4D,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9DD,uBAAA,CAAgEuN,WAAW,UAAAtN,uBAAA,kBAAAC,uBAAA,CAA3ED,uBAAA,CAA8E,CAAC,CAAC,UAAAC,uBAAA,iBAAhFA,uBAAA,CAAkFwN,GAAG,GAAI,EAAG,CACtG3J,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBC,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBO,WAAW,CAAEA,WAAa;AAAA,CAC1BC,UAAU,CAAE,CAAAY,cAAc,SAAdA,cAAc,kBAAAjF,uBAAA,CAAdiF,cAAc,CAAEuB,SAAS,UAAAxG,uBAAA,iBAAzBA,uBAAA,CAA2BkK,MAAM,GAAI,CAAE,CACnD5F,eAAe,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC1BC,QAAQ,CAAIH,WAAW,EAAK,CAAAa,cAAc,SAAdA,cAAc,kBAAAhF,uBAAA,CAAdgF,cAAc,CAAEuB,SAAS,UAAAvG,uBAAA,iBAAzBA,uBAAA,CAA2BiK,MAAM,GAAI,CAAC,CAAC,CAAI,GAAI,CAC3E1F,mBAAmB,CAAE,CAAAS,cAAc,SAAdA,cAAc,kBAAA/E,uBAAA,CAAd+E,cAAc,CAAEuB,SAAS,UAAAtG,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BkE,WAAW,CAAG,CAAC,CAAC,UAAAjE,uBAAA,iBAA5CA,uBAAA,CAA8C8J,mBAAmB,GAAI,EAAG,CAC7FxF,eAAe,CAAE,CAAAQ,cAAc,SAAdA,cAAc,kBAAA7E,uBAAA,CAAd6E,cAAc,CAAEuB,SAAS,UAAApG,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BgE,WAAW,CAAG,CAAC,CAAC,UAAA/D,uBAAA,iBAA5CA,uBAAA,CAA8C2M,eAAe,GAAI,EAAG,CACrFtI,YAAY,CACR,CAAAO,cAAc,SAAdA,cAAc,kBAAA3E,uBAAA,CAAd2E,cAAc,CAAEuB,SAAS,UAAAlG,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B8D,WAAW,CAAG,CAAC,CAAC,UAAA7D,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CwK,aAAa,UAAAvK,uBAAA,kBAAAC,uBAAA,CAA3DD,uBAAA,CAA6D4K,GAAG,CAAEJ,OAAO,EACrEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAAEC,MAAM,GAAM,CACnC,GAAGA,MAAM,CACT0H,WAAW,CAAE/H,OAAO,CAACT,EAAI;AAC7B,CAAC,CAAC,CACN,CAAC,UAAA9J,uBAAA,iBALDA,uBAAA,CAKG4S,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EACrD,CACD5O,eAAe,CAAE,CAAAM,cAAc,SAAdA,cAAc,kBAAAvE,uBAAA,CAAduE,cAAc,CAAEuB,SAAS,UAAA9F,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B0D,WAAW,CAAG,CAAC,CAAC,UAAAzD,uBAAA,iBAA5CA,uBAAA,CAA8CqS,KAAK,GAAI,CAAC,CAAE,CAC3EpO,gBAAgB,CAAE,CAAAK,cAAc,SAAdA,cAAc,kBAAArE,uBAAA,CAAdqE,cAAc,CAAEuB,SAAS,UAAA5F,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BwD,WAAW,CAAG,CAAC,CAAC,UAAAvD,uBAAA,iBAA5CA,uBAAA,CAA8C8G,MAAM,GAAI,CAAC,CAAE,CAC7E9C,WAAW,CAAE,CAAAI,cAAc,SAAdA,cAAc,kBAAAnE,uBAAA,CAAdmE,cAAc,CAAEuB,SAAS,UAAA1F,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BsD,WAAW,CAAG,CAAC,CAAC,UAAArD,uBAAA,kBAAAC,uBAAA,CAA5CD,uBAAA,CAA8CkJ,mBAAmB,UAAAjJ,uBAAA,kBAAAC,uBAAA,CAAjED,uBAAA,CAAoE,CAAC,CAAC,UAAAC,uBAAA,iBAAtEA,uBAAA,CAAwE4J,IAAI,GAAI,EAAG,CAChG7F,YAAY,CAAEA,YAAa,CAC3BC,cAAc,CAAEA,cAAe,CAC/BwO,iBAAiB,CAAE,CAAAxO,cAAc,SAAdA,cAAc,kBAAA/D,uBAAA,CAAd+D,cAAc,CAAEuB,SAAS,UAAAtF,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BkD,WAAW,CAAG,CAAC,CAAC,UAAAjD,uBAAA,iBAA5CA,uBAAA,CAA8CqH,OAAO,GAAI,CAAC,CAAE,CAC/E9E,QAAQ,CAAEiD,QAAQ,CAAC0K,IAAK,CAE3B,CACC,CAGAvL,cAAc,eAGXnJ,IAAA,CAACH,mBAAmB,EACpCiH,SAAS,CAAEA,SAAU,CACrBE,OAAO,CAAEA,OAAQ,CACjBG,KAAK,CAAE,CAAAmB,cAAc,SAAdA,cAAc,kBAAA7D,uBAAA,CAAd6D,cAAc,CAAEuB,SAAS,UAAApF,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BgD,WAAW,CAAC,UAAA/C,uBAAA,iBAAxCA,uBAAA,CAA0CuM,SAAS,GAAI,EAAG,CACjE7J,IAAI,CAAE,CAAAkB,cAAc,SAAdA,cAAc,kBAAA3D,uBAAA,CAAd2D,cAAc,CAAEuB,SAAS,UAAAlF,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B8C,WAAW,CAAC,UAAA7C,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0C0I,mBAAmB,UAAAzI,uBAAA,kBAAAC,uBAAA,CAA7DD,uBAAA,CAAgE,CAAC,CAAC,UAAAC,uBAAA,iBAAlEA,uBAAA,CAAoEoJ,IAAI,GAAI,EAAG,CACrF7G,QAAQ,CAAE,CAAAiB,cAAc,SAAdA,cAAc,kBAAAvD,uBAAA,CAAduD,cAAc,CAAEuB,SAAS,UAAA9E,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4B0C,WAAW,CAAC,UAAAzC,uBAAA,kBAAAC,uBAAA,CAAxCD,uBAAA,CAA0CqL,eAAe,UAAApL,uBAAA,kBAAAC,uBAAA,CAAzDD,uBAAA,CAA4D,CAAC,CAAC,UAAAC,uBAAA,kBAAAC,uBAAA,CAA9DD,uBAAA,CAAgEuL,WAAW,UAAAtL,uBAAA,kBAAAC,uBAAA,CAA3ED,uBAAA,CAA8E,CAAC,CAAC,UAAAC,uBAAA,iBAAhFA,uBAAA,CAAkFwL,GAAG,GAAI,EAAG,CACtG3J,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBC,UAAU,CAAEA,CAAA,GAAM,CAAC,CAAE,CACrBO,WAAW,CAAEA,WAAa;AAAA,CAC1BC,UAAU,CAAE,CAAAY,cAAc,SAAdA,cAAc,kBAAAjD,uBAAA,CAAdiD,cAAc,CAAEuB,SAAS,UAAAxE,uBAAA,iBAAzBA,uBAAA,CAA2BkI,MAAM,GAAI,CAAE,CACnD5F,eAAe,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC1BC,QAAQ,CAAG,CAACH,WAAW,CAAG,CAAC,GAAK,CAAAa,cAAc,SAAdA,cAAc,kBAAAhD,uBAAA,CAAdgD,cAAc,CAAEuB,SAAS,UAAAvE,uBAAA,iBAAzBA,uBAAA,CAA2BiI,MAAM,GAAI,CAAC,CAAC,CAAI,GAAI,CAC/E1F,mBAAmB,CAAE,CAAAS,cAAc,SAAdA,cAAc,kBAAA/C,uBAAA,CAAd+C,cAAc,CAAEuB,SAAS,UAAAtE,uBAAA,kBAAAC,uBAAA,CAAzBD,uBAAA,CAA4BkC,WAAW,CAAG,CAAC,CAAC,UAAAjC,uBAAA,iBAA5CA,uBAAA,CAA8C8H,mBAAmB,GAAI,EAAG,CAC7FxF,eAAe,CAAE,CAAAQ,cAAc,SAAdA,cAAc,kBAAA7C,uBAAA,CAAd6C,cAAc,CAAEuB,SAAS,UAAApE,uBAAA,kBAAAC,wBAAA,CAAzBD,uBAAA,CAA4BgC,WAAW,CAAG,CAAC,CAAC,UAAA/B,wBAAA,iBAA5CA,wBAAA,CAA8C2K,eAAe,GAAI,EAAG,CACrFtI,YAAY,CACX,CAAAO,cAAc,SAAdA,cAAc,kBAAA3C,wBAAA,CAAd2C,cAAc,CAAEuB,SAAS,UAAAlE,wBAAA,kBAAAC,wBAAA,CAAzBD,wBAAA,CAA4B8B,WAAW,CAAG,CAAC,CAAC,UAAA7B,wBAAA,kBAAAC,wBAAA,CAA5CD,wBAAA,CAA8CwI,aAAa,UAAAvI,wBAAA,kBAAAC,wBAAA,CAA3DD,wBAAA,CAA6D4I,GAAG,CAAEJ,OAAO,EACxEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAAEC,MAAM,GAAM,CACtC,GAAGA,MAAM,CACT0H,WAAW,CAAE/H,OAAO,CAACT,EAAI;AAC1B,CAAC,CAAC,CACH,CAAC,UAAA9H,wBAAA,iBALDA,wBAAA,CAKG4Q,MAAM,CAAC,CAACC,GAAG,CAAEC,IAAI,GAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,CAAE,EAAE,CAAC,GAAI,EAClD,CACD5O,eAAe,CAAE,CAAAM,cAAc,SAAdA,cAAc,kBAAAvC,wBAAA,CAAduC,cAAc,CAAEuB,SAAS,UAAA9D,wBAAA,kBAAAC,wBAAA,CAAzBD,wBAAA,CAA4B0B,WAAW,CAAG,CAAC,CAAC,UAAAzB,wBAAA,iBAA5CA,wBAAA,CAA8CqQ,KAAK,GAAI,CAAC,CAAE,CAC3EpO,gBAAgB,CAAE,CAAAK,cAAc,SAAdA,cAAc,kBAAArC,wBAAA,CAAdqC,cAAc,CAAEuB,SAAS,UAAA5D,wBAAA,kBAAAC,wBAAA,CAAzBD,wBAAA,CAA4BwB,WAAW,CAAG,CAAC,CAAC,UAAAvB,wBAAA,iBAA5CA,wBAAA,CAA8C8E,MAAM,GAAI,CAAC,CAAE,CAC7E9C,WAAW,CAAE,CAAAI,cAAc,SAAdA,cAAc,kBAAAnC,wBAAA,CAAdmC,cAAc,CAAEuB,SAAS,UAAA1D,wBAAA,kBAAAC,wBAAA,CAAzBD,wBAAA,CAA4BsB,WAAW,CAAG,CAAC,CAAC,UAAArB,wBAAA,kBAAAC,wBAAA,CAA5CD,wBAAA,CAA8CkH,mBAAmB,UAAAjH,wBAAA,kBAAAC,wBAAA,CAAjED,wBAAA,CAAoE,CAAC,CAAC,UAAAC,wBAAA,iBAAtEA,wBAAA,CAAwE4H,IAAI,GAAI,EAAG,CAChG7F,YAAY,CAAEA,YAAa,CAC3BC,cAAc,CAAEA,cAChB;AAAA,CACAvB,QAAQ,CAAEiD,QAAQ,CAAC0K,IAAK,CACxBnN,mBAAmB,CAAE,EAAG,CACxBC,mBAAmB,CAAE,EAAG,CACxB,CAEH,EAIgB,CAAC,CAEd,CAAC,CAED,cAAe,CAAArH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}