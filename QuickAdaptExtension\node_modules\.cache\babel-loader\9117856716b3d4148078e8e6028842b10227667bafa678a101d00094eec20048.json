{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\tours\\\\tourPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport useDrawerStore from \"../../store/drawerStore\";\nimport AnnouncementPopup from \"../GuidesPreview/AnnouncementPreview\";\nimport HotspotPreview from \"../GuidesPreview/HotspotPreview\";\nimport TooltiplastUserview from \"../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview\";\nimport BannerStepPreview from \"./BannerStepPreview\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TourPreview = ({\n  selectedTemplate,\n  handlecloseBannerPopup,\n  backgroundC,\n  Bposition,\n  bpadding,\n  Bbordercolor,\n  BborderSize,\n  guideStep,\n  anchorEl,\n  onClose,\n  onPrevious,\n  onContinue,\n  title,\n  text,\n  imageUrl,\n  videoUrl,\n  previousButtonLabel,\n  continueButtonLabel,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _savedGuideData$Guide17, _savedGuideData$Guide18, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22, _savedGuideData$Guide23, _savedGuideData$Guide24, _savedGuideData$Guide25, _savedGuideData$Guide26, _savedGuideData$Guide27, _savedGuideData$Guide28, _savedGuideData$Guide29, _savedGuideData$Guide30, _savedGuideData$Guide31, _savedGuideData$Guide32, _savedGuideData$Guide33, _savedGuideData$Guide34, _savedGuideData$Guide35, _savedGuideData$Guide36, _savedGuideData$Guide37, _savedGuideData$Guide38, _savedGuideData$Guide39, _savedGuideData$Guide40, _savedGuideData$Guide41, _savedGuideData$Guide42, _savedGuideData$Guide43, _savedGuideData$Guide44, _savedGuideData$Guide45, _savedGuideData$Guide46, _savedGuideData$Guide47, _savedGuideData$Guide48, _savedGuideData$Guide49, _savedGuideData$Guide50, _savedGuideData$Guide51, _savedGuideData$Guide52, _savedGuideData$Guide53, _savedGuideData$Guide54, _savedGuideData$Guide55, _savedGuideData$Guide56, _savedGuideData$Guide57, _savedGuideData$Guide58, _savedGuideData$Guide59, _savedGuideData$Guide60, _savedGuideData$Guide61, _savedGuideData$Guide62, _savedGuideData$Guide63, _savedGuideData$Guide64, _savedGuideData$Guide65, _savedGuideData$Guide66, _savedGuideData$Guide67, _savedGuideData$Guide68, _savedGuideData$Guide69, _savedGuideData$Guide70, _savedGuideData$Guide71, _savedGuideData$Guide72, _savedGuideData$Guide73, _savedGuideData$Guide74, _savedGuideData$Guide75, _savedGuideData$Guide76, _savedGuideData$Guide77, _savedGuideData$Guide78, _savedGuideData$Guide79, _savedGuideData$Guide80, _savedGuideData$Guide81, _savedGuideData$Guide82, _savedGuideData$Guide83, _savedGuideData$Guide84, _savedGuideData$Guide85, _savedGuideData$Guide86, _savedGuideData$Guide87, _savedGuideData$Guide88, _savedGuideData$Guide89, _savedGuideData$Guide90, _savedGuideData$Guide91, _savedGuideData$Guide92, _savedGuideData$Guide93, _savedGuideData$Guide94, _savedGuideData$Guide95, _savedGuideData$Guide96, _savedGuideData$Guide97, _savedGuideData$Guide98, _savedGuideData$Guide99, _savedGuideData$Guide100, _savedGuideData$Guide101, _savedGuideData$Guide102, _savedGuideData$Guide103, _savedGuideData$Guide104, _savedGuideData$Guide105, _savedGuideData$Guide106, _savedGuideData$Guide107, _savedGuideData$Guide108, _savedGuideData$Guide109, _savedGuideData$Guide110, _savedGuideData$Guide111, _savedGuideData$Guide112;\n  const {\n    setSelectedTemplate,\n    setBannerPopup,\n    setSelectedTemplateTour,\n    setSteps,\n    steps,\n    setTooltipCount,\n    tooltipCount,\n    HotspotGuideDetails,\n    announcementPreview,\n    setAnnouncementPreview,\n    bannerPreview,\n    setBannerPreview,\n    tooltipPreview,\n    setTooltipPreview,\n    hotspotPreview,\n    setHotspotPreview,\n    setCurrentStep,\n    setOpenTooltip,\n    ProgressColor,\n    setProgressColor\n  } = useDrawerStore(state => state);\n  const stepType = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[currentStep - 1]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.StepType;\n\n  // Note: AI data synchronization is now handled in the store's setCurrentStep function\n  // to avoid infinite loops and ensure proper timing\n\n  useEffect(() => {\n    // Clean up any existing hotspot before setting new preview\n    const existingHotspot = document.getElementById(\"hotspotBlink\");\n    if (existingHotspot) {\n      existingHotspot.style.display = \"none\";\n      existingHotspot.remove();\n    }\n    // Reset all previews before setting the specific one\n    setAnnouncementPreview(false);\n    setBannerPreview(false);\n    setTooltipPreview(false);\n    setHotspotPreview(false);\n\n    // Set the correct preview based on stepType\n    if (stepType === \"Announcement\") {\n      setAnnouncementPreview(true);\n      resetHeightofBanner(\"\", 0, 0, 0);\n    } else if (stepType === \"Banner\") {\n      var _savedGuideData$Guide3, _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11;\n      setBannerPreview(true);\n      // Get the current Canvas position from the step\n      const currentPosition = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3[currentStep - 1]) === null || _savedGuideData$Guide4 === void 0 ? void 0 : (_savedGuideData$Guide5 = _savedGuideData$Guide4.Canvas) === null || _savedGuideData$Guide5 === void 0 ? void 0 : _savedGuideData$Guide5.Position;\n      // Use the current position value to properly apply Push Down effect\n      resetHeightofBanner(currentPosition, parseInt((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide6 = savedGuideData.GuideStep) === null || _savedGuideData$Guide6 === void 0 ? void 0 : (_savedGuideData$Guide7 = _savedGuideData$Guide6[currentStep - 1]) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7.Canvas) === null || _savedGuideData$Guide8 === void 0 ? void 0 : _savedGuideData$Guide8.Padding) || \"0\"), parseInt((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide9 = savedGuideData.GuideStep) === null || _savedGuideData$Guide9 === void 0 ? void 0 : (_savedGuideData$Guide10 = _savedGuideData$Guide9[currentStep - 1]) === null || _savedGuideData$Guide10 === void 0 ? void 0 : (_savedGuideData$Guide11 = _savedGuideData$Guide10.Canvas) === null || _savedGuideData$Guide11 === void 0 ? void 0 : _savedGuideData$Guide11.BorderSize) || \"0\"), 0);\n    } else if (stepType === \"Tooltip\") {\n      setTooltipPreview(true);\n      resetHeightofBanner(\"\", 0, 0, 0);\n    } else if (stepType === \"Hotspot\") {\n      var _savedGuideData$Guide12, _savedGuideData$Guide13, _savedGuideData$Guide14, _savedGuideData$Guide15;\n      setHotspotPreview(true);\n      setOpenTooltip(false);\n      resetHeightofBanner(\"\", 0, 0, 0);\n\n      // Initialize tour hotspot metadata for proper functionality\n      initializeTourHotspotMetadata();\n\n      // Ensure pulse animation is enabled for tour hotspots\n      // useDrawerStore.setState({ pulseAnimationsH: true });\n\n      // Debug logging for tour hotspot initialization\n      console.log(\"Tour hotspot initialized:\", {\n        stepType,\n        currentStep,\n        hotspotData: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide12 = savedGuideData.GuideStep) === null || _savedGuideData$Guide12 === void 0 ? void 0 : (_savedGuideData$Guide13 = _savedGuideData$Guide12[currentStep - 1]) === null || _savedGuideData$Guide13 === void 0 ? void 0 : _savedGuideData$Guide13.Hotspot,\n        elementPath: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide14 = savedGuideData.GuideStep) === null || _savedGuideData$Guide14 === void 0 ? void 0 : (_savedGuideData$Guide15 = _savedGuideData$Guide14[currentStep - 1]) === null || _savedGuideData$Guide15 === void 0 ? void 0 : _savedGuideData$Guide15.ElementPath\n      });\n    }\n  }, [stepType, currentStep]); // Dependencies ensure effect runs when stepType or currentStep changes\n\n  // Initialize tour hotspot metadata for HotspotPreview compatibility\n  const initializeTourHotspotMetadata = () => {\n    var _savedGuideData$Guide16;\n    const currentStepData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide16 = savedGuideData.GuideStep) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16[currentStep - 1];\n    if (currentStepData && currentStepData.StepType === \"Hotspot\") {\n      var _Canvas, _Canvas2, _Canvas3, _Canvas4, _Canvas5, _Canvas6, _Canvas7, _Canvas8, _Canvas9, _Canvas10, _Design, _Design$GotoNext, _Design2, _Design2$GotoNext, _Design3, _Design3$GotoNext, _Design4, _Design4$GotoNext;\n      const hotspotProps = currentStepData.Hotspot || {};\n\n      // Create metadata structure for HotspotPreview\n      // Extract containers from saved guide data\n      const containers = [];\n\n      // Add RTE containers from TextFieldProperties\n      if (currentStepData.TextFieldProperties && currentStepData.TextFieldProperties.length > 0) {\n        currentStepData.TextFieldProperties.forEach(textField => {\n          containers.push({\n            id: textField.Id || crypto.randomUUID(),\n            type: \"rte\",\n            placeholder: \"Start typing here...\",\n            rteBoxValue: textField.Text || \"\",\n            style: {\n              backgroundColor: \"transparent\"\n            }\n          });\n        });\n      }\n\n      // Add button containers from ButtonSection\n      if (currentStepData.ButtonSection && currentStepData.ButtonSection.length > 0) {\n        currentStepData.ButtonSection.forEach(section => {\n          var _section$CustomButton;\n          containers.push({\n            id: section.Id || crypto.randomUUID(),\n            type: \"button\",\n            buttons: ((_section$CustomButton = section.CustomButtons) === null || _section$CustomButton === void 0 ? void 0 : _section$CustomButton.map(button => {\n              var _button$ButtonAction, _button$ButtonAction2, _button$ButtonAction3, _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3;\n              return {\n                id: button.ButtonId || crypto.randomUUID(),\n                name: button.ButtonName || \"Button\",\n                type: button.ButtonStyle || \"primary\",\n                position: button.Alignment || \"center\",\n                actions: {\n                  value: ((_button$ButtonAction = button.ButtonAction) === null || _button$ButtonAction === void 0 ? void 0 : _button$ButtonAction.Action) || \"close\",\n                  targetURL: ((_button$ButtonAction2 = button.ButtonAction) === null || _button$ButtonAction2 === void 0 ? void 0 : _button$ButtonAction2.TargetUrl) || \"\",\n                  tab: ((_button$ButtonAction3 = button.ButtonAction) === null || _button$ButtonAction3 === void 0 ? void 0 : _button$ButtonAction3.ActionValue) || \"same-tab\"\n                },\n                style: {\n                  backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#5F9EA0\",\n                  color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#ffffff\",\n                  borderColor: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"#5F9EA0\"\n                }\n              };\n            })) || [],\n            style: {\n              backgroundColor: \"transparent\"\n            }\n          });\n        });\n      }\n\n      // Add image containers from ImageProperties\n      if (currentStepData.ImageProperties && currentStepData.ImageProperties.length > 0) {\n        currentStepData.ImageProperties.forEach(imageProperty => {\n          var _imageProperty$Custom;\n          containers.push({\n            id: imageProperty.Id || crypto.randomUUID(),\n            type: \"image\",\n            images: ((_imageProperty$Custom = imageProperty.CustomImage) === null || _imageProperty$Custom === void 0 ? void 0 : _imageProperty$Custom.map(image => ({\n              url: image.Url || \"\",\n              altText: image.AltText || \"\"\n            }))) || [],\n            style: {\n              backgroundColor: \"transparent\"\n            }\n          });\n        });\n      }\n      const tourHotspotMetadata = {\n        containers: containers,\n        stepName: currentStepData.StepTitle || `Step ${currentStep}`,\n        stepDescription: \"\",\n        stepType: \"Hotspot\",\n        currentStep: currentStep,\n        stepId: currentStepData.StepId || crypto.randomUUID(),\n        xpath: {\n          value: currentStepData.ElementPath || \"\",\n          PossibleElementPath: currentStepData.PossibleElementPath || \"\",\n          position: {\n            x: 0,\n            y: 0\n          }\n        },\n        id: crypto.randomUUID(),\n        hotspots: {\n          XPosition: hotspotProps.XPosition || \"4\",\n          YPosition: hotspotProps.YPosition || \"4\",\n          Type: hotspotProps.Type || \"Question\",\n          Color: hotspotProps.Color || \"yellow\",\n          Size: hotspotProps.Size || \"16\",\n          PulseAnimation: hotspotProps.PulseAnimation !== false,\n          stopAnimationUponInteraction: hotspotProps.stopAnimationUponInteraction !== false,\n          ShowUpon: hotspotProps.ShowUpon || \"Clicking Hotspot\",\n          ShowByDefault: hotspotProps.ShowByDefault || false\n        },\n        canvas: {\n          position: ((_Canvas = currentStepData.Canvas) === null || _Canvas === void 0 ? void 0 : _Canvas.Position) || \"auto\",\n          autoposition: ((_Canvas2 = currentStepData.Canvas) === null || _Canvas2 === void 0 ? void 0 : _Canvas2.AutoPosition) || false,\n          xaxis: ((_Canvas3 = currentStepData.Canvas) === null || _Canvas3 === void 0 ? void 0 : _Canvas3.XAxis) || \"0\",\n          yaxis: ((_Canvas4 = currentStepData.Canvas) === null || _Canvas4 === void 0 ? void 0 : _Canvas4.YAxis) || \"0\",\n          width: ((_Canvas5 = currentStepData.Canvas) === null || _Canvas5 === void 0 ? void 0 : _Canvas5.Width) || \"300px\",\n          padding: ((_Canvas6 = currentStepData.Canvas) === null || _Canvas6 === void 0 ? void 0 : _Canvas6.Padding) || \"16\",\n          borderRadius: ((_Canvas7 = currentStepData.Canvas) === null || _Canvas7 === void 0 ? void 0 : _Canvas7.BorderRadius) || \"8\",\n          borderSize: ((_Canvas8 = currentStepData.Canvas) === null || _Canvas8 === void 0 ? void 0 : _Canvas8.BorderSize) || \"1\",\n          borderColor: ((_Canvas9 = currentStepData.Canvas) === null || _Canvas9 === void 0 ? void 0 : _Canvas9.BorderColor) || \"transparent\",\n          backgroundColor: ((_Canvas10 = currentStepData.Canvas) === null || _Canvas10 === void 0 ? void 0 : _Canvas10.BackgroundColor) || \"#ffffff\"\n        },\n        design: {\n          gotoNext: {\n            ButtonId: ((_Design = currentStepData.Design) === null || _Design === void 0 ? void 0 : (_Design$GotoNext = _Design.GotoNext) === null || _Design$GotoNext === void 0 ? void 0 : _Design$GotoNext.ButtonId) || \"\",\n            ElementPath: ((_Design2 = currentStepData.Design) === null || _Design2 === void 0 ? void 0 : (_Design2$GotoNext = _Design2.GotoNext) === null || _Design2$GotoNext === void 0 ? void 0 : _Design2$GotoNext.ElementPath) || \"\",\n            NextStep: ((_Design3 = currentStepData.Design) === null || _Design3 === void 0 ? void 0 : (_Design3$GotoNext = _Design3.GotoNext) === null || _Design3$GotoNext === void 0 ? void 0 : _Design3$GotoNext.NextStep) || \"\",\n            ButtonName: ((_Design4 = currentStepData.Design) === null || _Design4 === void 0 ? void 0 : (_Design4$GotoNext = _Design4.GotoNext) === null || _Design4$GotoNext === void 0 ? void 0 : _Design4$GotoNext.ButtonName) || \"\"\n          },\n          element: {\n            progress: \"\",\n            isDismiss: false,\n            progressSelectedOption: 1,\n            progressColor: \"var(--primarycolor)\"\n          }\n        }\n      };\n\n      // Update store with tour hotspot metadata\n      const store = useDrawerStore.getState();\n      const updatedMetadata = [...store.toolTipGuideMetaData];\n\n      // Ensure array has enough entries\n      while (updatedMetadata.length < currentStep) {\n        updatedMetadata.push({\n          containers: [],\n          stepName: `Step ${updatedMetadata.length + 1}`,\n          stepDescription: \"\",\n          stepType: \"Hotspot\",\n          currentStep: updatedMetadata.length + 1,\n          stepId: crypto.randomUUID(),\n          xpath: {\n            value: \"\",\n            PossibleElementPath: \"\",\n            position: {\n              x: 0,\n              y: 0\n            }\n          },\n          id: crypto.randomUUID(),\n          hotspots: {\n            XPosition: \"4\",\n            YPosition: \"4\",\n            Type: \"Question\",\n            Color: \"yellow\",\n            Size: \"16\",\n            PulseAnimation: true,\n            stopAnimationUponInteraction: true,\n            ShowUpon: \"Clicking Hotspot\",\n            ShowByDefault: false\n          },\n          canvas: {\n            position: \"auto\",\n            autoposition: false,\n            xaxis: \"0\",\n            yaxis: \"0\",\n            width: \"300px\",\n            padding: \"16\",\n            borderRadius: \"8\",\n            borderSize: \"1\",\n            borderColor: \"transparent\",\n            backgroundColor: \"#ffffff\"\n          },\n          design: {\n            gotoNext: {\n              ButtonId: \"\",\n              ElementPath: \"\",\n              NextStep: \"\",\n              ButtonName: \"\"\n            },\n            element: {\n              progress: \"\",\n              isDismiss: false,\n              progressSelectedOption: 1,\n              progressColor: \"var(--primarycolor)\"\n            }\n          }\n        });\n      }\n\n      // Set metadata for current step\n      updatedMetadata[currentStep - 1] = tourHotspotMetadata;\n\n      // Update store\n      useDrawerStore.setState({\n        toolTipGuideMetaData: updatedMetadata,\n        elementSelected: true\n      });\n      // Use the function with skipOverlayReset to preserve user settings\n      useDrawerStore.getState().setSelectedTemplateTour(\"Hotspot\", true);\n    }\n  };\n  const resetHeightofBanner = (position, padding = 0, border = 0, top = 55) => {\n    const styleExTag = document.getElementById(\"dynamic-body-style\");\n    if (styleExTag) {\n      document.head.removeChild(styleExTag);\n    }\n    let styleTag = document.getElementById(\"dynamic-body-style\");\n    const bodyElement = document.body;\n    bodyElement.classList.add(\"dynamic-body-style\");\n    if (!styleTag) {\n      styleTag = document.createElement(\"style\");\n      styleTag.id = \"dynamic-body-style\";\n      let styles = `\n                    .dynamic-body-style {\n                        padding-top: ${top}px !important;\n                        max-height:calc(100% - 55px);\n                    }\n\n                    `;\n      // Add styles for body and nested elements\n      if (position === \"Push Down\") {\n        // Inside the \"Push Down\" condition:\n        const banner = document.getElementById(\"guide-popup\");\n        const bannerHeight = (banner === null || banner === void 0 ? void 0 : banner.offsetHeight) || 49;\n        // Include padding and border in the height calculation\n        const paddingValue = parseInt(padding.toString()) || 0;\n        const borderValue = parseInt(border.toString()) || 0;\n        // Only add additionalHeight if banner is null\n        const additionalHeight = paddingValue * 2 + borderValue * 2;\n        // Use bannerHeight + additionalHeight only if banner is null\n        const height = banner ? bannerHeight : bannerHeight + additionalHeight;\n        styles = `\n                        .dynamic-body-style {\n                            padding-top: ${height}px !important;\n                            max-height:calc(100% - 55px);\n                        }\n                       .dynamic-body-style header {\n\t\t\t\t\t\ttop: ${height}px !important;\n\t\t\t\t\t}\n\n                        \t\t.dynamic-body-style .page-sidebar {\n\t\t\t\t\t\tpadding-top: ${height}px !important;\n\t\t\t\t\t}\n                        `;\n      }\n      styleTag.innerHTML = styles;\n      document.head.appendChild(styleTag);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [OverlayValue && !bannerPreview && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        zIndex: 998,\n        pointerEvents: \"none\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 21\n    }, this), bannerPreview && /*#__PURE__*/_jsxDEV(BannerStepPreview, {\n      showBannerenduser: \"\",\n      setShowBannerenduser: \"\",\n      initialGuideData: savedGuideData,\n      setInitialGuideData: \"\",\n      onClose: handlecloseBannerPopup,\n      backgroundC: backgroundC\n      // Pass the current position from the current step\n      ,\n      Bposition: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide17 = savedGuideData.GuideStep) === null || _savedGuideData$Guide17 === void 0 ? void 0 : (_savedGuideData$Guide18 = _savedGuideData$Guide17[currentStep - 1]) === null || _savedGuideData$Guide18 === void 0 ? void 0 : (_savedGuideData$Guide19 = _savedGuideData$Guide18.Canvas) === null || _savedGuideData$Guide19 === void 0 ? void 0 : _savedGuideData$Guide19.Position) || \"Cover Top\",\n      bpadding: bpadding,\n      Bbordercolor: Bbordercolor,\n      BborderSize: BborderSize,\n      totalSteps: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide20 = savedGuideData.GuideStep) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.length) || 1,\n      ProgressColor: ProgressColor,\n      savedGuideData: savedGuideData,\n      progress: currentStep / ((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : _savedGuideData$Guide21.length) || 1) * 100\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 18\n    }, this), announcementPreview && /*#__PURE__*/_jsxDEV(AnnouncementPopup, {\n      selectedTemplate: selectedTemplate,\n      handlecloseBannerPopup: handlecloseBannerPopup,\n      guideStep: guideStep,\n      anchorEl: document.body,\n      onClose: onClose,\n      onPrevious: () => {},\n      onContinue: () => {},\n      title: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide22 = savedGuideData.GuideStep) === null || _savedGuideData$Guide22 === void 0 ? void 0 : (_savedGuideData$Guide23 = _savedGuideData$Guide22[currentStep]) === null || _savedGuideData$Guide23 === void 0 ? void 0 : _savedGuideData$Guide23.StepTitle) || \"\",\n      text: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide24 = savedGuideData.GuideStep) === null || _savedGuideData$Guide24 === void 0 ? void 0 : (_savedGuideData$Guide25 = _savedGuideData$Guide24[currentStep]) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25.TextFieldProperties) === null || _savedGuideData$Guide26 === void 0 ? void 0 : (_savedGuideData$Guide27 = _savedGuideData$Guide26[0]) === null || _savedGuideData$Guide27 === void 0 ? void 0 : _savedGuideData$Guide27.Text) || \"\",\n      imageUrl: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide28 = savedGuideData.GuideStep) === null || _savedGuideData$Guide28 === void 0 ? void 0 : (_savedGuideData$Guide29 = _savedGuideData$Guide28[currentStep]) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29.ImageProperties) === null || _savedGuideData$Guide30 === void 0 ? void 0 : (_savedGuideData$Guide31 = _savedGuideData$Guide30[0]) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31.CustomImage) === null || _savedGuideData$Guide32 === void 0 ? void 0 : (_savedGuideData$Guide33 = _savedGuideData$Guide32[0]) === null || _savedGuideData$Guide33 === void 0 ? void 0 : _savedGuideData$Guide33.Url) || \"\",\n      previousButtonLabel: \"Back\",\n      continueButtonLabel: \"Next\",\n      currentStep: currentStep,\n      totalSteps: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide34 = savedGuideData.GuideStep) === null || _savedGuideData$Guide34 === void 0 ? void 0 : _savedGuideData$Guide34.length) || 1,\n      onDontShowAgain: () => {},\n      progress: currentStep / ((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide35 = savedGuideData.GuideStep) === null || _savedGuideData$Guide35 === void 0 ? void 0 : _savedGuideData$Guide35.length) || 1) * 100,\n      textFieldProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide36 = savedGuideData.GuideStep) === null || _savedGuideData$Guide36 === void 0 ? void 0 : (_savedGuideData$Guide37 = _savedGuideData$Guide36[currentStep - 1]) === null || _savedGuideData$Guide37 === void 0 ? void 0 : _savedGuideData$Guide37.TextFieldProperties) || [],\n      imageProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide38 = savedGuideData.GuideStep) === null || _savedGuideData$Guide38 === void 0 ? void 0 : (_savedGuideData$Guide39 = _savedGuideData$Guide38[currentStep - 1]) === null || _savedGuideData$Guide39 === void 0 ? void 0 : _savedGuideData$Guide39.ImageProperties) || [],\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide40 = savedGuideData.GuideStep) === null || _savedGuideData$Guide40 === void 0 ? void 0 : (_savedGuideData$Guide41 = _savedGuideData$Guide40[currentStep - 1]) === null || _savedGuideData$Guide41 === void 0 ? void 0 : (_savedGuideData$Guide42 = _savedGuideData$Guide41.ButtonSection) === null || _savedGuideData$Guide42 === void 0 ? void 0 : _savedGuideData$Guide42.flatMap(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id\n      })))) || [],\n      modalProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide43 = savedGuideData.GuideStep) === null || _savedGuideData$Guide43 === void 0 ? void 0 : (_savedGuideData$Guide44 = _savedGuideData$Guide43[currentStep - 1]) === null || _savedGuideData$Guide44 === void 0 ? void 0 : _savedGuideData$Guide44.Modal) || {},\n      canvasProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide45 = savedGuideData.GuideStep) === null || _savedGuideData$Guide45 === void 0 ? void 0 : (_savedGuideData$Guide46 = _savedGuideData$Guide45[currentStep - 1]) === null || _savedGuideData$Guide46 === void 0 ? void 0 : _savedGuideData$Guide46.Canvas) || {},\n      htmlSnippet: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide47 = savedGuideData.GuideStep) === null || _savedGuideData$Guide47 === void 0 ? void 0 : (_savedGuideData$Guide48 = _savedGuideData$Guide47[currentStep - 1]) === null || _savedGuideData$Guide48 === void 0 ? void 0 : (_savedGuideData$Guide49 = _savedGuideData$Guide48.TextFieldProperties) === null || _savedGuideData$Guide49 === void 0 ? void 0 : (_savedGuideData$Guide50 = _savedGuideData$Guide49[0]) === null || _savedGuideData$Guide50 === void 0 ? void 0 : _savedGuideData$Guide50.Text) || \"\",\n      OverlayValue: OverlayValue,\n      savedGuideData: savedGuideData,\n      backgroundC: backgroundC,\n      Bposition: Bposition,\n      bpadding: bpadding,\n      Bbordercolor: Bbordercolor,\n      BborderSize: BborderSize,\n      ProgressColor: ProgressColor\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 455,\n      columnNumber: 21\n    }, this), hotspotPreview && /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: true,\n      showHotspotenduser: true,\n      handleHotspotHover: () => {\n        // Enable hover functionality for tour preview\n        console.log(\"Tour hotspot hover detected\");\n      },\n      handleHotspotClick: () => {\n        // Enable click functionality for tour preview\n        console.log(\"Tour hotspot click detected\");\n        setOpenTooltip(true);\n      },\n      guideStep: guideStep,\n      onClose: onClose,\n      title: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide51 = savedGuideData.GuideStep) === null || _savedGuideData$Guide51 === void 0 ? void 0 : (_savedGuideData$Guide52 = _savedGuideData$Guide51[currentStep - 1]) === null || _savedGuideData$Guide52 === void 0 ? void 0 : _savedGuideData$Guide52.StepType) || \"\",\n      text: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide53 = savedGuideData.GuideStep) === null || _savedGuideData$Guide53 === void 0 ? void 0 : (_savedGuideData$Guide54 = _savedGuideData$Guide53[currentStep]) === null || _savedGuideData$Guide54 === void 0 ? void 0 : (_savedGuideData$Guide55 = _savedGuideData$Guide54.TextFieldProperties) === null || _savedGuideData$Guide55 === void 0 ? void 0 : (_savedGuideData$Guide56 = _savedGuideData$Guide55[0]) === null || _savedGuideData$Guide56 === void 0 ? void 0 : _savedGuideData$Guide56.Text) || \"\",\n      imageUrl: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide57 = savedGuideData.GuideStep) === null || _savedGuideData$Guide57 === void 0 ? void 0 : (_savedGuideData$Guide58 = _savedGuideData$Guide57[currentStep]) === null || _savedGuideData$Guide58 === void 0 ? void 0 : (_savedGuideData$Guide59 = _savedGuideData$Guide58.ImageProperties) === null || _savedGuideData$Guide59 === void 0 ? void 0 : (_savedGuideData$Guide60 = _savedGuideData$Guide59[0]) === null || _savedGuideData$Guide60 === void 0 ? void 0 : (_savedGuideData$Guide61 = _savedGuideData$Guide60.CustomImage) === null || _savedGuideData$Guide61 === void 0 ? void 0 : (_savedGuideData$Guide62 = _savedGuideData$Guide61[0]) === null || _savedGuideData$Guide62 === void 0 ? void 0 : _savedGuideData$Guide62.Url) || \"\",\n      onPrevious: () => {},\n      onContinue: () => {},\n      currentStep: currentStep // Adjust currentStep for display (1-based index)\n      ,\n      totalSteps: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide63 = savedGuideData.GuideStep) === null || _savedGuideData$Guide63 === void 0 ? void 0 : _savedGuideData$Guide63.length) || 1,\n      onDontShowAgain: () => {},\n      progress: currentStep / ((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide64 = savedGuideData.GuideStep) === null || _savedGuideData$Guide64 === void 0 ? void 0 : _savedGuideData$Guide64.length) || 1) * 100,\n      textFieldProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide65 = savedGuideData.GuideStep) === null || _savedGuideData$Guide65 === void 0 ? void 0 : (_savedGuideData$Guide66 = _savedGuideData$Guide65[currentStep - 1]) === null || _savedGuideData$Guide66 === void 0 ? void 0 : _savedGuideData$Guide66.TextFieldProperties) || [],\n      imageProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide67 = savedGuideData.GuideStep) === null || _savedGuideData$Guide67 === void 0 ? void 0 : (_savedGuideData$Guide68 = _savedGuideData$Guide67[currentStep - 1]) === null || _savedGuideData$Guide68 === void 0 ? void 0 : _savedGuideData$Guide68.ImageProperties) || [],\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide69 = savedGuideData.GuideStep) === null || _savedGuideData$Guide69 === void 0 ? void 0 : (_savedGuideData$Guide70 = _savedGuideData$Guide69[currentStep - 1]) === null || _savedGuideData$Guide70 === void 0 ? void 0 : (_savedGuideData$Guide71 = _savedGuideData$Guide70.ButtonSection) === null || _savedGuideData$Guide71 === void 0 ? void 0 : (_savedGuideData$Guide72 = _savedGuideData$Guide71.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide72 === void 0 ? void 0 : _savedGuideData$Guide72.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide73 = savedGuideData.GuideStep) === null || _savedGuideData$Guide73 === void 0 ? void 0 : (_savedGuideData$Guide74 = _savedGuideData$Guide73[currentStep - 1]) === null || _savedGuideData$Guide74 === void 0 ? void 0 : _savedGuideData$Guide74.Modal) || {},\n      canvasProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide75 = savedGuideData.GuideStep) === null || _savedGuideData$Guide75 === void 0 ? void 0 : (_savedGuideData$Guide76 = _savedGuideData$Guide75[currentStep - 1]) === null || _savedGuideData$Guide76 === void 0 ? void 0 : _savedGuideData$Guide76.Canvas) || {},\n      htmlSnippet: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide77 = savedGuideData.GuideStep) === null || _savedGuideData$Guide77 === void 0 ? void 0 : (_savedGuideData$Guide78 = _savedGuideData$Guide77[currentStep - 1]) === null || _savedGuideData$Guide78 === void 0 ? void 0 : (_savedGuideData$Guide79 = _savedGuideData$Guide78.TextFieldProperties) === null || _savedGuideData$Guide79 === void 0 ? void 0 : (_savedGuideData$Guide80 = _savedGuideData$Guide79[0]) === null || _savedGuideData$Guide80 === void 0 ? void 0 : _savedGuideData$Guide80.Text) || \"\",\n      OverlayValue: OverlayValue,\n      savedGuideData: savedGuideData,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide81 = savedGuideData.GuideStep) === null || _savedGuideData$Guide81 === void 0 ? void 0 : (_savedGuideData$Guide82 = _savedGuideData$Guide81[currentStep - 1]) === null || _savedGuideData$Guide82 === void 0 ? void 0 : _savedGuideData$Guide82.Hotspot) || {},\n      anchorEl: document.body\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 20\n    }, this), tooltipPreview && /*#__PURE__*/_jsxDEV(TooltiplastUserview, {\n      guideStep: guideStep,\n      onClose: onClose,\n      title: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide83 = savedGuideData.GuideStep) === null || _savedGuideData$Guide83 === void 0 ? void 0 : (_savedGuideData$Guide84 = _savedGuideData$Guide83[currentStep]) === null || _savedGuideData$Guide84 === void 0 ? void 0 : _savedGuideData$Guide84.StepTitle) || \"\",\n      text: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide85 = savedGuideData.GuideStep) === null || _savedGuideData$Guide85 === void 0 ? void 0 : (_savedGuideData$Guide86 = _savedGuideData$Guide85[currentStep]) === null || _savedGuideData$Guide86 === void 0 ? void 0 : (_savedGuideData$Guide87 = _savedGuideData$Guide86.TextFieldProperties) === null || _savedGuideData$Guide87 === void 0 ? void 0 : (_savedGuideData$Guide88 = _savedGuideData$Guide87[0]) === null || _savedGuideData$Guide88 === void 0 ? void 0 : _savedGuideData$Guide88.Text) || \"\",\n      imageUrl: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide89 = savedGuideData.GuideStep) === null || _savedGuideData$Guide89 === void 0 ? void 0 : (_savedGuideData$Guide90 = _savedGuideData$Guide89[currentStep]) === null || _savedGuideData$Guide90 === void 0 ? void 0 : (_savedGuideData$Guide91 = _savedGuideData$Guide90.ImageProperties) === null || _savedGuideData$Guide91 === void 0 ? void 0 : (_savedGuideData$Guide92 = _savedGuideData$Guide91[0]) === null || _savedGuideData$Guide92 === void 0 ? void 0 : (_savedGuideData$Guide93 = _savedGuideData$Guide92.CustomImage) === null || _savedGuideData$Guide93 === void 0 ? void 0 : (_savedGuideData$Guide94 = _savedGuideData$Guide93[0]) === null || _savedGuideData$Guide94 === void 0 ? void 0 : _savedGuideData$Guide94.Url) || \"\",\n      onPrevious: () => {},\n      onContinue: () => {},\n      currentStep: currentStep // Adjust currentStep for display (1-based index)\n      ,\n      totalSteps: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide95 = savedGuideData.GuideStep) === null || _savedGuideData$Guide95 === void 0 ? void 0 : _savedGuideData$Guide95.length) || 1,\n      onDontShowAgain: () => {},\n      progress: (currentStep + 1) / ((savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide96 = savedGuideData.GuideStep) === null || _savedGuideData$Guide96 === void 0 ? void 0 : _savedGuideData$Guide96.length) || 1) * 100,\n      textFieldProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide97 = savedGuideData.GuideStep) === null || _savedGuideData$Guide97 === void 0 ? void 0 : (_savedGuideData$Guide98 = _savedGuideData$Guide97[currentStep - 1]) === null || _savedGuideData$Guide98 === void 0 ? void 0 : _savedGuideData$Guide98.TextFieldProperties) || [],\n      imageProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide99 = savedGuideData.GuideStep) === null || _savedGuideData$Guide99 === void 0 ? void 0 : (_savedGuideData$Guide100 = _savedGuideData$Guide99[currentStep - 1]) === null || _savedGuideData$Guide100 === void 0 ? void 0 : _savedGuideData$Guide100.ImageProperties) || [],\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide101 = savedGuideData.GuideStep) === null || _savedGuideData$Guide101 === void 0 ? void 0 : (_savedGuideData$Guide102 = _savedGuideData$Guide101[currentStep - 1]) === null || _savedGuideData$Guide102 === void 0 ? void 0 : (_savedGuideData$Guide103 = _savedGuideData$Guide102.ButtonSection) === null || _savedGuideData$Guide103 === void 0 ? void 0 : (_savedGuideData$Guide104 = _savedGuideData$Guide103.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide104 === void 0 ? void 0 : _savedGuideData$Guide104.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide105 = savedGuideData.GuideStep) === null || _savedGuideData$Guide105 === void 0 ? void 0 : (_savedGuideData$Guide106 = _savedGuideData$Guide105[currentStep - 1]) === null || _savedGuideData$Guide106 === void 0 ? void 0 : _savedGuideData$Guide106.Modal) || {},\n      canvasProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide107 = savedGuideData.GuideStep) === null || _savedGuideData$Guide107 === void 0 ? void 0 : (_savedGuideData$Guide108 = _savedGuideData$Guide107[currentStep - 1]) === null || _savedGuideData$Guide108 === void 0 ? void 0 : _savedGuideData$Guide108.Canvas) || {},\n      htmlSnippet: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide109 = savedGuideData.GuideStep) === null || _savedGuideData$Guide109 === void 0 ? void 0 : (_savedGuideData$Guide110 = _savedGuideData$Guide109[currentStep - 1]) === null || _savedGuideData$Guide110 === void 0 ? void 0 : (_savedGuideData$Guide111 = _savedGuideData$Guide110.TextFieldProperties) === null || _savedGuideData$Guide111 === void 0 ? void 0 : (_savedGuideData$Guide112 = _savedGuideData$Guide111[0]) === null || _savedGuideData$Guide112 === void 0 ? void 0 : _savedGuideData$Guide112.Text) || \"\",\n      OverlayValue: OverlayValue,\n      savedGuideData: savedGuideData\n      //hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\n      ,\n      anchorEl: document.body,\n      previousButtonLabel: \"\",\n      continueButtonLabel: \"\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 21\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 423,\n    columnNumber: 13\n  }, this);\n};\n_s(TourPreview, \"NOlXSvX1jOoUY4+dhH+P7RCF5YA=\", false, function () {\n  return [useDrawerStore];\n});\n_c = TourPreview;\nexport default TourPreview;\nvar _c;\n$RefreshReg$(_c, \"TourPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useDrawerStore", "AnnouncementPopup", "HotspotPreview", "TooltiplastUserview", "BannerStepPreview", "jsxDEV", "_jsxDEV", "TourPreview", "selectedTemplate", "handlecloseBannerPopup", "backgroundC", "Bposition", "bpadding", "Bbordercolor", "BborderSize", "guideStep", "anchorEl", "onClose", "onPrevious", "onContinue", "title", "text", "imageUrl", "videoUrl", "previousButtonLabel", "continueButtonLabel", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "_savedGuideData$Guide37", "_savedGuideData$Guide38", "_savedGuideData$Guide39", "_savedGuideData$Guide40", "_savedGuideData$Guide41", "_savedGuideData$Guide42", "_savedGuideData$Guide43", "_savedGuideData$Guide44", "_savedGuideData$Guide45", "_savedGuideData$Guide46", "_savedGuideData$Guide47", "_savedGuideData$Guide48", "_savedGuideData$Guide49", "_savedGuideData$Guide50", "_savedGuideData$Guide51", "_savedGuideData$Guide52", "_savedGuideData$Guide53", "_savedGuideData$Guide54", "_savedGuideData$Guide55", "_savedGuideData$Guide56", "_savedGuideData$Guide57", "_savedGuideData$Guide58", "_savedGuideData$Guide59", "_savedGuideData$Guide60", "_savedGuideData$Guide61", "_savedGuideData$Guide62", "_savedGuideData$Guide63", "_savedGuideData$Guide64", "_savedGuideData$Guide65", "_savedGuideData$Guide66", "_savedGuideData$Guide67", "_savedGuideData$Guide68", "_savedGuideData$Guide69", "_savedGuideData$Guide70", "_savedGuideData$Guide71", "_savedGuideData$Guide72", "_savedGuideData$Guide73", "_savedGuideData$Guide74", "_savedGuideData$Guide75", "_savedGuideData$Guide76", "_savedGuideData$Guide77", "_savedGuideData$Guide78", "_savedGuideData$Guide79", "_savedGuideData$Guide80", "_savedGuideData$Guide81", "_savedGuideData$Guide82", "_savedGuideData$Guide83", "_savedGuideData$Guide84", "_savedGuideData$Guide85", "_savedGuideData$Guide86", "_savedGuideData$Guide87", "_savedGuideData$Guide88", "_savedGuideData$Guide89", "_savedGuideData$Guide90", "_savedGuideData$Guide91", "_savedGuideData$Guide92", "_savedGuideData$Guide93", "_savedGuideData$Guide94", "_savedGuideData$Guide95", "_savedGuideData$Guide96", "_savedGuideData$Guide97", "_savedGuideData$Guide98", "_savedGuideData$Guide99", "_savedGuideData$Guide100", "_savedGuideData$Guide101", "_savedGuideData$Guide102", "_savedGuideData$Guide103", "_savedGuideData$Guide104", "_savedGuideData$Guide105", "_savedGuideData$Guide106", "_savedGuideData$Guide107", "_savedGuideData$Guide108", "_savedGuideData$Guide109", "_savedGuideData$Guide110", "_savedGuideData$Guide111", "_savedGuideData$Guide112", "setSelectedTemplate", "setBannerPopup", "setSelectedTemplateTour", "setSteps", "steps", "setTooltipCount", "tooltipCount", "HotspotGuideDetails", "announcementPreview", "setAnnouncementPreview", "bannerPreview", "setBannerPreview", "tooltipPreview", "setTooltipPreview", "hotspotPreview", "setHotspotPreview", "setCurrentStep", "setOpenTooltip", "ProgressColor", "setProgressColor", "state", "stepType", "GuideStep", "StepType", "existingHotspot", "document", "getElementById", "style", "display", "remove", "resetHeightofBanner", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "currentPosition", "<PERSON><PERSON>", "Position", "parseInt", "Padding", "BorderSize", "_savedGuideData$Guide12", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "_savedGuideData$Guide15", "initializeTourHotspotMetadata", "console", "log", "hotspotData", "Hotspot", "elementPath", "<PERSON>ement<PERSON><PERSON>", "_savedGuideData$Guide16", "currentStepData", "_<PERSON><PERSON>", "_Canvas2", "_Canvas3", "_Canvas4", "_Canvas5", "_Canvas6", "_Canvas7", "_Canvas8", "_Canvas9", "_Canvas10", "_Design", "_Design$GotoNext", "_Design2", "_Design2$GotoNext", "_Design3", "_Design3$GotoNext", "_Design4", "_Design4$GotoNext", "hotspotProps", "containers", "TextFieldProperties", "length", "for<PERSON>ach", "textField", "push", "id", "Id", "crypto", "randomUUID", "type", "placeholder", "rteBoxValue", "Text", "backgroundColor", "ButtonSection", "section", "_section$CustomButton", "buttons", "CustomButtons", "map", "button", "_button$ButtonAction", "_button$ButtonAction2", "_button$ButtonAction3", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "ButtonId", "name", "ButtonName", "ButtonStyle", "position", "Alignment", "actions", "value", "ButtonAction", "Action", "targetURL", "TargetUrl", "tab", "ActionValue", "ButtonProperties", "ButtonBackgroundColor", "color", "ButtonTextColor", "borderColor", "ButtonBorderColor", "ImageProperties", "imageProperty", "_imageProperty$Custom", "images", "CustomImage", "image", "url", "Url", "altText", "AltText", "tourHotspotMetadata", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "stepDescription", "stepId", "StepId", "xpath", "PossibleElementPath", "x", "y", "hotspots", "XPosition", "YPosition", "Type", "Color", "Size", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "ShowByDefault", "canvas", "autoposition", "AutoPosition", "xaxis", "XAxis", "yaxis", "YA<PERSON>s", "width", "<PERSON><PERSON><PERSON>", "padding", "borderRadius", "BorderRadius", "borderSize", "BorderColor", "BackgroundColor", "design", "gotoNext", "Design", "GotoNext", "NextStep", "element", "<PERSON><PERSON><PERSON><PERSON>", "progressSelectedOption", "progressColor", "store", "getState", "updatedMetadata", "toolTipGuideMetaData", "setState", "elementSelected", "border", "top", "styleExTag", "head", "<PERSON><PERSON><PERSON><PERSON>", "styleTag", "bodyElement", "body", "classList", "add", "createElement", "styles", "banner", "bannerHeight", "offsetHeight", "paddingValue", "toString", "borderValue", "additionalHeight", "height", "innerHTML", "append<PERSON><PERSON><PERSON>", "children", "left", "right", "bottom", "zIndex", "pointerEvents", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "showBanneren<PERSON>er", "setShowBannerenduser", "initialGuideData", "setInitialGuideData", "flatMap", "ContainerId", "Modal", "isHotspotPopupOpen", "showHotspotenduser", "handleHotspotHover", "handleHotspotClick", "reduce", "acc", "curr", "concat", "hotspotProperties", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/tours/tourPreview.tsx"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Popover, Button, Typography, Box, LinearProgress, DialogActions,IconButton } from \"@mui/material\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { PopoverOrigin } from \"@mui/material\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport AnnouncementPopup from \"../GuidesPreview/AnnouncementPreview\";\r\nimport HotspotPreview from \"../GuidesPreview/HotspotPreview\";\r\nimport TooltiplastUserview from \"../GuidesPreview/tooltippreview/Tooltips/Tooltipuserview\";\r\nimport BannerStepPreview from \"./BannerStepPreview\";\r\ninterface PopupProps {\r\n    handlecloseBannerPopup: any;\r\n    guideStep: any[];\r\n    anchorEl: null | HTMLElement;\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonLabel: string;\r\n    continueButtonLabel: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    backgroundC: any;\r\n    Bposition: any;\r\n    bpadding: any;\r\n    Bbordercolor: any;\r\n    BborderSize: any;\r\n    savedGuideData: GuideData | null;\r\n    selectedTemplate:any}\r\n\r\n\r\n\r\n    const TourPreview: React.FC<PopupProps> = ({\r\n        selectedTemplate,\r\n        handlecloseBannerPopup,\r\n        backgroundC,\r\n        Bposition,\r\n        bpadding,\r\n        Bbordercolor,\r\n        BborderSize,\r\n        guideStep,\r\n        anchorEl,\r\n        onClose,\r\n        onPrevious,\r\n        onContinue,\r\n        title,\r\n        text,\r\n        imageUrl,\r\n        videoUrl,\r\n        previousButtonLabel,\r\n        continueButtonLabel,\r\n        currentStep,\r\n        totalSteps,\r\n        onDontShowAgain,\r\n        progress,\r\n        textFieldProperties,\r\n        imageProperties,\r\n        customButton,\r\n        modalProperties,\r\n        canvasProperties,\r\n        htmlSnippet,\r\n        previousButtonStyles,\r\n        continueButtonStyles,\r\n        OverlayValue,\r\n        savedGuideData\r\n    }) => {\r\n        const {\r\n            setSelectedTemplate,\r\n            setBannerPopup,\r\n          setSelectedTemplateTour,\r\n          setSteps,\r\n          steps,\r\n          setTooltipCount,\r\n          tooltipCount,\r\n            HotspotGuideDetails,\r\n            announcementPreview, setAnnouncementPreview,\r\n            bannerPreview, setBannerPreview,\r\n            tooltipPreview, setTooltipPreview,\r\n            hotspotPreview, setHotspotPreview,\r\n            setCurrentStep,\r\n            setOpenTooltip,\r\n            ProgressColor,\r\n            setProgressColor\r\n        } = useDrawerStore((state: DrawerState) => state);\r\n\r\n        const stepType = savedGuideData?.GuideStep?.[currentStep - 1]?.StepType;\r\n\r\n        // Note: AI data synchronization is now handled in the store's setCurrentStep function\r\n        // to avoid infinite loops and ensure proper timing\r\n\r\n        useEffect(() => {\r\n            // Clean up any existing hotspot before setting new preview\r\n            const existingHotspot = document.getElementById(\"hotspotBlink\");\r\n            if (existingHotspot) {\r\n                existingHotspot.style.display = \"none\";\r\n                existingHotspot.remove();\r\n            }\r\n            // Reset all previews before setting the specific one\r\n            setAnnouncementPreview(false);\r\n            setBannerPreview(false);\r\n            setTooltipPreview(false);\r\n            setHotspotPreview(false);\r\n\r\n\r\n\r\n            // Set the correct preview based on stepType\r\n            if (stepType === \"Announcement\") {\r\n                setAnnouncementPreview(true);\r\n                resetHeightofBanner(\"\",0,0,0)\r\n            } else if (stepType === \"Banner\") {\r\n                setBannerPreview(true);\r\n                  // Get the current Canvas position from the step\r\n                  const currentPosition = savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position;\r\n                  // Use the current position value to properly apply Push Down effect\r\n                  resetHeightofBanner(\r\n                      currentPosition,\r\n                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Padding||\"0\"),\r\n                      parseInt(savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.BorderSize||\"0\"),    0\r\n                                  );\r\n            } else if (stepType === \"Tooltip\") {\r\n                setTooltipPreview(true);\r\n                resetHeightofBanner(\"\",0,0,0)\r\n            } else if(stepType === \"Hotspot\") {\r\n                setHotspotPreview(true);\r\n                setOpenTooltip(false)\r\n                resetHeightofBanner(\"\",0,0,0)\r\n\r\n                // Initialize tour hotspot metadata for proper functionality\r\n                initializeTourHotspotMetadata();\r\n\r\n                // Ensure pulse animation is enabled for tour hotspots\r\n                // useDrawerStore.setState({ pulseAnimationsH: true });\r\n\r\n                // Debug logging for tour hotspot initialization\r\n                console.log(\"Tour hotspot initialized:\", {\r\n                    stepType,\r\n                    currentStep,\r\n                    hotspotData: savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot,\r\n                    elementPath: savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n                });\r\n            }\r\n        }, [stepType, currentStep]);  // Dependencies ensure effect runs when stepType or currentStep changes\r\n\r\n        // Initialize tour hotspot metadata for HotspotPreview compatibility\r\n        const initializeTourHotspotMetadata = () => {\r\n            const currentStepData = savedGuideData?.GuideStep?.[currentStep - 1];\r\n            if (currentStepData && currentStepData.StepType === \"Hotspot\") {\r\n                const hotspotProps = (currentStepData as any).Hotspot || {};\r\n\r\n                // Create metadata structure for HotspotPreview\r\n                // Extract containers from saved guide data\r\n                const containers: any[] = [];\r\n\r\n                // Add RTE containers from TextFieldProperties\r\n                if (currentStepData.TextFieldProperties && currentStepData.TextFieldProperties.length > 0) {\r\n                    currentStepData.TextFieldProperties.forEach((textField: any) => {\r\n                        containers.push({\r\n                            id: textField.Id || crypto.randomUUID(),\r\n                            type: \"rte\",\r\n                            placeholder: \"Start typing here...\",\r\n                            rteBoxValue: textField.Text || \"\",\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                // Add button containers from ButtonSection\r\n                if (currentStepData.ButtonSection && currentStepData.ButtonSection.length > 0) {\r\n                    currentStepData.ButtonSection.forEach((section: any) => {\r\n                        containers.push({\r\n                            id: section.Id || crypto.randomUUID(),\r\n                            type: \"button\",\r\n                            buttons: section.CustomButtons?.map((button: any) => ({\r\n                                id: button.ButtonId || crypto.randomUUID(),\r\n                                name: button.ButtonName || \"Button\",\r\n                                type: button.ButtonStyle || \"primary\",\r\n                                position: button.Alignment || \"center\",\r\n                                actions: {\r\n                                    value: button.ButtonAction?.Action || \"close\",\r\n                                    targetURL: button.ButtonAction?.TargetUrl || \"\",\r\n                                    tab: button.ButtonAction?.ActionValue || \"same-tab\"\r\n                                },\r\n                                style: {\r\n                                    backgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#5F9EA0\",\r\n                                    color: button.ButtonProperties?.ButtonTextColor || \"#ffffff\",\r\n                                    borderColor: button.ButtonProperties?.ButtonBorderColor || \"#5F9EA0\",\r\n                                }\r\n                            })) || [],\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                // Add image containers from ImageProperties\r\n                if (currentStepData.ImageProperties && currentStepData.ImageProperties.length > 0) {\r\n                    currentStepData.ImageProperties.forEach((imageProperty: any) => {\r\n                        containers.push({\r\n                            id: imageProperty.Id || crypto.randomUUID(),\r\n                            type: \"image\",\r\n                            images: imageProperty.CustomImage?.map((image: any) => ({\r\n                                url: image.Url || \"\",\r\n                                altText: image.AltText || \"\",\r\n                            })) || [],\r\n                            style: {\r\n                                backgroundColor: \"transparent\",\r\n                            },\r\n                        });\r\n                    });\r\n                }\r\n\r\n                const tourHotspotMetadata = {\r\n                    containers: containers,\r\n                    stepName: (currentStepData as any).StepTitle || `Step ${currentStep}`,\r\n                    stepDescription: \"\",\r\n                    stepType: \"Hotspot\" as const,\r\n                    currentStep: currentStep,\r\n                    stepId: (currentStepData as any).StepId || crypto.randomUUID(),\r\n                    xpath: {\r\n                        value: (currentStepData as any).ElementPath || \"\",\r\n                        PossibleElementPath: (currentStepData as any).PossibleElementPath || \"\",\r\n                        position: { x: 0, y: 0 }\r\n                    },\r\n                    id: crypto.randomUUID(),\r\n                    hotspots: {\r\n                        XPosition: (hotspotProps as any).XPosition || \"4\",\r\n                        YPosition: (hotspotProps as any).YPosition || \"4\",\r\n                        Type: (hotspotProps as any).Type || \"Question\",\r\n                        Color: (hotspotProps as any).Color || \"yellow\",\r\n                        Size: (hotspotProps as any).Size || \"16\",\r\n                        PulseAnimation: (hotspotProps as any).PulseAnimation !== false,\r\n                        stopAnimationUponInteraction: (hotspotProps as any).stopAnimationUponInteraction !== false,\r\n                        ShowUpon: (hotspotProps as any).ShowUpon || \"Clicking Hotspot\",\r\n                        ShowByDefault: (hotspotProps as any).ShowByDefault || false,\r\n                    },\r\n                    canvas: {\r\n                        position: ((currentStepData as any).Canvas?.Position || \"auto\"),\r\n                        autoposition: ((currentStepData as any).Canvas?.AutoPosition || false),\r\n                        xaxis: ((currentStepData as any).Canvas?.XAxis || \"0\"),\r\n                        yaxis: ((currentStepData as any).Canvas?.YAxis || \"0\"),\r\n                        width: ((currentStepData as any).Canvas?.Width || \"300px\"),\r\n                        padding: ((currentStepData as any).Canvas?.Padding || \"16\"),\r\n                        borderRadius: ((currentStepData as any).Canvas?.BorderRadius || \"8\"),\r\n                        borderSize: ((currentStepData as any).Canvas?.BorderSize || \"1\"),\r\n                        borderColor: ((currentStepData as any).Canvas?.BorderColor || \"transparent\"),\r\n                        backgroundColor: ((currentStepData as any).Canvas?.BackgroundColor || \"#ffffff\"),\r\n                    },\r\n                    design: {\r\n                        gotoNext: {\r\n                            ButtonId: ((currentStepData as any).Design?.GotoNext?.ButtonId || \"\"),\r\n                            ElementPath: ((currentStepData as any).Design?.GotoNext?.ElementPath || \"\"),\r\n                            NextStep: ((currentStepData as any).Design?.GotoNext?.NextStep || \"\"),\r\n                            ButtonName: ((currentStepData as any).Design?.GotoNext?.ButtonName || \"\"),\r\n                        },\r\n                        element: {\r\n                            progress: \"\",\r\n                            isDismiss: false,\r\n                            progressSelectedOption: 1,\r\n                            progressColor: \"var(--primarycolor)\",\r\n                        },\r\n                    },\r\n                };\r\n\r\n                // Update store with tour hotspot metadata\r\n                const store = useDrawerStore.getState();\r\n                const updatedMetadata = [...store.toolTipGuideMetaData];\r\n\r\n                // Ensure array has enough entries\r\n                while (updatedMetadata.length < currentStep) {\r\n                    updatedMetadata.push({\r\n                        containers: [],\r\n                        stepName: `Step ${updatedMetadata.length + 1}`,\r\n                        stepDescription: \"\",\r\n                        stepType: \"Hotspot\",\r\n                        currentStep: updatedMetadata.length + 1,\r\n                        stepId: crypto.randomUUID(),\r\n                        xpath: { value: \"\", PossibleElementPath: \"\", position: { x: 0, y: 0 } },\r\n                        id: crypto.randomUUID(),\r\n                        hotspots: {\r\n                            XPosition: \"4\",\r\n                            YPosition: \"4\",\r\n                            Type: \"Question\",\r\n                            Color: \"yellow\",\r\n                            Size: \"16\",\r\n                            PulseAnimation: true,\r\n                            stopAnimationUponInteraction: true,\r\n                            ShowUpon: \"Clicking Hotspot\",\r\n                            ShowByDefault: false,\r\n                        },\r\n                        canvas: {\r\n                            position: \"auto\",\r\n                            autoposition: false,\r\n                            xaxis: \"0\",\r\n                            yaxis: \"0\",\r\n                            width: \"300px\",\r\n                            padding: \"16\",\r\n                            borderRadius: \"8\",\r\n                            borderSize: \"1\",\r\n                            borderColor: \"transparent\",\r\n                            backgroundColor: \"#ffffff\",\r\n                        },\r\n                        design: {\r\n                            gotoNext: {\r\n                                ButtonId: \"\",\r\n                                ElementPath: \"\",\r\n                                NextStep: \"\",\r\n                                ButtonName: \"\",\r\n                            },\r\n                            element: {\r\n                                progress: \"\",\r\n                                isDismiss: false,\r\n                                progressSelectedOption: 1,\r\n                                progressColor: \"var(--primarycolor)\",\r\n                            },\r\n                        },\r\n                    });\r\n                }\r\n\r\n                // Set metadata for current step\r\n                updatedMetadata[currentStep - 1] = tourHotspotMetadata as any;\r\n\r\n                // Update store\r\n                useDrawerStore.setState({\r\n                    toolTipGuideMetaData: updatedMetadata,\r\n                    elementSelected: true\r\n                });\r\n                // Use the function with skipOverlayReset to preserve user settings\r\n                useDrawerStore.getState().setSelectedTemplateTour(\"Hotspot\", true);\r\n            }\r\n        };\r\n\r\n        const resetHeightofBanner = (position : any,padding: any = 0,border : any =0, top: any = 55) => {\r\n            const styleExTag = document.getElementById(\"dynamic-body-style\");\r\n            if (styleExTag) {\r\n                document.head.removeChild(styleExTag);\r\n            }\r\n            let styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n                const bodyElement = document.body;\r\n\r\n                bodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n                if (!styleTag) {\r\n                    styleTag = document.createElement(\"style\");\r\n                    styleTag.id = \"dynamic-body-style\";\r\n\r\n                    let styles = `\r\n                    .dynamic-body-style {\r\n                        padding-top: ${top}px !important;\r\n                        max-height:calc(100% - 55px);\r\n                    }\r\n\r\n                    `;\r\n                // Add styles for body and nested elements\r\n                if (position === \"Push Down\")\r\n                    {\r\n                        // Inside the \"Push Down\" condition:\r\n    const banner = document.getElementById(\"guide-popup\");\r\n    const bannerHeight = banner?.offsetHeight || 49;\r\n    // Include padding and border in the height calculation\r\n    const paddingValue = parseInt(padding.toString()) || 0;\r\n    const borderValue = parseInt(border.toString()) || 0;\r\n    // Only add additionalHeight if banner is null\r\n    const additionalHeight = (paddingValue * 2) + (borderValue * 2);\r\n    // Use bannerHeight + additionalHeight only if banner is null\r\n    const height = banner ? bannerHeight : bannerHeight + additionalHeight;\r\n\r\n                        styles = `\r\n                        .dynamic-body-style {\r\n                            padding-top: ${height}px !important;\r\n                            max-height:calc(100% - 55px);\r\n                        }\r\n                       .dynamic-body-style header {\r\n\t\t\t\t\t\ttop: ${height}px !important;\r\n\t\t\t\t\t}\r\n\r\n                        \t\t.dynamic-body-style .page-sidebar {\r\n\t\t\t\t\t\tpadding-top: ${height}px !important;\r\n\t\t\t\t\t}\r\n                        `;\r\n\r\n\r\n                    }\r\n\r\n                    styleTag.innerHTML = styles;\r\n                    document.head.appendChild(styleTag);\r\n                }\r\n        }\r\n\r\n        return (\r\n            <div>\r\n                {OverlayValue && !bannerPreview && (\r\n                    <div style={{\r\n                        position: 'fixed',\r\n                        top: 0,\r\n                        left: 0,\r\n                        right: 0,\r\n                        bottom: 0,\r\n                        backgroundColor: 'rgba(0, 0, 0, 0.5)',\r\n                        zIndex: 998,\r\n                        pointerEvents: \"none\"\r\n                    }} />\r\n                )}\r\n      {bannerPreview  && (\r\n                 <BannerStepPreview\r\n                 showBannerenduser=\"\"\r\n                 setShowBannerenduser=\"\"\r\n                 initialGuideData={savedGuideData}\r\n                 setInitialGuideData=\"\"\r\n                 onClose={handlecloseBannerPopup}\r\n                 backgroundC={backgroundC}\r\n                // Pass the current position from the current step\r\n                Bposition={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas?.Position || \"Cover Top\"}                 bpadding={bpadding}\r\n                 Bbordercolor={Bbordercolor}\r\n                        BborderSize={BborderSize}\r\n                        totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                        ProgressColor={ProgressColor}\r\n                        savedGuideData={savedGuideData}\r\n                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n             />\r\n            )}\r\n                {announcementPreview &&(\r\n                    <AnnouncementPopup\r\n                        selectedTemplate={selectedTemplate}\r\n                        handlecloseBannerPopup={handlecloseBannerPopup}\r\n                        guideStep={guideStep}\r\n                        anchorEl={document.body}\r\n                        onClose={onClose}\r\n                        onPrevious={() => {}}\r\n                        onContinue={() => {}}\r\n                        title={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || \"\"}\r\n                        text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                        imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n                        previousButtonLabel=\"Back\"\r\n                        continueButtonLabel=\"Next\"\r\n                        currentStep={currentStep}\r\n                        totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                        onDontShowAgain={() => {}}\r\n                        progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n                        textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n                        imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n                        customButton={\r\n                            savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.flatMap(section =>\r\n                                section.CustomButtons.map(button => ({\r\n                                    ...button,\r\n                                    ContainerId: section.Id,\r\n                                }))\r\n                            ) || []\r\n                        }\r\n                        modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n                        canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n                        htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                        OverlayValue={OverlayValue}\r\n                        savedGuideData={savedGuideData}\r\n                        backgroundC={backgroundC}\r\n                        Bposition={Bposition}\r\n                        bpadding={bpadding}\r\n                        Bbordercolor={Bbordercolor}\r\n                        BborderSize={BborderSize}\r\n                        ProgressColor={ProgressColor}\r\n                    />\r\n                )}\r\n\r\n                {hotspotPreview && (\r\n                   <HotspotPreview\r\n                   isHotspotPopupOpen={true}\r\n                   showHotspotenduser={true}\r\n                   handleHotspotHover={() => {\r\n                       // Enable hover functionality for tour preview\r\n                       console.log(\"Tour hotspot hover detected\");\r\n                   }}\r\n                   handleHotspotClick={() => {\r\n                       // Enable click functionality for tour preview\r\n                       console.log(\"Tour hotspot click detected\");\r\n                       setOpenTooltip(true);\r\n                   }}\r\n                   guideStep={guideStep}\r\n                   onClose={onClose}\r\n                   title={savedGuideData?.GuideStep?.[currentStep-1]?.StepType || \"\"}\r\n                   text={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                   imageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n                   onPrevious={() => {}}\r\n                   onContinue={() => {}}\r\n                   currentStep={currentStep} // Adjust currentStep for display (1-based index)\r\n                   totalSteps={savedGuideData?.GuideStep?.length || 1}\r\n                   onDontShowAgain={() => {}}\r\n                   progress={((currentStep) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n                   textFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n                   imageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n                   customButton={\r\n                       savedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>\r\n                           section.CustomButtons.map((button) => ({\r\n                               ...button,\r\n                               ContainerId: section.Id, // Attach the container ID for grouping\r\n                           }))\r\n                       )?.reduce((acc, curr) => acc.concat(curr), []) || []\r\n                   }\r\n                   modalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n                   canvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n                   htmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n                   OverlayValue={OverlayValue}\r\n                   savedGuideData={savedGuideData}\r\n                   hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n                   anchorEl={document.body}\r\n\r\n               />\r\n                )}\r\n\r\n\r\n                {tooltipPreview && (\r\n\r\n\r\n                    <TooltiplastUserview\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\ttitle={savedGuideData?.GuideStep?.[currentStep]?.StepTitle || \"\"}\r\n\t\t\t\ttext={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n\t\t\t\timageUrl={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties?.[0]?.CustomImage?.[0]?.Url || \"\"}\r\n\t\t\t\tonPrevious={() => {}}\r\n\t\t\t\tonContinue={() => {}}\r\n\t\t\t\tcurrentStep={currentStep} // Adjust currentStep for display (1-based index)\r\n\t\t\t\ttotalSteps={savedGuideData?.GuideStep?.length || 1}\r\n\t\t\t\tonDontShowAgain={() => {}}\r\n\t\t\t\tprogress={((currentStep + 1) / (savedGuideData?.GuideStep?.length || 1)) * 100}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties || []}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.ImageProperties || []}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep - 1]?.ButtonSection?.map((section) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc, curr) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Modal || {}}\r\n\t\t\t\tcanvasProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Canvas || {}}\r\n\t\t\t\thtmlSnippet={savedGuideData?.GuideStep?.[currentStep - 1]?.TextFieldProperties?.[0]?.Text || \"\"}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\t//hotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t\tanchorEl={document.body}\r\n\t\t\t\tpreviousButtonLabel={\"\"}\r\n\t\t\t\tcontinueButtonLabel={\"\"}\r\n\t\t\t/>\r\n\r\n)}\r\n\r\n\r\n                {/* Add similar logic for Banner, Tooltip, and Hotspot if needed */}\r\n            </div>\r\n        );\r\n    };\r\n\r\n    export default TourPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAcC,SAAS,QAAQ,OAAO;AAIlD,OAAOC,cAAc,MAAuB,yBAAyB;AAErE,OAAOC,iBAAiB,MAAM,sCAAsC;AACpE,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,mBAAmB,MAAM,0DAA0D;AAC1F,OAAOC,iBAAiB,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAqDhD,MAAMC,WAAiC,GAAGA,CAAC;EACvCC,gBAAgB;EAChBC,sBAAsB;EACtBC,WAAW;EACXC,SAAS;EACTC,QAAQ;EACRC,YAAY;EACZC,WAAW;EACXC,SAAS;EACTC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,QAAQ;EACRC,mBAAmB;EACnBC,mBAAmB;EACnBC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC;AACJ,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA,EAAAC,wBAAA;EACF,MAAM;IACFC,mBAAmB;IACnBC,cAAc;IAChBC,uBAAuB;IACvBC,QAAQ;IACRC,KAAK;IACLC,eAAe;IACfC,YAAY;IACVC,mBAAmB;IACnBC,mBAAmB;IAAEC,sBAAsB;IAC3CC,aAAa;IAAEC,gBAAgB;IAC/BC,cAAc;IAAEC,iBAAiB;IACjCC,cAAc;IAAEC,iBAAiB;IACjCC,cAAc;IACdC,cAAc;IACdC,aAAa;IACbC;EACJ,CAAC,GAAG9J,cAAc,CAAE+J,KAAkB,IAAKA,KAAK,CAAC;EAEjD,MAAMC,QAAQ,GAAGzH,cAAc,aAAdA,cAAc,wBAAAE,qBAAA,GAAdF,cAAc,CAAE0H,SAAS,cAAAxH,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4Bf,WAAW,GAAG,CAAC,CAAC,cAAAgB,sBAAA,uBAA5CA,sBAAA,CAA8CwH,QAAQ;;EAEvE;EACA;;EAEAnK,SAAS,CAAC,MAAM;IACZ;IACA,MAAMoK,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAC;IAC/D,IAAIF,eAAe,EAAE;MACjBA,eAAe,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;MACtCJ,eAAe,CAACK,MAAM,CAAC,CAAC;IAC5B;IACA;IACApB,sBAAsB,CAAC,KAAK,CAAC;IAC7BE,gBAAgB,CAAC,KAAK,CAAC;IACvBE,iBAAiB,CAAC,KAAK,CAAC;IACxBE,iBAAiB,CAAC,KAAK,CAAC;;IAIxB;IACA,IAAIM,QAAQ,KAAK,cAAc,EAAE;MAC7BZ,sBAAsB,CAAC,IAAI,CAAC;MAC5BqB,mBAAmB,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAIT,QAAQ,KAAK,QAAQ,EAAE;MAAA,IAAAU,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MAC9B5B,gBAAgB,CAAC,IAAI,CAAC;MACpB;MACA,MAAM6B,eAAe,GAAG5I,cAAc,aAAdA,cAAc,wBAAAmI,sBAAA,GAAdnI,cAAc,CAAE0H,SAAS,cAAAS,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BhJ,WAAW,GAAG,CAAC,CAAC,cAAAiJ,sBAAA,wBAAAC,sBAAA,GAA5CD,sBAAA,CAA8CS,MAAM,cAAAR,sBAAA,uBAApDA,sBAAA,CAAsDS,QAAQ;MACtF;MACAZ,mBAAmB,CACfU,eAAe,EACfG,QAAQ,CAAC,CAAA/I,cAAc,aAAdA,cAAc,wBAAAsI,sBAAA,GAAdtI,cAAc,CAAE0H,SAAS,cAAAY,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BnJ,WAAW,GAAG,CAAC,CAAC,cAAAoJ,sBAAA,wBAAAC,sBAAA,GAA5CD,sBAAA,CAA8CM,MAAM,cAAAL,sBAAA,uBAApDA,sBAAA,CAAsDQ,OAAO,KAAE,GAAG,CAAC,EAC5ED,QAAQ,CAAC,CAAA/I,cAAc,aAAdA,cAAc,wBAAAyI,sBAAA,GAAdzI,cAAc,CAAE0H,SAAS,cAAAe,sBAAA,wBAAAC,uBAAA,GAAzBD,sBAAA,CAA4BtJ,WAAW,GAAG,CAAC,CAAC,cAAAuJ,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CG,MAAM,cAAAF,uBAAA,uBAApDA,uBAAA,CAAsDM,UAAU,KAAE,GAAG,CAAC,EAAK,CACxE,CAAC;IACvB,CAAC,MAAM,IAAIxB,QAAQ,KAAK,SAAS,EAAE;MAC/BR,iBAAiB,CAAC,IAAI,CAAC;MACvBiB,mBAAmB,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAGT,QAAQ,KAAK,SAAS,EAAE;MAAA,IAAAyB,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MAC9BlC,iBAAiB,CAAC,IAAI,CAAC;MACvBE,cAAc,CAAC,KAAK,CAAC;MACrBa,mBAAmB,CAAC,EAAE,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC;;MAE7B;MACAoB,6BAA6B,CAAC,CAAC;;MAE/B;MACA;;MAEA;MACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QACrC/B,QAAQ;QACRtI,WAAW;QACXsK,WAAW,EAAEzJ,cAAc,aAAdA,cAAc,wBAAAkJ,uBAAA,GAAdlJ,cAAc,CAAE0H,SAAS,cAAAwB,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B/J,WAAW,GAAG,CAAC,CAAC,cAAAgK,uBAAA,uBAA5CA,uBAAA,CAA8CO,OAAO;QAClEC,WAAW,EAAE3J,cAAc,aAAdA,cAAc,wBAAAoJ,uBAAA,GAAdpJ,cAAc,CAAE0H,SAAS,cAAA0B,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjK,WAAW,GAAG,CAAC,CAAC,cAAAkK,uBAAA,uBAA5CA,uBAAA,CAA8CO;MAC/D,CAAC,CAAC;IACN;EACJ,CAAC,EAAE,CAACnC,QAAQ,EAAEtI,WAAW,CAAC,CAAC,CAAC,CAAE;;EAE9B;EACA,MAAMmK,6BAA6B,GAAGA,CAAA,KAAM;IAAA,IAAAO,uBAAA;IACxC,MAAMC,eAAe,GAAG9J,cAAc,aAAdA,cAAc,wBAAA6J,uBAAA,GAAd7J,cAAc,CAAE0H,SAAS,cAAAmC,uBAAA,uBAAzBA,uBAAA,CAA4B1K,WAAW,GAAG,CAAC,CAAC;IACpE,IAAI2K,eAAe,IAAIA,eAAe,CAACnC,QAAQ,KAAK,SAAS,EAAE;MAAA,IAAAoC,OAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,QAAA,EAAAC,SAAA,EAAAC,OAAA,EAAAC,gBAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,iBAAA,EAAAC,QAAA,EAAAC,iBAAA;MAC3D,MAAMC,YAAY,GAAInB,eAAe,CAASJ,OAAO,IAAI,CAAC,CAAC;;MAE3D;MACA;MACA,MAAMwB,UAAiB,GAAG,EAAE;;MAE5B;MACA,IAAIpB,eAAe,CAACqB,mBAAmB,IAAIrB,eAAe,CAACqB,mBAAmB,CAACC,MAAM,GAAG,CAAC,EAAE;QACvFtB,eAAe,CAACqB,mBAAmB,CAACE,OAAO,CAAEC,SAAc,IAAK;UAC5DJ,UAAU,CAACK,IAAI,CAAC;YACZC,EAAE,EAAEF,SAAS,CAACG,EAAE,IAAIC,MAAM,CAACC,UAAU,CAAC,CAAC;YACvCC,IAAI,EAAE,KAAK;YACXC,WAAW,EAAE,sBAAsB;YACnCC,WAAW,EAAER,SAAS,CAACS,IAAI,IAAI,EAAE;YACjChE,KAAK,EAAE;cACHiE,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN;;MAEA;MACA,IAAIlC,eAAe,CAACmC,aAAa,IAAInC,eAAe,CAACmC,aAAa,CAACb,MAAM,GAAG,CAAC,EAAE;QAC3EtB,eAAe,CAACmC,aAAa,CAACZ,OAAO,CAAEa,OAAY,IAAK;UAAA,IAAAC,qBAAA;UACpDjB,UAAU,CAACK,IAAI,CAAC;YACZC,EAAE,EAAEU,OAAO,CAACT,EAAE,IAAIC,MAAM,CAACC,UAAU,CAAC,CAAC;YACrCC,IAAI,EAAE,QAAQ;YACdQ,OAAO,EAAE,EAAAD,qBAAA,GAAAD,OAAO,CAACG,aAAa,cAAAF,qBAAA,uBAArBA,qBAAA,CAAuBG,GAAG,CAAEC,MAAW;cAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,OAAM;gBAClDrB,EAAE,EAAEe,MAAM,CAACO,QAAQ,IAAIpB,MAAM,CAACC,UAAU,CAAC,CAAC;gBAC1CoB,IAAI,EAAER,MAAM,CAACS,UAAU,IAAI,QAAQ;gBACnCpB,IAAI,EAAEW,MAAM,CAACU,WAAW,IAAI,SAAS;gBACrCC,QAAQ,EAAEX,MAAM,CAACY,SAAS,IAAI,QAAQ;gBACtCC,OAAO,EAAE;kBACLC,KAAK,EAAE,EAAAb,oBAAA,GAAAD,MAAM,CAACe,YAAY,cAAAd,oBAAA,uBAAnBA,oBAAA,CAAqBe,MAAM,KAAI,OAAO;kBAC7CC,SAAS,EAAE,EAAAf,qBAAA,GAAAF,MAAM,CAACe,YAAY,cAAAb,qBAAA,uBAAnBA,qBAAA,CAAqBgB,SAAS,KAAI,EAAE;kBAC/CC,GAAG,EAAE,EAAAhB,qBAAA,GAAAH,MAAM,CAACe,YAAY,cAAAZ,qBAAA,uBAAnBA,qBAAA,CAAqBiB,WAAW,KAAI;gBAC7C,CAAC;gBACD5F,KAAK,EAAE;kBACHiE,eAAe,EAAE,EAAAW,qBAAA,GAAAJ,MAAM,CAACqB,gBAAgB,cAAAjB,qBAAA,uBAAvBA,qBAAA,CAAyBkB,qBAAqB,KAAI,SAAS;kBAC5EC,KAAK,EAAE,EAAAlB,sBAAA,GAAAL,MAAM,CAACqB,gBAAgB,cAAAhB,sBAAA,uBAAvBA,sBAAA,CAAyBmB,eAAe,KAAI,SAAS;kBAC5DC,WAAW,EAAE,EAAAnB,sBAAA,GAAAN,MAAM,CAACqB,gBAAgB,cAAAf,sBAAA,uBAAvBA,sBAAA,CAAyBoB,iBAAiB,KAAI;gBAC/D;cACJ,CAAC;YAAA,CAAC,CAAC,KAAI,EAAE;YACTlG,KAAK,EAAE;cACHiE,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN;;MAEA;MACA,IAAIlC,eAAe,CAACoE,eAAe,IAAIpE,eAAe,CAACoE,eAAe,CAAC9C,MAAM,GAAG,CAAC,EAAE;QAC/EtB,eAAe,CAACoE,eAAe,CAAC7C,OAAO,CAAE8C,aAAkB,IAAK;UAAA,IAAAC,qBAAA;UAC5DlD,UAAU,CAACK,IAAI,CAAC;YACZC,EAAE,EAAE2C,aAAa,CAAC1C,EAAE,IAAIC,MAAM,CAACC,UAAU,CAAC,CAAC;YAC3CC,IAAI,EAAE,OAAO;YACbyC,MAAM,EAAE,EAAAD,qBAAA,GAAAD,aAAa,CAACG,WAAW,cAAAF,qBAAA,uBAAzBA,qBAAA,CAA2B9B,GAAG,CAAEiC,KAAU,KAAM;cACpDC,GAAG,EAAED,KAAK,CAACE,GAAG,IAAI,EAAE;cACpBC,OAAO,EAAEH,KAAK,CAACI,OAAO,IAAI;YAC9B,CAAC,CAAC,CAAC,KAAI,EAAE;YACT5G,KAAK,EAAE;cACHiE,eAAe,EAAE;YACrB;UACJ,CAAC,CAAC;QACN,CAAC,CAAC;MACN;MAEA,MAAM4C,mBAAmB,GAAG;QACxB1D,UAAU,EAAEA,UAAU;QACtB2D,QAAQ,EAAG/E,eAAe,CAASgF,SAAS,IAAI,QAAQ3P,WAAW,EAAE;QACrE4P,eAAe,EAAE,EAAE;QACnBtH,QAAQ,EAAE,SAAkB;QAC5BtI,WAAW,EAAEA,WAAW;QACxB6P,MAAM,EAAGlF,eAAe,CAASmF,MAAM,IAAIvD,MAAM,CAACC,UAAU,CAAC,CAAC;QAC9DuD,KAAK,EAAE;UACH7B,KAAK,EAAGvD,eAAe,CAASF,WAAW,IAAI,EAAE;UACjDuF,mBAAmB,EAAGrF,eAAe,CAASqF,mBAAmB,IAAI,EAAE;UACvEjC,QAAQ,EAAE;YAAEkC,CAAC,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE;QAC3B,CAAC;QACD7D,EAAE,EAAEE,MAAM,CAACC,UAAU,CAAC,CAAC;QACvB2D,QAAQ,EAAE;UACNC,SAAS,EAAGtE,YAAY,CAASsE,SAAS,IAAI,GAAG;UACjDC,SAAS,EAAGvE,YAAY,CAASuE,SAAS,IAAI,GAAG;UACjDC,IAAI,EAAGxE,YAAY,CAASwE,IAAI,IAAI,UAAU;UAC9CC,KAAK,EAAGzE,YAAY,CAASyE,KAAK,IAAI,QAAQ;UAC9CC,IAAI,EAAG1E,YAAY,CAAS0E,IAAI,IAAI,IAAI;UACxCC,cAAc,EAAG3E,YAAY,CAAS2E,cAAc,KAAK,KAAK;UAC9DC,4BAA4B,EAAG5E,YAAY,CAAS4E,4BAA4B,KAAK,KAAK;UAC1FC,QAAQ,EAAG7E,YAAY,CAAS6E,QAAQ,IAAI,kBAAkB;UAC9DC,aAAa,EAAG9E,YAAY,CAAS8E,aAAa,IAAI;QAC1D,CAAC;QACDC,MAAM,EAAE;UACJ9C,QAAQ,EAAG,EAAAnD,OAAA,GAACD,eAAe,CAASjB,MAAM,cAAAkB,OAAA,uBAA/BA,OAAA,CAAiCjB,QAAQ,KAAI,MAAO;UAC/DmH,YAAY,EAAG,EAAAjG,QAAA,GAACF,eAAe,CAASjB,MAAM,cAAAmB,QAAA,uBAA/BA,QAAA,CAAiCkG,YAAY,KAAI,KAAM;UACtEC,KAAK,EAAG,EAAAlG,QAAA,GAACH,eAAe,CAASjB,MAAM,cAAAoB,QAAA,uBAA/BA,QAAA,CAAiCmG,KAAK,KAAI,GAAI;UACtDC,KAAK,EAAG,EAAAnG,QAAA,GAACJ,eAAe,CAASjB,MAAM,cAAAqB,QAAA,uBAA/BA,QAAA,CAAiCoG,KAAK,KAAI,GAAI;UACtDC,KAAK,EAAG,EAAApG,QAAA,GAACL,eAAe,CAASjB,MAAM,cAAAsB,QAAA,uBAA/BA,QAAA,CAAiCqG,KAAK,KAAI,OAAQ;UAC1DC,OAAO,EAAG,EAAArG,QAAA,GAACN,eAAe,CAASjB,MAAM,cAAAuB,QAAA,uBAA/BA,QAAA,CAAiCpB,OAAO,KAAI,IAAK;UAC3D0H,YAAY,EAAG,EAAArG,QAAA,GAACP,eAAe,CAASjB,MAAM,cAAAwB,QAAA,uBAA/BA,QAAA,CAAiCsG,YAAY,KAAI,GAAI;UACpEC,UAAU,EAAG,EAAAtG,QAAA,GAACR,eAAe,CAASjB,MAAM,cAAAyB,QAAA,uBAA/BA,QAAA,CAAiCrB,UAAU,KAAI,GAAI;UAChE+E,WAAW,EAAG,EAAAzD,QAAA,GAACT,eAAe,CAASjB,MAAM,cAAA0B,QAAA,uBAA/BA,QAAA,CAAiCsG,WAAW,KAAI,aAAc;UAC5E7E,eAAe,EAAG,EAAAxB,SAAA,GAACV,eAAe,CAASjB,MAAM,cAAA2B,SAAA,uBAA/BA,SAAA,CAAiCsG,eAAe,KAAI;QAC1E,CAAC;QACDC,MAAM,EAAE;UACJC,QAAQ,EAAE;YACNlE,QAAQ,EAAG,EAAArC,OAAA,GAACX,eAAe,CAASmH,MAAM,cAAAxG,OAAA,wBAAAC,gBAAA,GAA/BD,OAAA,CAAiCyG,QAAQ,cAAAxG,gBAAA,uBAAzCA,gBAAA,CAA2CoC,QAAQ,KAAI,EAAG;YACrElD,WAAW,EAAG,EAAAe,QAAA,GAACb,eAAe,CAASmH,MAAM,cAAAtG,QAAA,wBAAAC,iBAAA,GAA/BD,QAAA,CAAiCuG,QAAQ,cAAAtG,iBAAA,uBAAzCA,iBAAA,CAA2ChB,WAAW,KAAI,EAAG;YAC3EuH,QAAQ,EAAG,EAAAtG,QAAA,GAACf,eAAe,CAASmH,MAAM,cAAApG,QAAA,wBAAAC,iBAAA,GAA/BD,QAAA,CAAiCqG,QAAQ,cAAApG,iBAAA,uBAAzCA,iBAAA,CAA2CqG,QAAQ,KAAI,EAAG;YACrEnE,UAAU,EAAG,EAAAjC,QAAA,GAACjB,eAAe,CAASmH,MAAM,cAAAlG,QAAA,wBAAAC,iBAAA,GAA/BD,QAAA,CAAiCmG,QAAQ,cAAAlG,iBAAA,uBAAzCA,iBAAA,CAA2CgC,UAAU,KAAI;UAC1E,CAAC;UACDoE,OAAO,EAAE;YACL9R,QAAQ,EAAE,EAAE;YACZ+R,SAAS,EAAE,KAAK;YAChBC,sBAAsB,EAAE,CAAC;YACzBC,aAAa,EAAE;UACnB;QACJ;MACJ,CAAC;;MAED;MACA,MAAMC,KAAK,GAAG/T,cAAc,CAACgU,QAAQ,CAAC,CAAC;MACvC,MAAMC,eAAe,GAAG,CAAC,GAAGF,KAAK,CAACG,oBAAoB,CAAC;;MAEvD;MACA,OAAOD,eAAe,CAACtG,MAAM,GAAGjM,WAAW,EAAE;QACzCuS,eAAe,CAACnG,IAAI,CAAC;UACjBL,UAAU,EAAE,EAAE;UACd2D,QAAQ,EAAE,QAAQ6C,eAAe,CAACtG,MAAM,GAAG,CAAC,EAAE;UAC9C2D,eAAe,EAAE,EAAE;UACnBtH,QAAQ,EAAE,SAAS;UACnBtI,WAAW,EAAEuS,eAAe,CAACtG,MAAM,GAAG,CAAC;UACvC4D,MAAM,EAAEtD,MAAM,CAACC,UAAU,CAAC,CAAC;UAC3BuD,KAAK,EAAE;YAAE7B,KAAK,EAAE,EAAE;YAAE8B,mBAAmB,EAAE,EAAE;YAAEjC,QAAQ,EAAE;cAAEkC,CAAC,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE;UAAE,CAAC;UACvE7D,EAAE,EAAEE,MAAM,CAACC,UAAU,CAAC,CAAC;UACvB2D,QAAQ,EAAE;YACNC,SAAS,EAAE,GAAG;YACdC,SAAS,EAAE,GAAG;YACdC,IAAI,EAAE,UAAU;YAChBC,KAAK,EAAE,QAAQ;YACfC,IAAI,EAAE,IAAI;YACVC,cAAc,EAAE,IAAI;YACpBC,4BAA4B,EAAE,IAAI;YAClCC,QAAQ,EAAE,kBAAkB;YAC5BC,aAAa,EAAE;UACnB,CAAC;UACDC,MAAM,EAAE;YACJ9C,QAAQ,EAAE,MAAM;YAChB+C,YAAY,EAAE,KAAK;YACnBE,KAAK,EAAE,GAAG;YACVE,KAAK,EAAE,GAAG;YACVE,KAAK,EAAE,OAAO;YACdE,OAAO,EAAE,IAAI;YACbC,YAAY,EAAE,GAAG;YACjBE,UAAU,EAAE,GAAG;YACf5C,WAAW,EAAE,aAAa;YAC1BhC,eAAe,EAAE;UACrB,CAAC;UACD+E,MAAM,EAAE;YACJC,QAAQ,EAAE;cACNlE,QAAQ,EAAE,EAAE;cACZlD,WAAW,EAAE,EAAE;cACfuH,QAAQ,EAAE,EAAE;cACZnE,UAAU,EAAE;YAChB,CAAC;YACDoE,OAAO,EAAE;cACL9R,QAAQ,EAAE,EAAE;cACZ+R,SAAS,EAAE,KAAK;cAChBC,sBAAsB,EAAE,CAAC;cACzBC,aAAa,EAAE;YACnB;UACJ;QACJ,CAAC,CAAC;MACN;;MAEA;MACAG,eAAe,CAACvS,WAAW,GAAG,CAAC,CAAC,GAAGyP,mBAA0B;;MAE7D;MACAnR,cAAc,CAACmU,QAAQ,CAAC;QACpBD,oBAAoB,EAAED,eAAe;QACrCG,eAAe,EAAE;MACrB,CAAC,CAAC;MACF;MACApU,cAAc,CAACgU,QAAQ,CAAC,CAAC,CAACnL,uBAAuB,CAAC,SAAS,EAAE,IAAI,CAAC;IACtE;EACJ,CAAC;EAED,MAAM4B,mBAAmB,GAAGA,CAACgF,QAAc,EAACuD,OAAY,GAAG,CAAC,EAACqB,MAAY,GAAE,CAAC,EAAEC,GAAQ,GAAG,EAAE,KAAK;IAC5F,MAAMC,UAAU,GAAGnK,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAC;IAChE,IAAIkK,UAAU,EAAE;MACZnK,QAAQ,CAACoK,IAAI,CAACC,WAAW,CAACF,UAAU,CAAC;IACzC;IACA,IAAIG,QAAQ,GAAGtK,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB;IAC5E,MAAMsK,WAAW,GAAGvK,QAAQ,CAACwK,IAAI;IAEjCD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAE/C,IAAI,CAACJ,QAAQ,EAAE;MACXA,QAAQ,GAAGtK,QAAQ,CAAC2K,aAAa,CAAC,OAAO,CAAC;MAC1CL,QAAQ,CAAC3G,EAAE,GAAG,oBAAoB;MAElC,IAAIiH,MAAM,GAAG;AACjC;AACA,uCAAuCV,GAAG;AAC1C;AACA;AACA;AACA,qBAAqB;MACL;MACA,IAAI7E,QAAQ,KAAK,WAAW,EACxB;QACI;QACpB,MAAMwF,MAAM,GAAG7K,QAAQ,CAACC,cAAc,CAAC,aAAa,CAAC;QACrD,MAAM6K,YAAY,GAAG,CAAAD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE,YAAY,KAAI,EAAE;QAC/C;QACA,MAAMC,YAAY,GAAG9J,QAAQ,CAAC0H,OAAO,CAACqC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QACtD,MAAMC,WAAW,GAAGhK,QAAQ,CAAC+I,MAAM,CAACgB,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC;QACpD;QACA,MAAME,gBAAgB,GAAIH,YAAY,GAAG,CAAC,GAAKE,WAAW,GAAG,CAAE;QAC/D;QACA,MAAME,MAAM,GAAGP,MAAM,GAAGC,YAAY,GAAGA,YAAY,GAAGK,gBAAgB;QAElDP,MAAM,GAAG;AACjC;AACA,2CAA2CQ,MAAM;AACjD;AACA;AACA;AACA,aAAaA,MAAM;AACnB;AACA;AACA;AACA,qBAAqBA,MAAM;AAC3B;AACA,yBAAyB;MAGL;MAEAd,QAAQ,CAACe,SAAS,GAAGT,MAAM;MAC3B5K,QAAQ,CAACoK,IAAI,CAACkB,WAAW,CAAChB,QAAQ,CAAC;IACvC;EACR,CAAC;EAED,oBACIpU,OAAA;IAAAqV,QAAA,GACKrT,YAAY,IAAI,CAAC+G,aAAa,iBAC3B/I,OAAA;MAAKgK,KAAK,EAAE;QACRmF,QAAQ,EAAE,OAAO;QACjB6E,GAAG,EAAE,CAAC;QACNsB,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTvH,eAAe,EAAE,oBAAoB;QACrCwH,MAAM,EAAE,GAAG;QACXC,aAAa,EAAE;MACnB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACP,EACV/M,aAAa,iBACH/I,OAAA,CAACF,iBAAiB;MAClBiW,iBAAiB,EAAC,EAAE;MACpBC,oBAAoB,EAAC,EAAE;MACvBC,gBAAgB,EAAEhU,cAAe;MACjCiU,mBAAmB,EAAC,EAAE;MACtBvV,OAAO,EAAER,sBAAuB;MAChCC,WAAW,EAAEA;MACd;MAAA;MACAC,SAAS,EAAE,CAAA4B,cAAc,aAAdA,cAAc,wBAAAI,uBAAA,GAAdJ,cAAc,CAAE0H,SAAS,cAAAtH,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjB,WAAW,GAAG,CAAC,CAAC,cAAAkB,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CwI,MAAM,cAAAvI,uBAAA,uBAApDA,uBAAA,CAAsDwI,QAAQ,KAAI,WAAY;MAAiBzK,QAAQ,EAAEA,QAAS;MAC5HC,YAAY,EAAEA,YAAa;MACpBC,WAAW,EAAEA,WAAY;MACzBa,UAAU,EAAE,CAAAY,cAAc,aAAdA,cAAc,wBAAAO,uBAAA,GAAdP,cAAc,CAAE0H,SAAS,cAAAnH,uBAAA,uBAAzBA,uBAAA,CAA2B6K,MAAM,KAAI,CAAE;MACnD9D,aAAa,EAAEA,aAAc;MAC7BtH,cAAc,EAAEA,cAAe;MAC/BV,QAAQ,EAAIH,WAAW,IAAK,CAAAa,cAAc,aAAdA,cAAc,wBAAAQ,uBAAA,GAAdR,cAAc,CAAE0H,SAAS,cAAAlH,uBAAA,uBAAzBA,uBAAA,CAA2B4K,MAAM,KAAI,CAAC,CAAC,GAAI;IAAI;MAAAsI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrF,CACD,EACIjN,mBAAmB,iBAChB7I,OAAA,CAACL,iBAAiB;MACdO,gBAAgB,EAAEA,gBAAiB;MACnCC,sBAAsB,EAAEA,sBAAuB;MAC/CM,SAAS,EAAEA,SAAU;MACrBC,QAAQ,EAAEoJ,QAAQ,CAACwK,IAAK;MACxB3T,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBC,KAAK,EAAE,CAAAmB,cAAc,aAAdA,cAAc,wBAAAS,uBAAA,GAAdT,cAAc,CAAE0H,SAAS,cAAAjH,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BtB,WAAW,CAAC,cAAAuB,uBAAA,uBAAxCA,uBAAA,CAA0CoO,SAAS,KAAI,EAAG;MACjEhQ,IAAI,EAAE,CAAAkB,cAAc,aAAdA,cAAc,wBAAAW,uBAAA,GAAdX,cAAc,CAAE0H,SAAS,cAAA/G,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BxB,WAAW,CAAC,cAAAyB,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0CuK,mBAAmB,cAAAtK,uBAAA,wBAAAC,uBAAA,GAA7DD,uBAAA,CAAgE,CAAC,CAAC,cAAAC,uBAAA,uBAAlEA,uBAAA,CAAoEiL,IAAI,KAAI,EAAG;MACrFhN,QAAQ,EAAE,CAAAiB,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAE0H,SAAS,cAAA3G,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B5B,WAAW,CAAC,cAAA6B,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0CkN,eAAe,cAAAjN,uBAAA,wBAAAC,uBAAA,GAAzDD,uBAAA,CAA4D,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9DD,uBAAA,CAAgEoN,WAAW,cAAAnN,uBAAA,wBAAAC,uBAAA,GAA3ED,uBAAA,CAA8E,CAAC,CAAC,cAAAC,uBAAA,uBAAhFA,uBAAA,CAAkFqN,GAAG,KAAI,EAAG;MACtGxP,mBAAmB,EAAC,MAAM;MAC1BC,mBAAmB,EAAC,MAAM;MAC1BC,WAAW,EAAEA,WAAY;MACzBC,UAAU,EAAE,CAAAY,cAAc,aAAdA,cAAc,wBAAAqB,uBAAA,GAAdrB,cAAc,CAAE0H,SAAS,cAAArG,uBAAA,uBAAzBA,uBAAA,CAA2B+J,MAAM,KAAI,CAAE;MACnD/L,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAC1BC,QAAQ,EAAIH,WAAW,IAAK,CAAAa,cAAc,aAAdA,cAAc,wBAAAsB,uBAAA,GAAdtB,cAAc,CAAE0H,SAAS,cAAApG,uBAAA,uBAAzBA,uBAAA,CAA2B8J,MAAM,KAAI,CAAC,CAAC,GAAI,GAAI;MAC3E7L,mBAAmB,EAAE,CAAAS,cAAc,aAAdA,cAAc,wBAAAuB,uBAAA,GAAdvB,cAAc,CAAE0H,SAAS,cAAAnG,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BpC,WAAW,GAAG,CAAC,CAAC,cAAAqC,uBAAA,uBAA5CA,uBAAA,CAA8C2J,mBAAmB,KAAI,EAAG;MAC7F3L,eAAe,EAAE,CAAAQ,cAAc,aAAdA,cAAc,wBAAAyB,uBAAA,GAAdzB,cAAc,CAAE0H,SAAS,cAAAjG,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BtC,WAAW,GAAG,CAAC,CAAC,cAAAuC,uBAAA,uBAA5CA,uBAAA,CAA8CwM,eAAe,KAAI,EAAG;MACrFzO,YAAY,EACR,CAAAO,cAAc,aAAdA,cAAc,wBAAA2B,uBAAA,GAAd3B,cAAc,CAAE0H,SAAS,cAAA/F,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BxC,WAAW,GAAG,CAAC,CAAC,cAAAyC,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CqK,aAAa,cAAApK,uBAAA,uBAA3DA,uBAAA,CAA6DqS,OAAO,CAAChI,OAAO,IACxEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAACC,MAAM,KAAK;QACjC,GAAGA,MAAM;QACT4H,WAAW,EAAEjI,OAAO,CAACT;MACzB,CAAC,CAAC,CACN,CAAC,KAAI,EACR;MACD/L,eAAe,EAAE,CAAAM,cAAc,aAAdA,cAAc,wBAAA8B,uBAAA,GAAd9B,cAAc,CAAE0H,SAAS,cAAA5F,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B3C,WAAW,GAAG,CAAC,CAAC,cAAA4C,uBAAA,uBAA5CA,uBAAA,CAA8CqS,KAAK,KAAI,CAAC,CAAE;MAC3EzU,gBAAgB,EAAE,CAAAK,cAAc,aAAdA,cAAc,wBAAAgC,uBAAA,GAAdhC,cAAc,CAAE0H,SAAS,cAAA1F,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B7C,WAAW,GAAG,CAAC,CAAC,cAAA8C,uBAAA,uBAA5CA,uBAAA,CAA8C4G,MAAM,KAAI,CAAC,CAAE;MAC7EjJ,WAAW,EAAE,CAAAI,cAAc,aAAdA,cAAc,wBAAAkC,uBAAA,GAAdlC,cAAc,CAAE0H,SAAS,cAAAxF,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B/C,WAAW,GAAG,CAAC,CAAC,cAAAgD,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CgJ,mBAAmB,cAAA/I,uBAAA,wBAAAC,uBAAA,GAAjED,uBAAA,CAAoE,CAAC,CAAC,cAAAC,uBAAA,uBAAtEA,uBAAA,CAAwE0J,IAAI,KAAI,EAAG;MAChGhM,YAAY,EAAEA,YAAa;MAC3BC,cAAc,EAAEA,cAAe;MAC/B7B,WAAW,EAAEA,WAAY;MACzBC,SAAS,EAAEA,SAAU;MACrBC,QAAQ,EAAEA,QAAS;MACnBC,YAAY,EAAEA,YAAa;MAC3BC,WAAW,EAAEA,WAAY;MACzB+I,aAAa,EAAEA;IAAc;MAAAoM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CACJ,EAEA3M,cAAc,iBACZnJ,OAAA,CAACJ,cAAc;MACf0W,kBAAkB,EAAE,IAAK;MACzBC,kBAAkB,EAAE,IAAK;MACzBC,kBAAkB,EAAEA,CAAA,KAAM;QACtB;QACAhL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC9C,CAAE;MACFgL,kBAAkB,EAAEA,CAAA,KAAM;QACtB;QACAjL,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;QAC1CnC,cAAc,CAAC,IAAI,CAAC;MACxB,CAAE;MACF7I,SAAS,EAAEA,SAAU;MACrBE,OAAO,EAAEA,OAAQ;MACjBG,KAAK,EAAE,CAAAmB,cAAc,aAAdA,cAAc,wBAAAsC,uBAAA,GAAdtC,cAAc,CAAE0H,SAAS,cAAApF,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BnD,WAAW,GAAC,CAAC,CAAC,cAAAoD,uBAAA,uBAA1CA,uBAAA,CAA4CoF,QAAQ,KAAI,EAAG;MAClE7I,IAAI,EAAE,CAAAkB,cAAc,aAAdA,cAAc,wBAAAwC,uBAAA,GAAdxC,cAAc,CAAE0H,SAAS,cAAAlF,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BrD,WAAW,CAAC,cAAAsD,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0C0I,mBAAmB,cAAAzI,uBAAA,wBAAAC,uBAAA,GAA7DD,uBAAA,CAAgE,CAAC,CAAC,cAAAC,uBAAA,uBAAlEA,uBAAA,CAAoEoJ,IAAI,KAAI,EAAG;MACrFhN,QAAQ,EAAE,CAAAiB,cAAc,aAAdA,cAAc,wBAAA4C,uBAAA,GAAd5C,cAAc,CAAE0H,SAAS,cAAA9E,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BzD,WAAW,CAAC,cAAA0D,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0CqL,eAAe,cAAApL,uBAAA,wBAAAC,uBAAA,GAAzDD,uBAAA,CAA4D,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9DD,uBAAA,CAAgEuL,WAAW,cAAAtL,uBAAA,wBAAAC,uBAAA,GAA3ED,uBAAA,CAA8E,CAAC,CAAC,cAAAC,uBAAA,uBAAhFA,uBAAA,CAAkFwL,GAAG,KAAI,EAAG;MACtG9P,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBO,WAAW,EAAEA,WAAY,CAAC;MAAA;MAC1BC,UAAU,EAAE,CAAAY,cAAc,aAAdA,cAAc,wBAAAkD,uBAAA,GAAdlD,cAAc,CAAE0H,SAAS,cAAAxE,uBAAA,uBAAzBA,uBAAA,CAA2BkI,MAAM,KAAI,CAAE;MACnD/L,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAC1BC,QAAQ,EAAIH,WAAW,IAAK,CAAAa,cAAc,aAAdA,cAAc,wBAAAmD,uBAAA,GAAdnD,cAAc,CAAE0H,SAAS,cAAAvE,uBAAA,uBAAzBA,uBAAA,CAA2BiI,MAAM,KAAI,CAAC,CAAC,GAAI,GAAI;MAC3E7L,mBAAmB,EAAE,CAAAS,cAAc,aAAdA,cAAc,wBAAAoD,uBAAA,GAAdpD,cAAc,CAAE0H,SAAS,cAAAtE,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjE,WAAW,GAAG,CAAC,CAAC,cAAAkE,uBAAA,uBAA5CA,uBAAA,CAA8C8H,mBAAmB,KAAI,EAAG;MAC7F3L,eAAe,EAAE,CAAAQ,cAAc,aAAdA,cAAc,wBAAAsD,uBAAA,GAAdtD,cAAc,CAAE0H,SAAS,cAAApE,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BnE,WAAW,GAAG,CAAC,CAAC,cAAAoE,uBAAA,uBAA5CA,uBAAA,CAA8C2K,eAAe,KAAI,EAAG;MACrFzO,YAAY,EACR,CAAAO,cAAc,aAAdA,cAAc,wBAAAwD,uBAAA,GAAdxD,cAAc,CAAE0H,SAAS,cAAAlE,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BrE,WAAW,GAAG,CAAC,CAAC,cAAAsE,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CwI,aAAa,cAAAvI,uBAAA,wBAAAC,uBAAA,GAA3DD,uBAAA,CAA6D4I,GAAG,CAAEJ,OAAO,IACrEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAAEC,MAAM,KAAM;QACnC,GAAGA,MAAM;QACT4H,WAAW,EAAEjI,OAAO,CAACT,EAAE,CAAE;MAC7B,CAAC,CAAC,CACN,CAAC,cAAA9H,uBAAA,uBALDA,uBAAA,CAKG8Q,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACrD;MACDjV,eAAe,EAAE,CAAAM,cAAc,aAAdA,cAAc,wBAAA4D,uBAAA,GAAd5D,cAAc,CAAE0H,SAAS,cAAA9D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BzE,WAAW,GAAG,CAAC,CAAC,cAAA0E,uBAAA,uBAA5CA,uBAAA,CAA8CuQ,KAAK,KAAI,CAAC,CAAE;MAC3EzU,gBAAgB,EAAE,CAAAK,cAAc,aAAdA,cAAc,wBAAA8D,uBAAA,GAAd9D,cAAc,CAAE0H,SAAS,cAAA5D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B3E,WAAW,GAAG,CAAC,CAAC,cAAA4E,uBAAA,uBAA5CA,uBAAA,CAA8C8E,MAAM,KAAI,CAAC,CAAE;MAC7EjJ,WAAW,EAAE,CAAAI,cAAc,aAAdA,cAAc,wBAAAgE,uBAAA,GAAdhE,cAAc,CAAE0H,SAAS,cAAA1D,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B7E,WAAW,GAAG,CAAC,CAAC,cAAA8E,uBAAA,wBAAAC,uBAAA,GAA5CD,uBAAA,CAA8CkH,mBAAmB,cAAAjH,uBAAA,wBAAAC,uBAAA,GAAjED,uBAAA,CAAoE,CAAC,CAAC,cAAAC,uBAAA,uBAAtEA,uBAAA,CAAwE4H,IAAI,KAAI,EAAG;MAChGhM,YAAY,EAAEA,YAAa;MAC3BC,cAAc,EAAEA,cAAe;MAC/B6U,iBAAiB,EAAE,CAAA7U,cAAc,aAAdA,cAAc,wBAAAoE,uBAAA,GAAdpE,cAAc,CAAE0H,SAAS,cAAAtD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjF,WAAW,GAAG,CAAC,CAAC,cAAAkF,uBAAA,uBAA5CA,uBAAA,CAA8CqF,OAAO,KAAI,CAAC,CAAE;MAC/EjL,QAAQ,EAAEoJ,QAAQ,CAACwK;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE3B,CACC,EAGA7M,cAAc,iBAGXjJ,OAAA,CAACH,mBAAmB;MACpCY,SAAS,EAAEA,SAAU;MACrBE,OAAO,EAAEA,OAAQ;MACjBG,KAAK,EAAE,CAAAmB,cAAc,aAAdA,cAAc,wBAAAsE,uBAAA,GAAdtE,cAAc,CAAE0H,SAAS,cAAApD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BnF,WAAW,CAAC,cAAAoF,uBAAA,uBAAxCA,uBAAA,CAA0CuK,SAAS,KAAI,EAAG;MACjEhQ,IAAI,EAAE,CAAAkB,cAAc,aAAdA,cAAc,wBAAAwE,uBAAA,GAAdxE,cAAc,CAAE0H,SAAS,cAAAlD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BrF,WAAW,CAAC,cAAAsF,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0C0G,mBAAmB,cAAAzG,uBAAA,wBAAAC,uBAAA,GAA7DD,uBAAA,CAAgE,CAAC,CAAC,cAAAC,uBAAA,uBAAlEA,uBAAA,CAAoEoH,IAAI,KAAI,EAAG;MACrFhN,QAAQ,EAAE,CAAAiB,cAAc,aAAdA,cAAc,wBAAA4E,uBAAA,GAAd5E,cAAc,CAAE0H,SAAS,cAAA9C,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BzF,WAAW,CAAC,cAAA0F,uBAAA,wBAAAC,uBAAA,GAAxCD,uBAAA,CAA0CqJ,eAAe,cAAApJ,uBAAA,wBAAAC,uBAAA,GAAzDD,uBAAA,CAA4D,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9DD,uBAAA,CAAgEuJ,WAAW,cAAAtJ,uBAAA,wBAAAC,uBAAA,GAA3ED,uBAAA,CAA8E,CAAC,CAAC,cAAAC,uBAAA,uBAAhFA,uBAAA,CAAkFwJ,GAAG,KAAI,EAAG;MACtG9P,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;MACrBO,WAAW,EAAEA,WAAY,CAAC;MAAA;MAC1BC,UAAU,EAAE,CAAAY,cAAc,aAAdA,cAAc,wBAAAkF,uBAAA,GAAdlF,cAAc,CAAE0H,SAAS,cAAAxC,uBAAA,uBAAzBA,uBAAA,CAA2BkG,MAAM,KAAI,CAAE;MACnD/L,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;MAC1BC,QAAQ,EAAG,CAACH,WAAW,GAAG,CAAC,KAAK,CAAAa,cAAc,aAAdA,cAAc,wBAAAmF,uBAAA,GAAdnF,cAAc,CAAE0H,SAAS,cAAAvC,uBAAA,uBAAzBA,uBAAA,CAA2BiG,MAAM,KAAI,CAAC,CAAC,GAAI,GAAI;MAC/E7L,mBAAmB,EAAE,CAAAS,cAAc,aAAdA,cAAc,wBAAAoF,uBAAA,GAAdpF,cAAc,CAAE0H,SAAS,cAAAtC,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BjG,WAAW,GAAG,CAAC,CAAC,cAAAkG,uBAAA,uBAA5CA,uBAAA,CAA8C8F,mBAAmB,KAAI,EAAG;MAC7F3L,eAAe,EAAE,CAAAQ,cAAc,aAAdA,cAAc,wBAAAsF,uBAAA,GAAdtF,cAAc,CAAE0H,SAAS,cAAApC,uBAAA,wBAAAC,wBAAA,GAAzBD,uBAAA,CAA4BnG,WAAW,GAAG,CAAC,CAAC,cAAAoG,wBAAA,uBAA5CA,wBAAA,CAA8C2I,eAAe,KAAI,EAAG;MACrFzO,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAAwF,wBAAA,GAAdxF,cAAc,CAAE0H,SAAS,cAAAlC,wBAAA,wBAAAC,wBAAA,GAAzBD,wBAAA,CAA4BrG,WAAW,GAAG,CAAC,CAAC,cAAAsG,wBAAA,wBAAAC,wBAAA,GAA5CD,wBAAA,CAA8CwG,aAAa,cAAAvG,wBAAA,wBAAAC,wBAAA,GAA3DD,wBAAA,CAA6D4G,GAAG,CAAEJ,OAAO,IACxEA,OAAO,CAACG,aAAa,CAACC,GAAG,CAAEC,MAAM,KAAM;QACtC,GAAGA,MAAM;QACT4H,WAAW,EAAEjI,OAAO,CAACT,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAA9F,wBAAA,uBALDA,wBAAA,CAKG8O,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EAClD;MACDjV,eAAe,EAAE,CAAAM,cAAc,aAAdA,cAAc,wBAAA4F,wBAAA,GAAd5F,cAAc,CAAE0H,SAAS,cAAA9B,wBAAA,wBAAAC,wBAAA,GAAzBD,wBAAA,CAA4BzG,WAAW,GAAG,CAAC,CAAC,cAAA0G,wBAAA,uBAA5CA,wBAAA,CAA8CuO,KAAK,KAAI,CAAC,CAAE;MAC3EzU,gBAAgB,EAAE,CAAAK,cAAc,aAAdA,cAAc,wBAAA8F,wBAAA,GAAd9F,cAAc,CAAE0H,SAAS,cAAA5B,wBAAA,wBAAAC,wBAAA,GAAzBD,wBAAA,CAA4B3G,WAAW,GAAG,CAAC,CAAC,cAAA4G,wBAAA,uBAA5CA,wBAAA,CAA8C8C,MAAM,KAAI,CAAC,CAAE;MAC7EjJ,WAAW,EAAE,CAAAI,cAAc,aAAdA,cAAc,wBAAAgG,wBAAA,GAAdhG,cAAc,CAAE0H,SAAS,cAAA1B,wBAAA,wBAAAC,wBAAA,GAAzBD,wBAAA,CAA4B7G,WAAW,GAAG,CAAC,CAAC,cAAA8G,wBAAA,wBAAAC,wBAAA,GAA5CD,wBAAA,CAA8CkF,mBAAmB,cAAAjF,wBAAA,wBAAAC,wBAAA,GAAjED,wBAAA,CAAoE,CAAC,CAAC,cAAAC,wBAAA,uBAAtEA,wBAAA,CAAwE4F,IAAI,KAAI,EAAG;MAChGhM,YAAY,EAAEA,YAAa;MAC3BC,cAAc,EAAEA;MAChB;MAAA;MACAvB,QAAQ,EAAEoJ,QAAQ,CAACwK,IAAK;MACxBpT,mBAAmB,EAAE,EAAG;MACxBC,mBAAmB,EAAE;IAAG;MAAAwU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CAEH;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAIgB,CAAC;AAEd,CAAC;AAAC5T,EAAA,CAzgBIjC,WAAiC;EAAA,QAmD/BP,cAAc;AAAA;AAAAqX,EAAA,GAnDhB9W,WAAiC;AA2gBvC,eAAeA,WAAW;AAAC,IAAA8W,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}