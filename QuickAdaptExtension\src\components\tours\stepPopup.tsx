import React, { useEffect, useState } from 'react';
import useDrawerS<PERSON>, { DrawerState } from '../../store/drawerStore';
import { Select, MenuItem, Typography } from "@mui/material";
import { ForkLeft } from '@mui/icons-material';
import {warning} from "../../assets/icons/icons";

const StepCreationPopup: React.FC<{ isOpen: boolean; onClose: () => void; onCreateStep:any,stepData:any,setStepData:any,isEditStepName:any,stepName:any,setStepName:any,editType:any,setEditType:any,editDescription:any,setDescription:any,setTemplateSelectionPopup:any}> = ({ isOpen, onClose,onCreateStep,stepData,setStepData,isEditStepName,stepName,setStepName,editType,setEditType,editDescription,setDescription,setTemplateSelectionPopup}) => {    const {
        setSelectedTemplate,
        setBannerPopup,
        setSelectedTemplateTour,
  selectedTemplateTour,
  selectedTemplate,
  steps,
  setSelectedStepTypeHotspot,
  selectedStepTypeHotspot,
  setBposition,
    } = useDrawerStore((state: DrawerState) => state);

  if (!isOpen) return null;
  const handleStepTypeChange = (e: React.ChangeEvent<{ value: unknown }>) => {
    const selectedType = e.target.value;
    setStepData((prevData: any) => ({
      ...prevData,
      type: selectedType,
    }));
    setEditType(selectedType); // update editType state for future reference
  };
  const handleCreate = () => {
    const updatedStepData =
    {
      ...stepData,
      type: stepData?.type||"Announcement",
      
      }

    // If creating a NEW Banner step in a Tour, set default position to Cover Top
    if (updatedStepData.type === "Banner" && selectedTemplate === "Tour") {
      // Only set default position for new banners
      setBposition("Cover Top");
    }

    onCreateStep(updatedStepData);
      onClose();
      setStepData("");
  };
  let isDuplicateStep = false;


  if (isEditStepName) {
    
    const originalStepName = stepData.stepNumber;
    
    isDuplicateStep = steps.some(step => 
      step.name === stepName && step.name !== originalStepName
    );
  } else {
    isDuplicateStep = steps.some(step => step.name === stepName);
    }

  const stepNameTrimmed = stepName ? stepName.trim() : '';
  const descriptionTrimmed = stepData.description ? stepData.description.replace(/\s+/g, '') : editDescription ? editDescription.replace(/\s+/g, '') : '';
  const isCreateButtonDisabled =
    !stepName || stepNameTrimmed.length < 3 || stepNameTrimmed.length > 50 || isDuplicateStep || descriptionTrimmed.length > 50 || stepNameTrimmed.length > 20;
  return (
    <div style={{
      width: '195px',
      backgroundColor: '#fff',
      borderRadius: '6px',
      boxShadow: '0px 4px 14px 0px #00000026',
      border: '1px solid #e0e0e0',
      zIndex: 1000,
      height: 'auto',
      overflow:'auto'
    }}>
      <div style={{
              padding: '10px 12px',
        // width: '171px',
        // maxHeight: '265px',    
      }}>
        <div>
        <div style={{
          textAlign: 'start',
          marginBottom:'4px'
        }}>
          <span style={{
            color: '#616365',
            lineHeight: '18px'  
          }}>Step Name</span>
        </div>

        <div className={`step-input ${isDuplicateStep ? 'qadpt-stbdr' : ''}`}>
          <input
            type="text"
            value={stepName}
            onChange={(e) => {
              const newStepName = e.target.value;
              //setStepData({ ...stepData, stepNumber: newStepName });
              setStepName(newStepName);
            }}
            onFocus={(e) => e.target.style.border = '1px solid #a8a8a8'}
            onBlur={(e) => e.target.style.border = '1px solid #a8a8a8'}
            style={{
              border: isDuplicateStep || stepNameTrimmed.length > 20 ? '1px solid red' : '1px solid #a8a8a8'
            }}
          />
        </div>

{/* Error message for duplicate step name */}
{isDuplicateStep && (
  <Typography
    style={{
      fontSize: "12px",
      color: "#e9a971",
      textAlign: "left",
      top: "100%",
      left: 0,
      marginBottom: "5px",
      display: "flex",
    }}
  >
    <span
      style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}
      dangerouslySetInnerHTML={{ __html: warning }}
    />
    Step name should be unique
  </Typography>
)}

        {stepName && stepNameTrimmed.length < 3 && (
  <Typography
  style={{
    fontSize: "12px",
    color: "#e9a971",
    textAlign: "left",
    top: "100%",
    left: 0,
    marginBottom: "5px",
    display: "flex",
  }}
>
  <span
    style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}
    dangerouslySetInnerHTML={{ __html: warning }}
  />
            Guide name must be at least 3 characters.
            </Typography>
          )}  
          {stepName && stepNameTrimmed.length > 20 && (

  <Typography
  style={{
    fontSize: "12px",
    color: "#e9a971",
    textAlign: "left",
    top: "100%",
    left: 0,
    marginBottom: "5px",
    display: "flex",
  }}
>
  <span
    style={{ display: "flex", fontSize: "12px", alignItems: "center", marginRight: "4px" }}
    dangerouslySetInnerHTML={{ __html: warning }}
  />
    Step name should not exceed 20 characters.
  </Typography>
)}
      </div>
        {selectedTemplate === "Tour" && (


          <div style={{
            marginBottom: '12px',
          }}>
            <div style={{
              textAlign: 'start',
              marginBottom: '4px'
            }}>
              <span style={{
                color: '#616365',
                lineHeight: '18px'
              }}>Step Type:</span>
            </div>
       
            
            <div>
              <Select
               value={stepData.type || editType || (selectedStepTypeHotspot === true ? "Hotspot" : "Announcement")}
                disabled={isEditStepName}
                onOpen={(e) => {
                  setTemplateSelectionPopup(true);
                }}
                onChange={(e) => {
                  setStepData({ ...stepData, type: e.target.value });
                  setEditType(e.target.value);
                  // If Banner is selected in a Tour, ensure position is set to Cover Top
                  if (e.target.value === "Banner" && selectedTemplate === "Tour") {
                    setBposition("Cover Top");
                  }
                }
                }
                onClose={(e) => {
                  setTemplateSelectionPopup(false);
                }}
                displayEmpty
                MenuProps={{
                  sx: {
                    zIndex: 9999999, 
                  },
                  PopoverClasses: {
                    root: 'qadpt-turstp', 
                  },
                }}
                sx={{
                  width: "171px",
                  padding: "6px 10px",
                  borderRadius: "6px",
                  fontSize: "14px",
                  height: "30px",
                  color: "#000",
                  background: "#fff",
                  outline: "none",
                  textAlign: "left",
                  minWidth: "100%",
                  "&:hover .MuiOutlinedInput-notchedOutline": { borderColor: "#a8a8a8" }, // Prevents color change on hover
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": { borderColor: "#a8a8a8" }, // Prevents focus color change
                  "& .MuiSelect-select": { paddingLeft: "0 !important" },
                 
                }}
              >
                <MenuItem value="Announcement">Announcement</MenuItem>
                <MenuItem value="Banner">Banner</MenuItem>
                <MenuItem value="Tooltip">Tooltip</MenuItem>
                 <MenuItem value="Hotspot">Hotspot</MenuItem>
              </Select>
            </div>
          

          </div>
        )}

<div style={{ marginBottom: '12px' }}>
  <div style={{ textAlign: 'start', marginBottom: '4px' }}>
    <span style={{ color: '#616365', lineHeight: '18px' }}>Description:</span>
  </div>

  <div className={`step-input ${descriptionTrimmed.length > 50 ? 'qadpt-stbdr' : ''}`}>
    <textarea
      placeholder={
        (!stepData.description && !editDescription) ? "write description" : ""
      }
      value={stepData.description || editDescription || ""}
      onChange={(e) => {
        setStepData({ ...stepData, description: e.target.value });
        setDescription(e.target.value);
      }}
      style={{
        width: "-webkit-fill-available",
        padding: "6px 10px",
        borderRadius: "6px",
        background: "#fff",
        outline: "none",
        textAlign: "left",
        minHeight: "60px",
      }}
    />

    {descriptionTrimmed.length > 50 && (
      <Typography
        style={{
          fontSize: "12px",
          color: "#e9a971",
          textAlign: "left",
          top: "100%",
          left: 0,
          marginBottom: "5px",
          display: "flex",
        }}
      >
        <span
          style={{
            display: "flex",
            fontSize: "12px",
            alignItems: "center",
            marginRight: "4px"
          }}
          dangerouslySetInnerHTML={{ __html: warning }}
        />
        Description must be a maximum of 50 characters.
      </Typography>
    )}
  </div>
</div>

       
      </div>
      <div style={{padding: "0 10px 10px 10px"}}>
      <button
          onClick={handleCreate}
          disabled={isCreateButtonDisabled}
          style={{
            width: '100%',
            padding: '7px 10px',
            backgroundColor: "var(--primarycolor)", // Change color when disabled
            opacity:isCreateButtonDisabled?"0.5":"1",
            color: '#fff',
            border: 'none',
            borderRadius: '7px',
            cursor: 'pointer',
            display:"block",
          }}
        >
          {isEditStepName ? "Update" : "Create"}
        </button>
        </div>
    </div>
  );
};

export default StepCreationPopup;

