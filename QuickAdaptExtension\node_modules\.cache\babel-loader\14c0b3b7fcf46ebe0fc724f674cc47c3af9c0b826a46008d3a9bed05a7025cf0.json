{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\guideSetting\\\\guideList\\\\PopupList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useContext } from \"react\";\nimport { Dialog, DialogContent, TextField, InputAdornment, IconButton, Tab, Tabs, Tooltip, DialogTitle, DialogContentText, DialogActions, Button, Typography } from \"@mui/material\";\nimport { DataGrid } from \"@mui/x-data-grid\";\nimport SearchIcon from \"@mui/icons-material/Search\";\nimport ClearIcon from \"@mui/icons-material/Clear\";\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\nimport \"./GuideMenuOptions.css\";\nimport AddIcon from \"@mui/icons-material/Add\";\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\nimport { AccountContext } from \"../../login/AccountContext\";\nimport { useSnackbar } from \"./SnackbarContext\";\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\nimport useDrawerStore from \"../../../store/drawerStore\";\nimport useUserSession from \"../../../store/userSession\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nlet editedguide;\nconst PopupModal = ({\n  Open,\n  onClose,\n  title,\n  searchText,\n  onAddClick\n}) => {\n  _s();\n  const {\n    setCurrentGuideId,\n    currentGuideId\n  } = useUserSession(state => state);\n  const {\n    setBannerPopup,\n    setOpenTooltip,\n    setElementSelected,\n    setBannerButtonSelected,\n    selectedTemplateTour,\n    isUnSavedChanges,\n    setIsUnSavedChanges,\n    openWarning,\n    setOpenWarning,\n    setEditClicked,\n    setActiveMenu,\n    setSearchText\n  } = useDrawerStore(state => state);\n  const [activeTab, setActiveTab] = useState(0);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [filteredData, setFilteredData] = useState([]);\n  const [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\n  const [cloneAnnouncementName, setCloneAnnouncementName] = useState(null);\n  const [guideIdToDelete, setGuideIdToDelete] = useState(null);\n  const [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\n  const [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\n  const [openDialog, setOpenDialog] = useState(false);\n  const [paginationModel, setPaginationModel] = useState({\n    page: 0,\n    pageSize: 15\n  });\n  const {\n    accountId\n  } = useContext(AccountContext);\n  const {\n    openSnackbar\n  } = useSnackbar();\n  const [totalCount, setTotalCount] = useState(0);\n  const [name, setName] = useState(\"Announcement\");\n  const handleEditClick = guide => {\n    setBannerButtonSelected(true);\n    setIsUnSavedChanges(false);\n    setEditClicked(true);\n    setOpenWarning(false);\n    let targetUrl = \"\";\n    editedguide = true;\n    if (guide.GuideType.toLowerCase() == \"announcement\" || guide.GuideType.toLowerCase() === \"tooltip\" || guide.GuideType.toLowerCase() === \"hotspot\" || guide.GuideType.toLowerCase() === \"tour\" || guide.GuideType.toLowerCase() === \"checklist\") {\n      if (guide.GuideType.toLowerCase() === \"tooltip\" || guide.GuideType.toLowerCase() === \"hotspot\" || guide.GuideType.toLowerCase() === \"banner\" || selectedTemplateTour === \"Tooltip\" || selectedTemplateTour === \"Banner\" || selectedTemplateTour === \"Hotspot\") {\n        setOpenTooltip(true);\n        setElementSelected(true);\n        let styleTag = document.getElementById(\"dynamic-body-style\");\n        const bodyElement = document.body;\n\n        // Add a dynamic class to the body\n        bodyElement.classList.add(\"dynamic-body-style\");\n        if (!styleTag) {\n          styleTag = document.createElement(\"style\");\n          styleTag.id = \"dynamic-body-style\";\n\n          // Add styles for body and nested elements\n          let styles = `\n\t\t\t\t\t\t.dynamic-body-style {\n\t\t\t\t\t\t\tpadding-top: 50px !important;\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t`;\n          styleTag.innerHTML = styles;\n          document.head.appendChild(styleTag);\n        }\n      }\n      targetUrl = `${guide === null || guide === void 0 ? void 0 : guide.TargetUrl}`;\n      if (targetUrl !== window.location.href) {\n        setCurrentGuideId(guide.GuideId);\n        window.open(targetUrl);\n      } else {\n        setCurrentGuideId(guide.GuideId);\n      }\n      return;\n    } else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\n      //targetUrl = `${guide?.TargetUrl}#bannerEdit`;\n      setCurrentGuideId(guide.GuideId);\n      setBannerPopup(true);\n    }\n    if (targetUrl) {\n      //onAddClick(guide.GuideType, true, guide);\n      window.open(targetUrl);\n    }\n  };\n  const handleCopyClick = announcement => {\n    setCloneAnnouncementName(announcement);\n    setIsCloneDialogOpen(true);\n  };\n  const handleDeleteConfirmation = guideId => {\n    setGuideIdToDelete(guideId);\n    setOpenDialog(true);\n  };\n  const handleKeyDown = event => {\n    if (event.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n  const columns = [{\n    field: \"Name\",\n    headerName: \"Name\",\n    // width: 300,\n    hideable: true,\n    resizable: false\n  }, {\n    field: \"UpdatedDate\",\n    headerName: \"Last Edited\",\n    // width: 250,\n    hideable: true,\n    renderCell: params => /*#__PURE__*/_jsxDEV(\"span\", {\n      children: [\" \", `${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 5\n    }, this),\n    resizable: false\n  }, {\n    field: \"actions\",\n    headerName: \"Actions\",\n    // width: 302,\n    hideable: true,\n    sortable: false,\n    renderCell: params => /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: \"Edit\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleEditClick(params.row),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: ListEditIcon\n            },\n            style: {\n              zoom: 0.7\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: \"Clone\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => handleCopyClick(params.row),\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: CopyListIcon\n            },\n            style: {\n              zoom: 0.7\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 214,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 6\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        arrow: true,\n        title: \"Delete\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: () => {\n            handleDeleteConfirmation(params.row.GuideId);\n            setGuideNametoDelete(params.row.Name);\n            setGuideTypetoDelete(params.row.GuideType);\n          },\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            dangerouslySetInnerHTML: {\n              __html: DeleteIconList\n            },\n            style: {\n              zoom: 0.7\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 8\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 6\n      }, this)]\n    }, void 0, true),\n    resizable: false\n  }];\n  const fetchAnnouncements = async () => {\n    var _data$results;\n    const {\n      page,\n      pageSize\n    } = paginationModel;\n    const offset = page * pageSize;\n    const statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\n    const filters = [{\n      FieldName: \"GuideType\",\n      ElementType: \"string\",\n      Condition: \"equals\",\n      Value: title === \"Product Tours\" ? \"Tour\" : title,\n      IsCustomField: false\n    }, {\n      FieldName: \"GuideStatus\",\n      ElementType: \"string\",\n      Condition: \"equals\",\n      Value: statusFilter,\n      IsCustomField: false\n    }, {\n      FieldName: \"Name\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: searchQuery,\n      IsCustomField: false\n    }, {\n      FieldName: \"AccountId\",\n      ElementType: \"string\",\n      Condition: \"contains\",\n      Value: accountId,\n      IsCustomField: false\n    }];\n    const data = await getAllGuides(offset, pageSize, filters, \"\");\n    const rowsWithIds = data === null || data === void 0 ? void 0 : (_data$results = data.results) === null || _data$results === void 0 ? void 0 : _data$results.map(item => ({\n      ...item,\n      id: item.GuideId\n    }));\n    setFilteredData(rowsWithIds || []);\n    setTotalCount(data === null || data === void 0 ? void 0 : data._count);\n  };\n  useEffect(() => {\n    if (Open || accountId) {\n      fetchAnnouncements();\n    }\n  }, [paginationModel, activeTab, Open, accountId]);\n\n  // useEffect(() => {\n  //     if (accountId) {\n  //       fetchAnnouncements();\n  //     }\n  //   }, [paginationModel, activeTab,accountId]);\n\n  const handleSearch = () => {\n    fetchAnnouncements();\n  };\n  useEffect(() => {\n    if (searchQuery.trim() === \"\") {\n      fetchAnnouncements();\n    }\n  }, [searchQuery]);\n  const handleClearSearch = () => {\n    setSearchQuery(\"\");\n    fetchAnnouncements();\n  };\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    setPaginationModel(prev => ({\n      ...prev,\n      page: 0\n    })); // Reset pagination when the tab changes\n  };\n  const getRowSpacing = React.useCallback(params => {\n    return {\n      top: params.isFirstVisible ? 0 : 5,\n      bottom: params.isLastVisible ? 0 : 5\n    };\n  }, []);\n  const handleDelete = async () => {\n    if (guideIdToDelete) {\n      try {\n        const response = await DeleteGuideByGuideId(guideIdToDelete);\n        if (response.Success) {\n          openSnackbar(`${GuidenametoDelete} ${GuideTypetoDelete} deleted Successfully`, \"success\");\n          await fetchAnnouncements();\n        } else {\n          openSnackbar(response.ErrorMessage, \"error\");\n        }\n      } catch (error) {}\n    }\n    setOpenDialog(false);\n    setGuideIdToDelete(null);\n    setGuideNametoDelete(\"\");\n  };\n  const handleCloneSuccess = async () => {\n    await fetchAnnouncements();\n  };\n  const getNoRowsLabel = () => {\n    const tabLabels = [\"Active\", \"Inactive\", \"Draft\"];\n    const currentTabLabel = tabLabels[activeTab] || searchText;\n    return `No ${currentTabLabel} ${searchText}s`;\n  };\n  const NoRowsOverlay = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      alignItems: \"center\",\n      flexDirection: \"column\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"qadpt-hotsicon\",\n      dangerouslySetInnerHTML: {\n        __html: NoData\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      sx: {\n        fontWeight: \"600\"\n      },\n      children: getNoRowsLabel()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 350,\n    columnNumber: 3\n  }, this);\n  const handleClosePopup = () => {\n    //if further ever need of closing popup when clicked outside the popup then please uncomment below code\n    // setActiveMenu(null);\n    // setSearchText(\"\");\n    // onClose();\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    id: \"popuplistmenu\",\n    children: [/*#__PURE__*/_jsxDEV(Dialog, {\n      slotProps: {\n        root: {\n          id: \"tooltipdialog\"\n        },\n        backdrop: {\n          sx: {\n            position: \"absolute !important\"\n          }\n        }\n      },\n      open: Open,\n      onClose: handleClosePopup,\n      fullWidth: true,\n      maxWidth: \"md\",\n      className: \"qadpt-gud-menupopup\",\n      children: /*#__PURE__*/_jsxDEV(DialogContent, {\n        className: \"qadpt-gud-menupopup-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-subhead\",\n          id: \"tablesubhead\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"title\",\n            style: {\n              fontWeight: \"600 !important\"\n            },\n            children: [searchText, \"s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-head\",\n          id: \"table-head\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"qadpt-titsection\",\n            children: [/*#__PURE__*/_jsxDEV(TextField, {\n              variant: \"outlined\",\n              placeholder: `Search ${title}`,\n              value: searchQuery,\n              onChange: e => {\n                const newValue = e.target.value;\n                setSearchQuery(newValue);\n                if (newValue === \"\") {\n                  handleClearSearch();\n                }\n              },\n              onKeyDown: e => {\n                if (e.key === \"Enter\") {\n                  handleSearch();\n                }\n              },\n              className: \"qadpt-extsearch\",\n              InputProps: {\n                sx: {\n                  \"&:hover .MuiOutlinedInput-notchedOutline\": {\n                    borderColor: \"#a8a8a8\"\n                  },\n                  // Prevents color change on hover\n                  \"&.Mui-focused .MuiOutlinedInput-notchedOutline\": {\n                    border: \"1px solid #a8a8a8\"\n                  }\n                },\n                startAdornment: /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"start\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"search\",\n                    onClick: () => handleSearch(),\n                    onMouseDown: event => event.preventDefault(),\n                    children: /*#__PURE__*/_jsxDEV(SearchIcon, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 443,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 437,\n                  columnNumber: 11\n                }, this),\n                endAdornment: searchQuery && /*#__PURE__*/_jsxDEV(InputAdornment, {\n                  position: \"end\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    \"aria-label\": \"clear\",\n                    onClick: () => {\n                      setSearchQuery(\"\");\n                      handleClearSearch();\n                    },\n                    children: /*#__PURE__*/_jsxDEV(ClearIcon, {\n                      sx: {\n                        zoom: \"1.2\"\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 456,\n                      columnNumber: 13\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 449,\n                    columnNumber: 12\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 448,\n                  columnNumber: 11\n                }, this)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"qadpt-right-part\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => onAddClick(searchText),\n                className: \"qadpt-memberButton\",\n                children: [/*#__PURE__*/_jsxDEV(AddIcon, {\n                  style: {\n                    marginRight: \"8px\",\n                    zoom: \"1.4\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 467,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: `Create ${searchText === \"Product Tours\" ? \"Tour\" : searchText}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 469,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-tabs-container\",\n          children: /*#__PURE__*/_jsxDEV(Tabs, {\n            value: activeTab,\n            onChange: handleTabChange,\n            children: [/*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Active\",\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"InActive\",\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(Tab, {\n              label: \"Draft\",\n              sx: {\n                backgroundColor: \"inherit !important\",\n                border: \"inherit !important\",\n                color: \"inherit !important\",\n                fontSize: \"14px !important\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"qadpt-webgird\",\n          children: /*#__PURE__*/_jsxDEV(DataGrid, {\n            rows: filteredData,\n            columns: columns,\n            getRowId: row => row.GuideId,\n            getRowSpacing: getRowSpacing,\n            pagination: true,\n            paginationModel: paginationModel,\n            paginationMode: \"server\",\n            onPaginationModelChange: setPaginationModel,\n            rowCount: totalCount,\n            pageSizeOptions: [15, 25, 50, 100],\n            localeText: {\n              MuiTablePagination: {\n                labelRowsPerPage: \"Records Per Page\"\n              },\n              noRowsLabel: getNoRowsLabel()\n            },\n            disableColumnMenu: true,\n            disableRowSelectionOnClick: true,\n            className: \"qadpt-grdcont\",\n            slots: {\n              noRowsOverlay: NoRowsOverlay // Using the 'slots' prop for NoRowsOverlay\n            },\n            sx: {\n              \"& .MuiDataGrid-row\": {\n                maxWidth: \"calc(100% - 30px)\",\n                \"--rowBorderColor\": \"transparent\"\n                //   marginTop: \"17px\",\n                // marginBottom:\"0 !important\"\n              },\n              \"& .MuiDataGrid-cell\": {\n                padding: \"0 15px !important\"\n              },\n              \".MuiTablePagination-toolbar\": {\n                display: \"flex !important\",\n                alignItems: \"baseline !important\"\n              },\n              \".MuiTablePagination-actions button\": {\n                border: \"none !important\",\n                color: \"inherit !important\",\n                backgroundColor: \"initial !important\",\n                \"&:hover\": {\n                  backgroundColor: \"initial !important\" // Hover background\n                }\n              },\n              \"& .MuiDataGrid-columnHeader\": {\n                background: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\n                padding: \"0 15px !important\",\n                borderRight: \"1px solid #f6eeee\",\n                height: \"40px !important\"\n              },\n              \"& .MuiDataGrid-columnHeaderTitle\": {\n                fontWeight: \"600\"\n              },\n              \"& .MuiDataGrid-filler\": {\n                backgroundColor: \"var(--ext-background)\",\n                \"--rowBorderColor\": \"transparent !important\"\n              },\n              \"& .MuiDataGrid-scrollbarFiller\": {\n                backgroundColor: \"var(--ext-background)\",\n                display: \"none\"\n              }\n            },\n            rowHeight: 38\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 510,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 385,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 4\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: openDialog,\n      onClose: () => setOpenDialog(false),\n      PaperProps: {\n        style: {\n          borderRadius: \"4px\",\n          maxWidth: \"400px\",\n          textAlign: \"center\",\n          maxHeight: \"300px\",\n          boxShadow: \"none\"\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        sx: {\n          padding: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            padding: \"10px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              backgroundColor: \"#e4b6b0\",\n              borderRadius: \"50%\",\n              width: \"40px\",\n              height: \"40px\",\n              display: \"flex\",\n              alignItems: \"center\",\n              justifyContent: \"center\"\n            },\n            children: /*#__PURE__*/_jsxDEV(DeleteOutlineOutlinedIcon, {\n              sx: {\n                color: \"#F44336\",\n                height: \"20px\",\n                width: \"20px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 605,\n              columnNumber: 8\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            fontSize: \"16px !important\",\n            fontWeight: 600,\n            padding: \"0 10px\"\n          },\n          children: [\"Delete \", GuideTypetoDelete]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        sx: {\n          padding: \"20px !important\"\n        },\n        children: /*#__PURE__*/_jsxDEV(DialogContentText, {\n          style: {\n            fontSize: \"14px\",\n            color: \"#000\"\n          },\n          children: [\"The \", /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontWeight: \"bold\"\n            },\n            children: GuidenametoDelete\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 11\n          }, this), \" cannot be restored once it is deleted.\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 613,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        sx: {\n          justifyContent: \"space-between\",\n          borderTop: \"1px solid var(--border-color)\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setOpenDialog(false),\n          sx: {\n            color: \"#9E9E9E\",\n            border: \"1px solid #9E9E9E\",\n            borderRadius: \"4px\",\n            textTransform: \"capitalize\",\n            padding: \"var(--button-padding)\",\n            lineHeight: \"var(--button-lineheight)\"\n          },\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 620,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleDelete,\n          sx: {\n            backgroundColor: \"var(--error-color)\",\n            color: \"#FFF\",\n            borderRadius: \"4px\",\n            textTransform: \"capitalize\",\n            padding: \"var(--button-padding)\",\n            lineHeight: \"var(--button-lineheight)\"\n            // \"&:hover\": {\n            // \tbackgroundColor: \"#D32F2F\",\n            // },\n          },\n          children: \"Delete\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 633,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 619,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 4\n    }, this), isCloneDialogOpen && cloneAnnouncementName && /*#__PURE__*/_jsxDEV(CloneInteractionDialog, {\n      open: isCloneDialogOpen,\n      handleClose: () => setIsCloneDialogOpen(false),\n      initialName: cloneAnnouncementName,\n      onCloneSuccess: handleCloneSuccess,\n      name: name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 652,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 367,\n    columnNumber: 3\n  }, this);\n};\n_s(PopupModal, \"kj/bZKP4ZWfwz89inVGHuMCfsrA=\", false, function () {\n  return [useUserSession, useDrawerStore, useSnackbar];\n});\n_c = PopupModal;\nexport { editedguide };\nexport default PopupModal;\nvar _c;\n$RefreshReg$(_c, \"PopupModal\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useContext", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TextField", "InputAdornment", "IconButton", "Tab", "Tabs", "<PERSON><PERSON><PERSON>", "DialogTitle", "DialogContentText", "DialogActions", "<PERSON><PERSON>", "Typography", "DataGrid", "SearchIcon", "ClearIcon", "getAllGuides", "DeleteGuideByGuideId", "ListEditIcon", "CopyListIcon", "DeleteIconList", "NoData", "DeleteOutlineOutlinedIcon", "AddIcon", "CloneInteractionDialog", "AccountContext", "useSnackbar", "formatDateTime", "useDrawerStore", "useUserSession", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "editedguide", "PopupModal", "Open", "onClose", "title", "searchText", "onAddClick", "_s", "setCurrentGuideId", "currentGuideId", "state", "setBannerPopup", "setOpenTooltip", "setElementSelected", "setBannerButtonSelected", "selectedTemplateTour", "isUnSavedChanges", "setIsUnSavedChanges", "openWarning", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditClicked", "setActiveMenu", "setSearchText", "activeTab", "setActiveTab", "searchQuery", "setSearch<PERSON>uery", "filteredData", "setFilteredData", "isCloneDialogOpen", "setIsCloneDialogOpen", "cloneAnnouncementName", "setCloneAnnouncementName", "guideIdToDelete", "setGuideIdToDelete", "GuidenametoDelete", "setGuideNametoDelete", "GuideTypetoDelete", "setGuideTypetoDelete", "openDialog", "setOpenDialog", "paginationModel", "setPaginationModel", "page", "pageSize", "accountId", "openSnackbar", "totalCount", "setTotalCount", "name", "setName", "handleEditClick", "guide", "targetUrl", "GuideType", "toLowerCase", "styleTag", "document", "getElementById", "bodyElement", "body", "classList", "add", "createElement", "id", "styles", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "TargetUrl", "window", "location", "href", "GuideId", "open", "handleCopyClick", "announcement", "handleDeleteConfirmation", "guideId", "handleKeyDown", "event", "key", "handleSearch", "columns", "field", "headerName", "hideable", "resizable", "renderCell", "params", "children", "row", "UpdatedDate", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sortable", "arrow", "onClick", "dangerouslySetInnerHTML", "__html", "style", "zoom", "Name", "fetchAnnouncements", "_data$results", "offset", "statusFilter", "filters", "FieldName", "ElementType", "Condition", "Value", "IsCustomField", "data", "rowsWithIds", "results", "map", "item", "_count", "trim", "handleClearSearch", "handleTabChange", "newValue", "prev", "getRowSpacing", "useCallback", "top", "isFirstVisible", "bottom", "isLastVisible", "handleDelete", "response", "Success", "ErrorMessage", "error", "handleCloneSuccess", "getNoRowsLabel", "tabLabels", "currentTabLabel", "NoRowsOverlay", "display", "alignItems", "flexDirection", "className", "sx", "fontWeight", "handleClosePopup", "slotProps", "root", "backdrop", "position", "fullWidth", "max<PERSON><PERSON><PERSON>", "variant", "placeholder", "value", "onChange", "e", "target", "onKeyDown", "InputProps", "borderColor", "border", "startAdornment", "onMouseDown", "preventDefault", "endAdornment", "marginRight", "label", "backgroundColor", "color", "fontSize", "rows", "getRowId", "pagination", "paginationMode", "onPaginationModelChange", "rowCount", "pageSizeOptions", "localeText", "MuiTablePagination", "labelRowsPerPage", "noRowsLabel", "disableColumnMenu", "disableRowSelectionOnClick", "slots", "noRowsOverlay", "padding", "background", "borderRight", "height", "rowHeight", "PaperProps", "borderRadius", "textAlign", "maxHeight", "boxShadow", "justifyContent", "width", "borderTop", "textTransform", "lineHeight", "handleClose", "initialName", "onCloneSuccess", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/guideSetting/guideList/PopupList.tsx"], "sourcesContent": ["import React, { useState, useEffect, useContext } from \"react\";\r\nimport {\r\n\t<PERSON><PERSON>,\r\n\tDialogContent,\r\n\tTextField,\r\n\tInputAdornment,\r\n\tIconButton,\r\n\tTab,\r\n\tTabs,\r\n\tTooltip,\r\n\tDialogTitle,\r\n\tDialogContentText,\r\n\tDialogActions,\r\n\tButton,\r\n\tTypography,\r\n} from \"@mui/material\";\r\nimport { DataGrid, GridColDef, GridRenderCellParams, GridRowSpacingParams } from \"@mui/x-data-grid\";\r\nimport SearchIcon from \"@mui/icons-material/Search\";\r\nimport ClearIcon from \"@mui/icons-material/Clear\";\r\n\r\nimport { getAllGuides, DeleteGuideByGuideId } from \"../../../services/GuideListServices\";\r\nimport { ListEditIcon, CopyListIcon, DeleteIconList, NoData } from \"../../../assets/icons/icons\";\r\nimport DeleteOutlineOutlinedIcon from \"@mui/icons-material/DeleteOutlineOutlined\";\r\nimport \"./GuideMenuOptions.css\";\r\nimport AddIcon from \"@mui/icons-material/Add\";\r\nimport CloneInteractionDialog from \"./CloneGuidePopUp\";\r\nimport { AccountContext } from \"../../login/AccountContext\";\r\nimport { useSnackbar } from \"./SnackbarContext\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport { formatDateTime } from \"../../guideSetting/guideList/TimeZoneConversion\";\r\nimport useDrawerStore from \"../../../store/drawerStore\";\r\nimport useUserSession from \"../../../store/userSession\";\r\nlet editedguide: any;\r\ninterface PopupModalProps {\r\n\ttitle: string;\r\n\tOpen: boolean;\r\n\tonClose: () => void;\r\n\tsearchText: string;\r\n\tonAddClick: (searchText: string, isEditing?: boolean, guideDetails?: any) => void;\r\n}\r\ninterface Announcement {\r\n\tAccountId: string;\r\n\tContent: string;\r\n\tCreatedBy: string;\r\n\tCreatedDate: string;\r\n\tFrequency: string;\r\n\tGuideId: string;\r\n\tGuideStatus: string;\r\n\tGuideType: string;\r\n\tName: string;\r\n\tOrganizationId: string;\r\n\tSegment: string;\r\n\tTargetUrl: string;\r\n\tTemplateId: string;\r\n\tUpdatedBy: string;\r\n\tUpdatedDate: string;\r\n}\r\n\r\nconst PopupModal: React.FC<PopupModalProps> = ({ Open, onClose, title, searchText, onAddClick }) => {\r\n\tconst { setCurrentGuideId, currentGuideId } = useUserSession((state) => state);\r\n\tconst {\r\n\t\tsetBannerPopup,\r\n\t\tsetOpenTooltip,\r\n\t\tsetElementSelected,\r\n\t\tsetBannerButtonSelected,\r\n\t\tselectedTemplateTour,\r\n\t\tisUnSavedChanges,\r\n\t\tsetIsUnSavedChanges,\r\n\t\topenWarning,\r\n\t\tsetOpenWarning,\r\n\t\tsetEditClicked,\r\n\t\tsetActiveMenu,\r\n\t\tsetSearchText,\r\n\t} = useDrawerStore((state) => state);\r\n\tconst [activeTab, setActiveTab] = useState(0);\r\n\tconst [searchQuery, setSearchQuery] = useState(\"\");\r\n\tconst [filteredData, setFilteredData] = useState<any[]>([]);\r\n\tconst [isCloneDialogOpen, setIsCloneDialogOpen] = useState(false);\r\n\tconst [cloneAnnouncementName, setCloneAnnouncementName] = useState<Announcement | null>(null);\r\n\tconst [guideIdToDelete, setGuideIdToDelete] = useState<string | null>(null);\r\n\tconst [GuidenametoDelete, setGuideNametoDelete] = useState(\"\");\r\n\tconst [GuideTypetoDelete, setGuideTypetoDelete] = useState(\"\");\r\n\tconst [openDialog, setOpenDialog] = useState(false);\r\n\tconst [paginationModel, setPaginationModel] = useState({\r\n\t\tpage: 0,\r\n\t\tpageSize: 15,\r\n\t});\r\n\tconst { accountId } = useContext(AccountContext);\r\n\tconst { openSnackbar } = useSnackbar();\r\n\tconst [totalCount, setTotalCount] = useState(0);\r\n\tconst [name, setName] = useState(\"Announcement\");\r\n\tconst handleEditClick = (guide: Announcement) => {\r\n\t\tsetBannerButtonSelected(true);\r\n\t\tsetIsUnSavedChanges(false);\r\n\t\tsetEditClicked(true);\r\n\t\tsetOpenWarning(false);\r\n\t\tlet targetUrl = \"\";\r\n\t\teditedguide = true;\r\n\t\tif (\r\n\t\t\tguide.GuideType.toLowerCase() == \"announcement\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"tour\" ||\r\n\t\t\tguide.GuideType.toLowerCase() === \"checklist\"\r\n\t\t) {\r\n\t\t\tif (\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"tooltip\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"hotspot\" ||\r\n\t\t\t\tguide.GuideType.toLowerCase() === \"banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Tooltip\" ||\r\n\t\t\t\tselectedTemplateTour === \"Banner\" ||\r\n\t\t\t\tselectedTemplateTour === \"Hotspot\"\r\n\t\t\t) {\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\tsetElementSelected(true);\r\n\t\t\t\tlet styleTag = document.getElementById(\"dynamic-body-style\") as HTMLStyleElement;\r\n\t\t\t\tconst bodyElement = document.body;\r\n\r\n\t\t\t\t// Add a dynamic class to the body\r\n\t\t\t\tbodyElement.classList.add(\"dynamic-body-style\");\r\n\r\n\t\t\t\tif (!styleTag) {\r\n\t\t\t\t\tstyleTag = document.createElement(\"style\");\r\n\t\t\t\t\tstyleTag.id = \"dynamic-body-style\";\r\n\r\n\t\t\t\t\t// Add styles for body and nested elements\r\n\t\t\t\t\tlet styles = `\r\n\t\t\t\t\t\t.dynamic-body-style {\r\n\t\t\t\t\t\t\tpadding-top: 50px !important;\r\n\t\t\t\t\t\t\tmax-height:calc(100% - 55px);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t`;\r\n\r\n\t\t\t\t\tstyleTag.innerHTML = styles;\r\n\t\t\t\t\tdocument.head.appendChild(styleTag);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\ttargetUrl = `${guide?.TargetUrl}`;\r\n\t\t\tif (targetUrl !== window.location.href) {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t\twindow.open(targetUrl);\r\n\t\t\t} else {\r\n\t\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t} else if (guide.GuideType.toLowerCase() == \"banner\" || selectedTemplateTour === \"Banner\") {\r\n\t\t\t//targetUrl = `${guide?.TargetUrl}#bannerEdit`;\r\n\t\t\tsetCurrentGuideId(guide.GuideId);\r\n\t\t\tsetBannerPopup(true);\r\n\t\t}\r\n\t\tif (targetUrl) {\r\n\t\t\t//onAddClick(guide.GuideType, true, guide);\r\n\t\t\twindow.open(targetUrl);\r\n\t\t}\r\n\t};\r\n\t\r\n\tconst handleCopyClick = (announcement: Announcement) => {\r\n\t\tsetCloneAnnouncementName(announcement);\r\n\t\tsetIsCloneDialogOpen(true);\r\n\t};\r\n\tconst handleDeleteConfirmation = (guideId: string) => {\r\n\t\tsetGuideIdToDelete(guideId);\r\n\t\tsetOpenDialog(true);\r\n\t};\r\n\tconst handleKeyDown = (event: React.KeyboardEvent) => {\r\n\t\tif (event.key === \"Enter\") {\r\n\t\t\thandleSearch();\r\n\t\t}\r\n\t};\r\n\tconst columns: GridColDef[] = [\r\n\t\t{\r\n\t\t\tfield: \"Name\",\r\n\t\t\theaderName: \"Name\",\r\n\t\t\t// width: 300,\r\n\t\t\thideable: true,\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"UpdatedDate\",\r\n\t\t\theaderName: \"Last Edited\",\r\n\t\t\t// width: 250,\r\n\t\t\thideable: true,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<span> {`${formatDateTime(params.row.UpdatedDate, \"dd-MM-yyyy\")}`}</span>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t\t{\r\n\t\t\tfield: \"actions\",\r\n\t\t\theaderName: \"Actions\",\r\n\t\t\t// width: 302,\r\n\t\t\thideable: true,\r\n\t\t\tsortable: false,\r\n\t\t\trenderCell: (params: GridRenderCellParams) => (\r\n\t\t\t\t<>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Edit\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleEditClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: ListEditIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Clone\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton onClick={() => handleCopyClick(params.row)}>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: CopyListIcon }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t\t<Tooltip\r\n\t\t\t\t\t\tarrow\r\n\t\t\t\t\t\ttitle=\"Delete\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\thandleDeleteConfirmation(params.row.GuideId);\r\n\t\t\t\t\t\t\t\tsetGuideNametoDelete(params.row.Name);\r\n\t\t\t\t\t\t\t\tsetGuideTypetoDelete(params.row.GuideType);\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={{ __html: DeleteIconList }}\r\n\t\t\t\t\t\t\t\tstyle={{ zoom: 0.7 }}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t</Tooltip>\r\n\t\t\t\t</>\r\n\t\t\t),\r\n\t\t\tresizable: false,\r\n\t\t},\r\n\t];\r\n\r\n\tconst fetchAnnouncements = async () => {\r\n\t\tconst { page, pageSize } = paginationModel;\r\n\t\tconst offset = page * pageSize;\r\n\t\tconst statusFilter = activeTab === 0 ? \"Active\" : activeTab === 1 ? \"InActive\" : \"Draft\";\r\n\r\n\t\tconst filters = [\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideType\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: title === \"Product Tours\" ? \"Tour\" : title,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"GuideStatus\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"equals\",\r\n\t\t\t\tValue: statusFilter,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"Name\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: searchQuery,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\tFieldName: \"AccountId\",\r\n\t\t\t\tElementType: \"string\",\r\n\t\t\t\tCondition: \"contains\",\r\n\t\t\t\tValue: accountId,\r\n\t\t\t\tIsCustomField: false,\r\n\t\t\t},\r\n\t\t];\r\n\t\tconst data = await getAllGuides(offset, pageSize, filters, \"\");\r\n\t\tconst rowsWithIds = data?.results?.map((item: any) => ({\r\n\t\t\t...item,\r\n\t\t\tid: item.GuideId,\r\n\t\t}));\r\n\r\n\t\tsetFilteredData(rowsWithIds || []);\r\n\t\tsetTotalCount(data?._count);\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (Open || accountId) {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [paginationModel, activeTab, Open, accountId]);\r\n\r\n\t// useEffect(() => {\r\n\t//     if (accountId) {\r\n\t//       fetchAnnouncements();\r\n\t//     }\r\n\t//   }, [paginationModel, activeTab,accountId]);\r\n\r\n\tconst handleSearch = () => {\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tuseEffect(() => {\r\n\t\tif (searchQuery.trim() === \"\") {\r\n\t\t\tfetchAnnouncements();\r\n\t\t}\r\n\t}, [searchQuery]);\r\n\tconst handleClearSearch = () => {\r\n\t\tsetSearchQuery(\"\");\r\n\t\tfetchAnnouncements();\r\n\t};\r\n\r\n\tconst handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\r\n\t\tsetActiveTab(newValue);\r\n\t\tsetPaginationModel((prev) => ({ ...prev, page: 0 })); // Reset pagination when the tab changes\r\n\t};\r\n\tconst getRowSpacing = React.useCallback((params: GridRowSpacingParams) => {\r\n\t\treturn {\r\n\t\t\ttop: params.isFirstVisible ? 0 : 5,\r\n\t\t\tbottom: params.isLastVisible ? 0 : 5,\r\n\t\t};\r\n\t}, []);\r\n\r\n\tconst handleDelete = async () => {\r\n\t\tif (guideIdToDelete) {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await DeleteGuideByGuideId(guideIdToDelete);\r\n\t\t\t\tif (response.Success) {\r\n\t\t\t\t\topenSnackbar(`${GuidenametoDelete} ${GuideTypetoDelete} deleted Successfully`, \"success\");\r\n\t\t\t\t\tawait fetchAnnouncements();\r\n\t\t\t\t} else {\r\n\t\t\t\t\topenSnackbar(response.ErrorMessage, \"error\");\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {}\r\n\t\t}\r\n\t\tsetOpenDialog(false);\r\n\t\tsetGuideIdToDelete(null);\r\n\t\tsetGuideNametoDelete(\"\");\r\n\t};\r\n\tconst handleCloneSuccess = async () => {\r\n\t\tawait fetchAnnouncements();\r\n\t};\r\n\tconst getNoRowsLabel = () => {\r\n\t\tconst tabLabels = [\"Active\", \"Inactive\", \"Draft\"];\r\n\t\tconst currentTabLabel = tabLabels[activeTab] || searchText;\r\n\t\treturn `No ${currentTabLabel} ${searchText}s`;\r\n\t};\r\n\tconst NoRowsOverlay = () => (\r\n\t\t<div style={{ display: \"flex\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n\t\t\t<span\r\n\t\t\t\tclassName=\"qadpt-hotsicon\"\r\n\t\t\t\tdangerouslySetInnerHTML={{ __html: NoData }}\r\n\t\t\t/>\r\n\t\t\t<Typography sx={{ fontWeight: \"600\" }}>{getNoRowsLabel()}</Typography>\r\n\t\t</div>\r\n\t);\r\n\r\n\tconst handleClosePopup = () => {\r\n\t\t//if further ever need of closing popup when clicked outside the popup then please uncomment below code\r\n\t\t// setActiveMenu(null);\r\n\t\t// setSearchText(\"\");\r\n\t\t// onClose();\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div id=\"popuplistmenu\">\r\n\t\t\t<Dialog\r\n\t\t\t\tslotProps={{\r\n\t\t\t\t\troot: {\r\n\t\t\t\t\t\tid: \"tooltipdialog\",\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbackdrop: {\r\n\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\tposition: \"absolute !important\",\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t\topen={Open}\r\n\t\t\t\tonClose={handleClosePopup}\r\n\t\t\t\tfullWidth\r\n\t\t\t\tmaxWidth=\"md\"\r\n\t\t\t\tclassName=\"qadpt-gud-menupopup\"\r\n\t\t\t>\r\n\t\t\t\t<DialogContent className=\"qadpt-gud-menupopup-content\">\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-subhead\"\r\n\t\t\t\t\t\tid=\"tablesubhead\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\tclassName=\"title\"\r\n\t\t\t\t\t\t\tstyle={{ fontWeight: \"600 !important\" }}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{searchText}s\r\n\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t{/* <IconButton\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\t\tborderWidth: \"1px\",\r\n\t\t\t\t\t\t\t\tborderStyle: \"solid\",\r\n\t\t\t\t\t\t\t\tpadding: \"5px\",\r\n\t\t\t\t\t\t\t\tborderColor: \"var(--border-color)\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={onClose}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<CloseIcon style={{ color: \"#000\" }} />\r\n\t\t\t\t\t\t</IconButton> */}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName=\"qadpt-head\"\r\n\t\t\t\t\t\tid=\"table-head\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<div className=\"qadpt-titsection\">\r\n\t\t\t\t\t\t\t<TextField\r\n\t\t\t\t\t\t\t\tvariant=\"outlined\"\r\n\t\t\t\t\t\t\t\tplaceholder={`Search ${title}`}\r\n\t\t\t\t\t\t\t\tvalue={searchQuery}\r\n\t\t\t\t\t\t\t\tonChange={(e) => {\r\n\t\t\t\t\t\t\t\t\tconst newValue = e.target.value;\r\n\t\t\t\t\t\t\t\t\tsetSearchQuery(newValue);\r\n\t\t\t\t\t\t\t\t\tif (newValue === \"\") {\r\n\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tonKeyDown={(e) => {\r\n\t\t\t\t\t\t\t\t\tif (e.key === \"Enter\") {\r\n\t\t\t\t\t\t\t\t\t\thandleSearch();\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\tclassName=\"qadpt-extsearch\"\r\n\t\t\t\t\t\t\t\tInputProps={{\r\n\t\t\t\t\t\t\t\t\tsx: {\r\n\t\t\t\t\t\t\t\t\t\t\"&:hover .MuiOutlinedInput-notchedOutline\": { borderColor: \"#a8a8a8\" }, // Prevents color change on hover\r\n\t\t\t\t\t\t\t\t\t\t\"&.Mui-focused .MuiOutlinedInput-notchedOutline\": { border: \"1px solid #a8a8a8\" },\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tstartAdornment: (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"start\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"search\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleSearch()}\r\n\t\t\t\t\t\t\t\t\t\t\t\tonMouseDown={(event) => event.preventDefault()}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<SearchIcon />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t\tendAdornment: searchQuery && (\r\n\t\t\t\t\t\t\t\t\t\t<InputAdornment position=\"end\">\r\n\t\t\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\t\t\taria-label=\"clear\"\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsetSearchQuery(\"\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\thandleClearSearch();\r\n\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<ClearIcon sx={{ zoom: \"1.2\" }} />\r\n\t\t\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t\t\t</InputAdornment>\r\n\t\t\t\t\t\t\t\t\t),\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<div className=\"qadpt-right-part\">\r\n\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\tonClick={() => onAddClick(searchText)}\r\n\t\t\t\t\t\t\t\t\tclassName=\"qadpt-memberButton\"\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<AddIcon style={{ marginRight: \"8px\", zoom: \"1.4\" }} />\r\n\t\t\t\t\t\t\t\t\t{/* <i className=\"fal fa-add-plus\"></i> */}\r\n\t\t\t\t\t\t\t\t\t<span>{`Create ${searchText === \"Product Tours\" ? \"Tour\" : searchText}`}</span>\r\n\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-tabs-container\">\r\n\t\t\t\t\t\t<Tabs\r\n\t\t\t\t\t\t\tvalue={activeTab}\r\n\t\t\t\t\t\t\tonChange={handleTabChange}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"Active\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"InActive\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<Tab\r\n\t\t\t\t\t\t\t\tlabel=\"Draft\"\r\n\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tborder: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tfontSize: \"14px !important\",\r\n\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</Tabs>\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t<div className=\"qadpt-webgird\">\r\n\t\t\t\t\t\t<DataGrid\r\n\t\t\t\t\t\t\trows={filteredData}\r\n\t\t\t\t\t\t\tcolumns={columns}\r\n\t\t\t\t\t\t\tgetRowId={(row) => row.GuideId}\r\n\t\t\t\t\t\t\tgetRowSpacing={getRowSpacing}\r\n\t\t\t\t\t\t\tpagination\r\n\t\t\t\t\t\t\tpaginationModel={paginationModel}\r\n\t\t\t\t\t\t\tpaginationMode=\"server\"\r\n\t\t\t\t\t\t\tonPaginationModelChange={setPaginationModel}\r\n\t\t\t\t\t\t\trowCount={totalCount}\r\n\t\t\t\t\t\t\tpageSizeOptions={[15, 25, 50, 100]}\r\n\t\t\t\t\t\t\tlocaleText={{\r\n\t\t\t\t\t\t\t\tMuiTablePagination: {\r\n\t\t\t\t\t\t\t\t\tlabelRowsPerPage: \"Records Per Page\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\tnoRowsLabel: getNoRowsLabel(),\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableColumnMenu\r\n\t\t\t\t\t\t\tdisableRowSelectionOnClick\r\n\t\t\t\t\t\t\tclassName=\"qadpt-grdcont\"\r\n\t\t\t\t\t\t\tslots={{\r\n\t\t\t\t\t\t\t\tnoRowsOverlay: NoRowsOverlay, // Using the 'slots' prop for NoRowsOverlay\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-row\": {\r\n\t\t\t\t\t\t\t\t\tmaxWidth: \"calc(100% - 30px)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent\",\r\n\t\t\t\t\t\t\t\t\t//   marginTop: \"17px\",\r\n\t\t\t\t\t\t\t\t\t// marginBottom:\"0 !important\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-cell\": {\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-toolbar\": {\r\n\t\t\t\t\t\t\t\t\tdisplay: \"flex !important\",\r\n\t\t\t\t\t\t\t\t\talignItems: \"baseline !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\".MuiTablePagination-actions button\": {\r\n\t\t\t\t\t\t\t\t\tborder: \"none !important\",\r\n\t\t\t\t\t\t\t\t\tcolor: \"inherit !important\",\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\",\r\n\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\tbackgroundColor: \"initial !important\", // Hover background\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeader\": {\r\n\t\t\t\t\t\t\t\t\tbackground: \"linear-gradient(to right, #f6eeee, #f6eeee)\",\r\n\t\t\t\t\t\t\t\t\tpadding: \"0 15px !important\",\r\n\t\t\t\t\t\t\t\t\tborderRight: \"1px solid #f6eeee\",\r\n\t\t\t\t\t\t\t\t\theight: \"40px !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-columnHeaderTitle\": {\r\n\t\t\t\t\t\t\t\t\tfontWeight: \"600\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-filler\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\t\"--rowBorderColor\": \"transparent !important\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"& .MuiDataGrid-scrollbarFiller\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: \"var(--ext-background)\",\r\n\t\t\t\t\t\t\t\t\tdisplay: \"none\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\trowHeight={38}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</DialogContent>\r\n\t\t\t</Dialog>\r\n\t\t\t<Dialog\r\n\t\t\t\topen={openDialog}\r\n\t\t\t\tonClose={() => setOpenDialog(false)}\r\n\t\t\t\tPaperProps={{\r\n\t\t\t\t\tstyle: {\r\n\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\tmaxWidth: \"400px\",\r\n\t\t\t\t\t\ttextAlign: \"center\",\r\n\t\t\t\t\t\tmaxHeight: \"300px\",\r\n\t\t\t\t\t\tboxShadow: \"none\",\r\n\t\t\t\t\t},\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t<DialogTitle sx={{ padding: 0 }}>\r\n\t\t\t\t\t<div style={{ display: \"flex\", justifyContent: \"center\", padding: \"10px\" }}>\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"#e4b6b0\",\r\n\t\t\t\t\t\t\t\tborderRadius: \"50%\",\r\n\t\t\t\t\t\t\t\twidth: \"40px\",\r\n\t\t\t\t\t\t\t\theight: \"40px\",\r\n\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\talignItems: \"center\",\r\n\t\t\t\t\t\t\t\tjustifyContent: \"center\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<DeleteOutlineOutlinedIcon sx={{ color: \"#F44336\", height: \"20px\", width: \"20px\" }} />\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<Typography sx={{ fontSize: \"16px !important\", fontWeight: 600, padding: \"0 10px\" }}>\r\n\t\t\t\t\t\tDelete {GuideTypetoDelete}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</DialogTitle>\r\n\r\n\t\t\t\t<DialogContent sx={{ padding: \"20px !important\" }}>\r\n\t\t\t\t\t<DialogContentText style={{ fontSize: \"14px\", color: \"#000\" }}>\r\n\t\t\t\t\t\tThe <span style={{ fontWeight: \"bold\" }}>{GuidenametoDelete}</span> cannot be restored once it is deleted.\r\n\t\t\t\t\t</DialogContentText>\r\n\t\t\t\t</DialogContent>\r\n\r\n\t\t\t\t<DialogActions sx={{ justifyContent: \"space-between\", borderTop: \"1px solid var(--border-color)\" }}>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={() => setOpenDialog(false)}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tcolor: \"#9E9E9E\",\r\n\t\t\t\t\t\t\tborder: \"1px solid #9E9E9E\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tCancel\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t\t<Button\r\n\t\t\t\t\t\tonClick={handleDelete}\r\n\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\tbackgroundColor: \"var(--error-color)\",\r\n\t\t\t\t\t\t\tcolor: \"#FFF\",\r\n\t\t\t\t\t\t\tborderRadius: \"4px\",\r\n\t\t\t\t\t\t\ttextTransform: \"capitalize\",\r\n\t\t\t\t\t\t\tpadding: \"var(--button-padding)\",\r\n\t\t\t\t\t\t\tlineHeight: \"var(--button-lineheight)\",\r\n\t\t\t\t\t\t\t// \"&:hover\": {\r\n\t\t\t\t\t\t\t// \tbackgroundColor: \"#D32F2F\",\r\n\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\tDelete\r\n\t\t\t\t\t</Button>\r\n\t\t\t\t</DialogActions>\r\n\t\t\t</Dialog>\r\n\t\t\t{isCloneDialogOpen && cloneAnnouncementName && (\r\n\t\t\t\t<CloneInteractionDialog\r\n\t\t\t\t\topen={isCloneDialogOpen}\r\n\t\t\t\t\thandleClose={() => setIsCloneDialogOpen(false)}\r\n\t\t\t\t\tinitialName={cloneAnnouncementName}\r\n\t\t\t\t\tonCloneSuccess={handleCloneSuccess}\r\n\t\t\t\t\tname={name}\r\n\t\t\t\t/>\r\n\t\t\t)}\r\n\t\t</div>\r\n\t);\r\n};\r\nexport { editedguide };\r\nexport default PopupModal;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;AAC9D,SACCC,MAAM,EACNC,aAAa,EACbC,SAAS,EACTC,cAAc,EACdC,UAAU,EACVC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,iBAAiB,EACjBC,aAAa,EACbC,MAAM,EACNC,UAAU,QACJ,eAAe;AACtB,SAASC,QAAQ,QAAgE,kBAAkB;AACnG,OAAOC,UAAU,MAAM,4BAA4B;AACnD,OAAOC,SAAS,MAAM,2BAA2B;AAEjD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,qCAAqC;AACxF,SAASC,YAAY,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,QAAQ,6BAA6B;AAChG,OAAOC,yBAAyB,MAAM,2CAA2C;AACjF,OAAO,wBAAwB;AAC/B,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,sBAAsB,MAAM,mBAAmB;AACtD,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,SAASC,WAAW,QAAQ,mBAAmB;AAE/C,SAASC,cAAc,QAAQ,iDAAiD;AAChF,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACxD,IAAIC,WAAgB;AA0BpB,MAAMC,UAAqC,GAAGA,CAAC;EAAEC,IAAI;EAAEC,OAAO;EAAEC,KAAK;EAAEC,UAAU;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACnG,MAAM;IAAEC,iBAAiB;IAAEC;EAAe,CAAC,GAAGd,cAAc,CAAEe,KAAK,IAAKA,KAAK,CAAC;EAC9E,MAAM;IACLC,cAAc;IACdC,cAAc;IACdC,kBAAkB;IAClBC,uBAAuB;IACvBC,oBAAoB;IACpBC,gBAAgB;IAChBC,mBAAmB;IACnBC,WAAW;IACXC,cAAc;IACdC,cAAc;IACdC,aAAa;IACbC;EACD,CAAC,GAAG5B,cAAc,CAAEgB,KAAK,IAAKA,KAAK,CAAC;EACpC,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAG7D,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC8D,WAAW,EAAEC,cAAc,CAAC,GAAG/D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACgE,YAAY,EAAEC,eAAe,CAAC,GAAGjE,QAAQ,CAAQ,EAAE,CAAC;EAC3D,MAAM,CAACkE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACoE,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGrE,QAAQ,CAAsB,IAAI,CAAC;EAC7F,MAAM,CAACsE,eAAe,EAAEC,kBAAkB,CAAC,GAAGvE,QAAQ,CAAgB,IAAI,CAAC;EAC3E,MAAM,CAACwE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGzE,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC0E,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3E,QAAQ,CAAC,EAAE,CAAC;EAC9D,MAAM,CAAC4E,UAAU,EAAEC,aAAa,CAAC,GAAG7E,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC8E,eAAe,EAAEC,kBAAkB,CAAC,GAAG/E,QAAQ,CAAC;IACtDgF,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACX,CAAC,CAAC;EACF,MAAM;IAAEC;EAAU,CAAC,GAAGhF,UAAU,CAAC0B,cAAc,CAAC;EAChD,MAAM;IAAEuD;EAAa,CAAC,GAAGtD,WAAW,CAAC,CAAC;EACtC,MAAM,CAACuD,UAAU,EAAEC,aAAa,CAAC,GAAGrF,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACsF,IAAI,EAAEC,OAAO,CAAC,GAAGvF,QAAQ,CAAC,cAAc,CAAC;EAChD,MAAMwF,eAAe,GAAIC,KAAmB,IAAK;IAChDtC,uBAAuB,CAAC,IAAI,CAAC;IAC7BG,mBAAmB,CAAC,KAAK,CAAC;IAC1BG,cAAc,CAAC,IAAI,CAAC;IACpBD,cAAc,CAAC,KAAK,CAAC;IACrB,IAAIkC,SAAS,GAAG,EAAE;IAClBrD,WAAW,GAAG,IAAI;IAClB,IACCoD,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,IAAI,cAAc,IAC/CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,MAAM,IACxCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,WAAW,EAC5C;MACD,IACCH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,SAAS,IAC3CH,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,IAC1CxC,oBAAoB,KAAK,SAAS,IAClCA,oBAAoB,KAAK,QAAQ,IACjCA,oBAAoB,KAAK,SAAS,EACjC;QACDH,cAAc,CAAC,IAAI,CAAC;QACpBC,kBAAkB,CAAC,IAAI,CAAC;QACxB,IAAI2C,QAAQ,GAAGC,QAAQ,CAACC,cAAc,CAAC,oBAAoB,CAAqB;QAChF,MAAMC,WAAW,GAAGF,QAAQ,CAACG,IAAI;;QAEjC;QACAD,WAAW,CAACE,SAAS,CAACC,GAAG,CAAC,oBAAoB,CAAC;QAE/C,IAAI,CAACN,QAAQ,EAAE;UACdA,QAAQ,GAAGC,QAAQ,CAACM,aAAa,CAAC,OAAO,CAAC;UAC1CP,QAAQ,CAACQ,EAAE,GAAG,oBAAoB;;UAElC;UACA,IAAIC,MAAM,GAAG;AAClB;AACA;AACA;AACA;AACA;AACA,MAAM;UAEDT,QAAQ,CAACU,SAAS,GAAGD,MAAM;UAC3BR,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACZ,QAAQ,CAAC;QACpC;MACD;MACAH,SAAS,GAAG,GAAGD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiB,SAAS,EAAE;MACjC,IAAIhB,SAAS,KAAKiB,MAAM,CAACC,QAAQ,CAACC,IAAI,EAAE;QACvChE,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC;QAChCH,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC;MACvB,CAAC,MAAM;QACN7C,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC;MACjC;MAEA;IACD,CAAC,MAAM,IAAIrB,KAAK,CAACE,SAAS,CAACC,WAAW,CAAC,CAAC,IAAI,QAAQ,IAAIxC,oBAAoB,KAAK,QAAQ,EAAE;MAC1F;MACAP,iBAAiB,CAAC4C,KAAK,CAACqB,OAAO,CAAC;MAChC9D,cAAc,CAAC,IAAI,CAAC;IACrB;IACA,IAAI0C,SAAS,EAAE;MACd;MACAiB,MAAM,CAACI,IAAI,CAACrB,SAAS,CAAC;IACvB;EACD,CAAC;EAED,MAAMsB,eAAe,GAAIC,YAA0B,IAAK;IACvD5C,wBAAwB,CAAC4C,YAAY,CAAC;IACtC9C,oBAAoB,CAAC,IAAI,CAAC;EAC3B,CAAC;EACD,MAAM+C,wBAAwB,GAAIC,OAAe,IAAK;IACrD5C,kBAAkB,CAAC4C,OAAO,CAAC;IAC3BtC,aAAa,CAAC,IAAI,CAAC;EACpB,CAAC;EACD,MAAMuC,aAAa,GAAIC,KAA0B,IAAK;IACrD,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MAC1BC,YAAY,CAAC,CAAC;IACf;EACD,CAAC;EACD,MAAMC,OAAqB,GAAG,CAC7B;IACCC,KAAK,EAAE,MAAM;IACbC,UAAU,EAAE,MAAM;IAClB;IACAC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE;EACZ,CAAC,EACD;IACCH,KAAK,EAAE,aAAa;IACpBC,UAAU,EAAE,aAAa;IACzB;IACAC,QAAQ,EAAE,IAAI;IACdE,UAAU,EAAGC,MAA4B,iBACxC5F,OAAA;MAAA6F,QAAA,GAAM,GAAC,EAAC,GAAGjG,cAAc,CAACgG,MAAM,CAACE,GAAG,CAACC,WAAW,EAAE,YAAY,CAAC,EAAE;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CACxE;IACDT,SAAS,EAAE;EACZ,CAAC,EACD;IACCH,KAAK,EAAE,SAAS;IAChBC,UAAU,EAAE,SAAS;IACrB;IACAC,QAAQ,EAAE,IAAI;IACdW,QAAQ,EAAE,KAAK;IACfT,UAAU,EAAGC,MAA4B,iBACxC5F,OAAA,CAAAE,SAAA;MAAA2F,QAAA,gBACC7F,OAAA,CAACxB,OAAO;QACP6H,KAAK;QACL9F,KAAK,EAAC,MAAM;QAAAsF,QAAA,eAEZ7F,OAAA,CAAC3B,UAAU;UAACiI,OAAO,EAAEA,CAAA,KAAMhD,eAAe,CAACsC,MAAM,CAACE,GAAG,CAAE;UAAAD,QAAA,eACtD7F,OAAA;YACCuG,uBAAuB,EAAE;cAAEC,MAAM,EAAErH;YAAa,CAAE;YAClDsH,KAAK,EAAE;cAAEC,IAAI,EAAE;YAAI;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACVnG,OAAA,CAACxB,OAAO;QACP6H,KAAK;QACL9F,KAAK,EAAC,OAAO;QAAAsF,QAAA,eAEb7F,OAAA,CAAC3B,UAAU;UAACiI,OAAO,EAAEA,CAAA,KAAMxB,eAAe,CAACc,MAAM,CAACE,GAAG,CAAE;UAAAD,QAAA,eACtD7F,OAAA;YACCuG,uBAAuB,EAAE;cAAEC,MAAM,EAAEpH;YAAa,CAAE;YAClDqH,KAAK,EAAE;cAAEC,IAAI,EAAE;YAAI;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACVnG,OAAA,CAACxB,OAAO;QACP6H,KAAK;QACL9F,KAAK,EAAC,QAAQ;QAAAsF,QAAA,eAEd7F,OAAA,CAAC3B,UAAU;UACViI,OAAO,EAAEA,CAAA,KAAM;YACdtB,wBAAwB,CAACY,MAAM,CAACE,GAAG,CAAClB,OAAO,CAAC;YAC5CrC,oBAAoB,CAACqD,MAAM,CAACE,GAAG,CAACa,IAAI,CAAC;YACrClE,oBAAoB,CAACmD,MAAM,CAACE,GAAG,CAACrC,SAAS,CAAC;UAC3C,CAAE;UAAAoC,QAAA,eAEF7F,OAAA;YACCuG,uBAAuB,EAAE;cAAEC,MAAM,EAAEnH;YAAe,CAAE;YACpDoH,KAAK,EAAE;cAAEC,IAAI,EAAE;YAAI;UAAE;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA,eACT,CACF;IACDT,SAAS,EAAE;EACZ,CAAC,CACD;EAED,MAAMkB,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,aAAA;IACtC,MAAM;MAAE/D,IAAI;MAAEC;IAAS,CAAC,GAAGH,eAAe;IAC1C,MAAMkE,MAAM,GAAGhE,IAAI,GAAGC,QAAQ;IAC9B,MAAMgE,YAAY,GAAGrF,SAAS,KAAK,CAAC,GAAG,QAAQ,GAAGA,SAAS,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO;IAExF,MAAMsF,OAAO,GAAG,CACf;MACCC,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE7G,KAAK,KAAK,eAAe,GAAG,MAAM,GAAGA,KAAK;MACjD8G,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,aAAa;MACxBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEL,YAAY;MACnBM,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,MAAM;MACjBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAExF,WAAW;MAClByF,aAAa,EAAE;IAChB,CAAC,EACD;MACCJ,SAAS,EAAE,WAAW;MACtBC,WAAW,EAAE,QAAQ;MACrBC,SAAS,EAAE,UAAU;MACrBC,KAAK,EAAEpE,SAAS;MAChBqE,aAAa,EAAE;IAChB,CAAC,CACD;IACD,MAAMC,IAAI,GAAG,MAAMrI,YAAY,CAAC6H,MAAM,EAAE/D,QAAQ,EAAEiE,OAAO,EAAE,EAAE,CAAC;IAC9D,MAAMO,WAAW,GAAGD,IAAI,aAAJA,IAAI,wBAAAT,aAAA,GAAJS,IAAI,CAAEE,OAAO,cAAAX,aAAA,uBAAbA,aAAA,CAAeY,GAAG,CAAEC,IAAS,KAAM;MACtD,GAAGA,IAAI;MACPvD,EAAE,EAAEuD,IAAI,CAAC9C;IACV,CAAC,CAAC,CAAC;IAEH7C,eAAe,CAACwF,WAAW,IAAI,EAAE,CAAC;IAClCpE,aAAa,CAACmE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,MAAM,CAAC;EAC5B,CAAC;EAED5J,SAAS,CAAC,MAAM;IACf,IAAIsC,IAAI,IAAI2C,SAAS,EAAE;MACtB4D,kBAAkB,CAAC,CAAC;IACrB;EACD,CAAC,EAAE,CAAChE,eAAe,EAAElB,SAAS,EAAErB,IAAI,EAAE2C,SAAS,CAAC,CAAC;;EAEjD;EACA;EACA;EACA;EACA;;EAEA,MAAMqC,YAAY,GAAGA,CAAA,KAAM;IAC1BuB,kBAAkB,CAAC,CAAC;EACrB,CAAC;EAED7I,SAAS,CAAC,MAAM;IACf,IAAI6D,WAAW,CAACgG,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC9BhB,kBAAkB,CAAC,CAAC;IACrB;EACD,CAAC,EAAE,CAAChF,WAAW,CAAC,CAAC;EACjB,MAAMiG,iBAAiB,GAAGA,CAAA,KAAM;IAC/BhG,cAAc,CAAC,EAAE,CAAC;IAClB+E,kBAAkB,CAAC,CAAC;EACrB,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAAC3C,KAA2B,EAAE4C,QAAgB,KAAK;IAC1EpG,YAAY,CAACoG,QAAQ,CAAC;IACtBlF,kBAAkB,CAAEmF,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAElF,IAAI,EAAE;IAAE,CAAC,CAAC,CAAC,CAAC,CAAC;EACvD,CAAC;EACD,MAAMmF,aAAa,GAAGpK,KAAK,CAACqK,WAAW,CAAEtC,MAA4B,IAAK;IACzE,OAAO;MACNuC,GAAG,EAAEvC,MAAM,CAACwC,cAAc,GAAG,CAAC,GAAG,CAAC;MAClCC,MAAM,EAAEzC,MAAM,CAAC0C,aAAa,GAAG,CAAC,GAAG;IACpC,CAAC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAInG,eAAe,EAAE;MACpB,IAAI;QACH,MAAMoG,QAAQ,GAAG,MAAMtJ,oBAAoB,CAACkD,eAAe,CAAC;QAC5D,IAAIoG,QAAQ,CAACC,OAAO,EAAE;UACrBxF,YAAY,CAAC,GAAGX,iBAAiB,IAAIE,iBAAiB,uBAAuB,EAAE,SAAS,CAAC;UACzF,MAAMoE,kBAAkB,CAAC,CAAC;QAC3B,CAAC,MAAM;UACN3D,YAAY,CAACuF,QAAQ,CAACE,YAAY,EAAE,OAAO,CAAC;QAC7C;MACD,CAAC,CAAC,OAAOC,KAAK,EAAE,CAAC;IAClB;IACAhG,aAAa,CAAC,KAAK,CAAC;IACpBN,kBAAkB,CAAC,IAAI,CAAC;IACxBE,oBAAoB,CAAC,EAAE,CAAC;EACzB,CAAC;EACD,MAAMqG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACtC,MAAMhC,kBAAkB,CAAC,CAAC;EAC3B,CAAC;EACD,MAAMiC,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,OAAO,CAAC;IACjD,MAAMC,eAAe,GAAGD,SAAS,CAACpH,SAAS,CAAC,IAAIlB,UAAU;IAC1D,OAAO,MAAMuI,eAAe,IAAIvI,UAAU,GAAG;EAC9C,CAAC;EACD,MAAMwI,aAAa,GAAGA,CAAA,kBACrBhJ,OAAA;IAAKyG,KAAK,EAAE;MAAEwC,OAAO,EAAE,MAAM;MAAEC,UAAU,EAAE,QAAQ;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAtD,QAAA,gBAC9E7F,OAAA;MACCoJ,SAAS,EAAC,gBAAgB;MAC1B7C,uBAAuB,EAAE;QAAEC,MAAM,EAAElH;MAAO;IAAE;MAAA0G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eACFnG,OAAA,CAACnB,UAAU;MAACwK,EAAE,EAAE;QAAEC,UAAU,EAAE;MAAM,CAAE;MAAAzD,QAAA,EAAEgD,cAAc,CAAC;IAAC;MAAA7C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClE,CACL;EAED,MAAMoD,gBAAgB,GAAGA,CAAA,KAAM;IAC9B;IACA;IACA;IACA;EAAA,CACA;EAED,oBACCvJ,OAAA;IAAKmE,EAAE,EAAC,eAAe;IAAA0B,QAAA,gBACtB7F,OAAA,CAAC/B,MAAM;MACNuL,SAAS,EAAE;QACVC,IAAI,EAAE;UACLtF,EAAE,EAAE;QACL,CAAC;QACDuF,QAAQ,EAAE;UACTL,EAAE,EAAE;YACHM,QAAQ,EAAE;UACX;QACD;MACD,CAAE;MACF9E,IAAI,EAAExE,IAAK;MACXC,OAAO,EAAEiJ,gBAAiB;MAC1BK,SAAS;MACTC,QAAQ,EAAC,IAAI;MACbT,SAAS,EAAC,qBAAqB;MAAAvD,QAAA,eAE/B7F,OAAA,CAAC9B,aAAa;QAACkL,SAAS,EAAC,6BAA6B;QAAAvD,QAAA,gBACrD7F,OAAA;UACCoJ,SAAS,EAAC,eAAe;UACzBjF,EAAE,EAAC,cAAc;UAAA0B,QAAA,eAEjB7F,OAAA;YACCoJ,SAAS,EAAC,OAAO;YACjB3C,KAAK,EAAE;cAAE6C,UAAU,EAAE;YAAiB,CAAE;YAAAzD,QAAA,GAEvCrF,UAAU,EAAC,GACb;UAAA;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAaH,CAAC,eACNnG,OAAA;UACCoJ,SAAS,EAAC,YAAY;UACtBjF,EAAE,EAAC,YAAY;UAAA0B,QAAA,eAEf7F,OAAA;YAAKoJ,SAAS,EAAC,kBAAkB;YAAAvD,QAAA,gBAChC7F,OAAA,CAAC7B,SAAS;cACT2L,OAAO,EAAC,UAAU;cAClBC,WAAW,EAAE,UAAUxJ,KAAK,EAAG;cAC/ByJ,KAAK,EAAEpI,WAAY;cACnBqI,QAAQ,EAAGC,CAAC,IAAK;gBAChB,MAAMnC,QAAQ,GAAGmC,CAAC,CAACC,MAAM,CAACH,KAAK;gBAC/BnI,cAAc,CAACkG,QAAQ,CAAC;gBACxB,IAAIA,QAAQ,KAAK,EAAE,EAAE;kBACpBF,iBAAiB,CAAC,CAAC;gBACpB;cACD,CAAE;cACFuC,SAAS,EAAGF,CAAC,IAAK;gBACjB,IAAIA,CAAC,CAAC9E,GAAG,KAAK,OAAO,EAAE;kBACtBC,YAAY,CAAC,CAAC;gBACf;cACD,CAAE;cACF+D,SAAS,EAAC,iBAAiB;cAC3BiB,UAAU,EAAE;gBACXhB,EAAE,EAAE;kBACH,0CAA0C,EAAE;oBAAEiB,WAAW,EAAE;kBAAU,CAAC;kBAAE;kBACxE,gDAAgD,EAAE;oBAAEC,MAAM,EAAE;kBAAoB;gBACjF,CAAC;gBACDC,cAAc,eACbxK,OAAA,CAAC5B,cAAc;kBAACuL,QAAQ,EAAC,OAAO;kBAAA9D,QAAA,eAC/B7F,OAAA,CAAC3B,UAAU;oBACV,cAAW,QAAQ;oBACnBiI,OAAO,EAAEA,CAAA,KAAMjB,YAAY,CAAC,CAAE;oBAC9BoF,WAAW,EAAGtF,KAAK,IAAKA,KAAK,CAACuF,cAAc,CAAC,CAAE;oBAAA7E,QAAA,eAE/C7F,OAAA,CAACjB,UAAU;sBAAAiH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAChB;gBACDwE,YAAY,EAAE/I,WAAW,iBACxB5B,OAAA,CAAC5B,cAAc;kBAACuL,QAAQ,EAAC,KAAK;kBAAA9D,QAAA,eAC7B7F,OAAA,CAAC3B,UAAU;oBACV,cAAW,OAAO;oBAClBiI,OAAO,EAAEA,CAAA,KAAM;sBACdzE,cAAc,CAAC,EAAE,CAAC;sBAClBgG,iBAAiB,CAAC,CAAC;oBACpB,CAAE;oBAAAhC,QAAA,eAEF7F,OAAA,CAAChB,SAAS;sBAACqK,EAAE,EAAE;wBAAE3C,IAAI,EAAE;sBAAM;oBAAE;sBAAAV,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE;cAElB;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFnG,OAAA;cAAKoJ,SAAS,EAAC,kBAAkB;cAAAvD,QAAA,eAChC7F,OAAA;gBACCsG,OAAO,EAAEA,CAAA,KAAM7F,UAAU,CAACD,UAAU,CAAE;gBACtC4I,SAAS,EAAC,oBAAoB;gBAAAvD,QAAA,gBAE9B7F,OAAA,CAACR,OAAO;kBAACiH,KAAK,EAAE;oBAAEmE,WAAW,EAAE,KAAK;oBAAElE,IAAI,EAAE;kBAAM;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAEvDnG,OAAA;kBAAA6F,QAAA,EAAO,UAAUrF,UAAU,KAAK,eAAe,GAAG,MAAM,GAAGA,UAAU;gBAAE;kBAAAwF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAENnG,OAAA;UAAKoJ,SAAS,EAAC,sBAAsB;UAAAvD,QAAA,eACpC7F,OAAA,CAACzB,IAAI;YACJyL,KAAK,EAAEtI,SAAU;YACjBuI,QAAQ,EAAEnC,eAAgB;YAAAjC,QAAA,gBAE1B7F,OAAA,CAAC1B,GAAG;cACHuM,KAAK,EAAC,QAAQ;cACdxB,EAAE,EAAE;gBACHyB,eAAe,EAAE,oBAAoB;gBACrCP,MAAM,EAAE,oBAAoB;gBAC5BQ,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFnG,OAAA,CAAC1B,GAAG;cACHuM,KAAK,EAAC,UAAU;cAChBxB,EAAE,EAAE;gBACHyB,eAAe,EAAE,oBAAoB;gBACrCP,MAAM,EAAE,oBAAoB;gBAC5BQ,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACFnG,OAAA,CAAC1B,GAAG;cACHuM,KAAK,EAAC,OAAO;cACbxB,EAAE,EAAE;gBACHyB,eAAe,EAAE,oBAAoB;gBACrCP,MAAM,EAAE,oBAAoB;gBAC5BQ,KAAK,EAAE,oBAAoB;gBAC3BC,QAAQ,EAAE;cACX;YAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA;UAAKoJ,SAAS,EAAC,eAAe;UAAAvD,QAAA,eAC7B7F,OAAA,CAAClB,QAAQ;YACRmM,IAAI,EAAEnJ,YAAa;YACnBwD,OAAO,EAAEA,OAAQ;YACjB4F,QAAQ,EAAGpF,GAAG,IAAKA,GAAG,CAAClB,OAAQ;YAC/BqD,aAAa,EAAEA,aAAc;YAC7BkD,UAAU;YACVvI,eAAe,EAAEA,eAAgB;YACjCwI,cAAc,EAAC,QAAQ;YACvBC,uBAAuB,EAAExI,kBAAmB;YAC5CyI,QAAQ,EAAEpI,UAAW;YACrBqI,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAE;YACnCC,UAAU,EAAE;cACXC,kBAAkB,EAAE;gBACnBC,gBAAgB,EAAE;cACnB,CAAC;cACDC,WAAW,EAAE9C,cAAc,CAAC;YAC7B,CAAE;YACF+C,iBAAiB;YACjBC,0BAA0B;YAC1BzC,SAAS,EAAC,eAAe;YACzB0C,KAAK,EAAE;cACNC,aAAa,EAAE/C,aAAa,CAAE;YAC/B,CAAE;YACFK,EAAE,EAAE;cACH,oBAAoB,EAAE;gBACrBQ,QAAQ,EAAE,mBAAmB;gBAC7B,kBAAkB,EAAE;gBACpB;gBACA;cACD,CAAC;cACD,qBAAqB,EAAE;gBACtBmC,OAAO,EAAE;cACV,CAAC;cACD,6BAA6B,EAAE;gBAC9B/C,OAAO,EAAE,iBAAiB;gBAC1BC,UAAU,EAAE;cACb,CAAC;cACD,oCAAoC,EAAE;gBACrCqB,MAAM,EAAE,iBAAiB;gBACzBQ,KAAK,EAAE,oBAAoB;gBAC3BD,eAAe,EAAE,oBAAoB;gBACrC,SAAS,EAAE;kBACVA,eAAe,EAAE,oBAAoB,CAAE;gBACxC;cACD,CAAC;cACD,6BAA6B,EAAE;gBAC9BmB,UAAU,EAAE,6CAA6C;gBACzDD,OAAO,EAAE,mBAAmB;gBAC5BE,WAAW,EAAE,mBAAmB;gBAChCC,MAAM,EAAE;cACT,CAAC;cACD,kCAAkC,EAAE;gBACnC7C,UAAU,EAAE;cACb,CAAC;cACD,uBAAuB,EAAE;gBACxBwB,eAAe,EAAE,uBAAuB;gBACxC,kBAAkB,EAAE;cACrB,CAAC;cACD,gCAAgC,EAAE;gBACjCA,eAAe,EAAE,uBAAuB;gBACxC7B,OAAO,EAAE;cACV;YACD,CAAE;YACFmD,SAAS,EAAE;UAAG;YAAApG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eACTnG,OAAA,CAAC/B,MAAM;MACN4G,IAAI,EAAEnC,UAAW;MACjBpC,OAAO,EAAEA,CAAA,KAAMqC,aAAa,CAAC,KAAK,CAAE;MACpC0J,UAAU,EAAE;QACX5F,KAAK,EAAE;UACN6F,YAAY,EAAE,KAAK;UACnBzC,QAAQ,EAAE,OAAO;UACjB0C,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,OAAO;UAClBC,SAAS,EAAE;QACZ;MACD,CAAE;MAAA5G,QAAA,gBAEF7F,OAAA,CAACvB,WAAW;QAAC4K,EAAE,EAAE;UAAE2C,OAAO,EAAE;QAAE,CAAE;QAAAnG,QAAA,gBAC/B7F,OAAA;UAAKyG,KAAK,EAAE;YAAEwC,OAAO,EAAE,MAAM;YAAEyD,cAAc,EAAE,QAAQ;YAAEV,OAAO,EAAE;UAAO,CAAE;UAAAnG,QAAA,eAC1E7F,OAAA;YACCyG,KAAK,EAAE;cACNqE,eAAe,EAAE,SAAS;cAC1BwB,YAAY,EAAE,KAAK;cACnBK,KAAK,EAAE,MAAM;cACbR,MAAM,EAAE,MAAM;cACdlD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBwD,cAAc,EAAE;YACjB,CAAE;YAAA7G,QAAA,eAEF7F,OAAA,CAACT,yBAAyB;cAAC8J,EAAE,EAAE;gBAAE0B,KAAK,EAAE,SAAS;gBAAEoB,MAAM,EAAE,MAAM;gBAAEQ,KAAK,EAAE;cAAO;YAAE;cAAA3G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACNnG,OAAA,CAACnB,UAAU;UAACwK,EAAE,EAAE;YAAE2B,QAAQ,EAAE,iBAAiB;YAAE1B,UAAU,EAAE,GAAG;YAAE0C,OAAO,EAAE;UAAS,CAAE;UAAAnG,QAAA,GAAC,SAC7E,EAACrD,iBAAiB;QAAA;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEdnG,OAAA,CAAC9B,aAAa;QAACmL,EAAE,EAAE;UAAE2C,OAAO,EAAE;QAAkB,CAAE;QAAAnG,QAAA,eACjD7F,OAAA,CAACtB,iBAAiB;UAAC+H,KAAK,EAAE;YAAEuE,QAAQ,EAAE,MAAM;YAAED,KAAK,EAAE;UAAO,CAAE;UAAAlF,QAAA,GAAC,MAC1D,eAAA7F,OAAA;YAAMyG,KAAK,EAAE;cAAE6C,UAAU,EAAE;YAAO,CAAE;YAAAzD,QAAA,EAAEvD;UAAiB;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,2CACpE;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEhBnG,OAAA,CAACrB,aAAa;QAAC0K,EAAE,EAAE;UAAEqD,cAAc,EAAE,eAAe;UAAEE,SAAS,EAAE;QAAgC,CAAE;QAAA/G,QAAA,gBAClG7F,OAAA,CAACpB,MAAM;UACN0H,OAAO,EAAEA,CAAA,KAAM3D,aAAa,CAAC,KAAK,CAAE;UACpC0G,EAAE,EAAE;YACH0B,KAAK,EAAE,SAAS;YAChBR,MAAM,EAAE,mBAAmB;YAC3B+B,YAAY,EAAE,KAAK;YACnBO,aAAa,EAAE,YAAY;YAC3Bb,OAAO,EAAE,uBAAuB;YAChCc,UAAU,EAAE;UACb,CAAE;UAAAjH,QAAA,EACF;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTnG,OAAA,CAACpB,MAAM;UACN0H,OAAO,EAAEiC,YAAa;UACtBc,EAAE,EAAE;YACHyB,eAAe,EAAE,oBAAoB;YACrCC,KAAK,EAAE,MAAM;YACbuB,YAAY,EAAE,KAAK;YACnBO,aAAa,EAAE,YAAY;YAC3Bb,OAAO,EAAE,uBAAuB;YAChCc,UAAU,EAAE;YACZ;YACA;YACA;UACD,CAAE;UAAAjH,QAAA,EACF;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,EACRnE,iBAAiB,IAAIE,qBAAqB,iBAC1ClC,OAAA,CAACP,sBAAsB;MACtBoF,IAAI,EAAE7C,iBAAkB;MACxB+K,WAAW,EAAEA,CAAA,KAAM9K,oBAAoB,CAAC,KAAK,CAAE;MAC/C+K,WAAW,EAAE9K,qBAAsB;MACnC+K,cAAc,EAAErE,kBAAmB;MACnCxF,IAAI,EAAEA;IAAK;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CACD;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAER,CAAC;AAACzF,EAAA,CA3lBIN,UAAqC;EAAA,QACIN,cAAc,EAcxDD,cAAc,EAeOF,WAAW;AAAA;AAAAuN,EAAA,GA9B/B9M,UAAqC;AA4lB3C,SAASD,WAAW;AACpB,eAAeC,UAAU;AAAC,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}