{"ast": null, "code": "var _jsxFileName = \"E:\\\\code\\\\QuickAdopt\\\\quickadapt\\\\QuickAdaptExtension\\\\src\\\\components\\\\GuidesPreview\\\\HotspotPreview.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, Typography } from \"@mui/material\";\nimport CloseIcon from \"@mui/icons-material/Close\";\nimport useDrawerStore from \"../../store/drawerStore\";\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\nimport PerfectScrollbar from 'react-perfect-scrollbar';\nimport 'react-perfect-scrollbar/dist/css/styles.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst HotspotPreview = ({\n  anchorEl,\n  guideStep,\n  title,\n  text,\n  imageUrl,\n  onClose,\n  onPrevious,\n  onContinue,\n  videoUrl,\n  currentStep,\n  totalSteps,\n  onDontShowAgain,\n  progress,\n  textFieldProperties,\n  imageProperties,\n  customButton,\n  modalProperties,\n  canvasProperties,\n  htmlSnippet,\n  previousButtonStyles,\n  continueButtonStyles,\n  OverlayValue,\n  savedGuideData,\n  hotspotProperties,\n  handleHotspotHover,\n  handleHotspotClick,\n  isHotspotPopupOpen,\n  showHotspotenduser\n}) => {\n  _s();\n  var _savedGuideData$Guide, _savedGuideData$Guide2, _textFieldProperties$, _textFieldProperties$2, _textFieldProperties$3, _imageProperties, _imageProperties$Cust, _imageProperties$Cust2, _savedGuideData$Guide31, _savedGuideData$Guide32, _savedGuideData$Guide33;\n  const {\n    setCurrentStep,\n    selectedTemplate,\n    toolTipGuideMetaData,\n    elementSelected,\n    axisData,\n    tooltipXaxis,\n    tooltipYaxis,\n    setOpenTooltip,\n    openTooltip,\n    pulseAnimationsH,\n    hotspotGuideMetaData,\n    selectedTemplateTour,\n    selectedOption,\n    ProgressColor\n  } = useDrawerStore(state => state);\n  const [targetElement, setTargetElement] = useState(null);\n  // State to track if the popover should be shown\n  // State for popup visibility is managed through openTooltip\n  const [popupPosition, setPopupPosition] = useState(null);\n  const [dynamicWidth, setDynamicWidth] = useState(null);\n  const [hotspotSize, setHotspotSize] = useState(30); // Track hotspot size for dynamic popup positioning\n  const contentRef = useRef(null);\n  const buttonContainerRef = useRef(null);\n  let hotspot;\n  const getElementByXPath = xpath => {\n    if (!xpath) return null;\n    try {\n      const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\n      const node = result.singleNodeValue;\n      if (node instanceof HTMLElement) {\n        return node;\n      } else if (node !== null && node !== void 0 && node.parentElement) {\n        return node.parentElement; // Return parent if it's a text node\n      } else {\n        return null;\n      }\n    } catch (error) {\n      console.error(\"Error evaluating XPath:\", xpath, error);\n      return null;\n    }\n  };\n  const validateElementPosition = element => {\n    const rect = element.getBoundingClientRect();\n    return rect.width > 0 && rect.height > 0 && rect.top !== 0 && rect.left !== 0 && !Number.isNaN(rect.top) && !Number.isNaN(rect.left);\n  };\n  let xpath;\n  if (savedGuideData) xpath = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide = savedGuideData.GuideStep) === null || _savedGuideData$Guide === void 0 ? void 0 : (_savedGuideData$Guide2 = _savedGuideData$Guide[0]) === null || _savedGuideData$Guide2 === void 0 ? void 0 : _savedGuideData$Guide2.ElementPath;\n  const getElementPosition = xpath => {\n    const element = getElementByXPath(xpath || \"\");\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      return {\n        top: rect.top,\n        //+ window.scrollY + yOffset, // Adjust for vertical scroll\n        left: rect.left // + window.scrollX + xOffset, // Adjust for horizontal scroll\n      };\n    }\n    return null;\n  };\n  // State to track if scrolling is needed\n  const [needsScrolling, setNeedsScrolling] = useState(false);\n  const scrollbarRef = useRef(null);\n  // Function to calculate popup position below the hotspot\n  const calculatePopupPosition = (elementRect, hotspotSize, xOffset, yOffset) => {\n    const hotspotLeft = elementRect.x + xOffset;\n    const hotspotTop = elementRect.y + yOffset;\n\n    // Position popup below the hotspot for better user experience\n    const dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\n    const dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\n\n    return {\n      top: hotspotTop + window.scrollY + dynamicOffsetY,\n      left: hotspotLeft + window.scrollX + dynamicOffsetX\n    };\n  };\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    if (element) {\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        // Account for scrolling\n        left: rect.left + window.scrollX\n      });\n    }\n  }, [xpath]);\n  useEffect(() => {\n    if (typeof window !== undefined) {\n      const position = getElementPosition(xpath || \"\");\n      if (position) {\n        setPopupPosition(position);\n      }\n    }\n  }, [xpath]);\n  useEffect(() => {\n    const element = getElementByXPath(xpath);\n    // setTargetElement(element);\n    if (element) {}\n  }, [savedGuideData]);\n  useEffect(() => {\n    var _guideStep;\n    const element = getElementByXPath(guideStep === null || guideStep === void 0 ? void 0 : (_guideStep = guideStep[currentStep - 1]) === null || _guideStep === void 0 ? void 0 : _guideStep.ElementPath);\n    setTargetElement(element);\n    if (element) {\n      element.style.backgroundColor = \"red !important\";\n\n      // Update popup position when target element changes\n      const rect = element.getBoundingClientRect();\n      setPopupPosition({\n        top: rect.top + window.scrollY,\n        left: rect.left + window.scrollX\n      });\n\n      // Check if element is visible and scroll to it if needed (for tour step navigation)\n      const viewportHeight = window.innerHeight;\n      const viewportWidth = window.innerWidth;\n\n      // Check if element is completely out of view\n      const isCompletelyOutOfView = rect.bottom < 0 ||\n      // Completely above viewport\n      rect.top > viewportHeight ||\n      // Completely below viewport\n      rect.right < 0 ||\n      // Completely left of viewport\n      rect.left > viewportWidth // Completely right of viewport\n      ;\n\n      // More generous visibility check - element should be reasonably visible\n      const isReasonablyVisible = rect.top >= -50 &&\n      // Allow some element to be above viewport\n      rect.left >= -50 &&\n      // Allow some element to be left of viewport\n      rect.bottom <= viewportHeight + 50 &&\n      // Allow some element below viewport\n      rect.right <= viewportWidth + 50 &&\n      // Allow some element right of viewport\n      rect.width > 0 && rect.height > 0;\n\n      // Add a small delay to ensure proper positioning\n      setTimeout(() => {\n        if (element && (isCompletelyOutOfView || !isReasonablyVisible)) {\n          console.log(\"🎯 Tour hotspot element not visible, performing auto-scroll\");\n          // Element is not visible, scroll to bring it into view\n          try {\n            element.scrollIntoView({\n              behavior: 'smooth',\n              block: 'center',\n              // Center it for better visibility\n              inline: 'nearest'\n            });\n            console.log(\"✅ Tour hotspot auto-scroll completed successfully\");\n          } catch (scrollError) {\n            console.error(\"❌ Tour hotspot auto-scroll failed:\", scrollError);\n          }\n        } else if (element) {\n          console.log(\"✅ Tour hotspot element is reasonably visible, minimal adjustment\");\n          // Element is mostly visible, just ensure it's well positioned\n          try {\n            element.scrollIntoView({\n              behavior: 'smooth',\n              block: 'nearest',\n              // Don't force center if already visible\n              inline: 'nearest'\n            });\n          } catch (error) {\n            console.log(\"Tour hotspot minimal scroll adjustment failed:\", error);\n          }\n        }\n      }, 100);\n    }\n  }, [guideStep, currentStep]);\n\n  // Hotspot styles are applied directly in the applyHotspotStyles function\n  // State for overlay value\n  const [, setOverlayValue] = useState(false);\n  const handleContinue = () => {\n    if (selectedTemplate !== \"Tour\") {\n      if (currentStep < totalSteps) {\n        setCurrentStep(currentStep + 1);\n        onContinue();\n        renderNextPopup(currentStep < totalSteps);\n      }\n    } else {\n      setCurrentStep(currentStep + 1);\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.style.display = \"none\";\n        existingHotspot.remove();\n      }\n    }\n  };\n  const renderNextPopup = shouldRenderNextPopup => {\n    var _savedGuideData$Guide3, _savedGuideData$Guide4, _savedGuideData$Guide5, _savedGuideData$Guide6, _savedGuideData$Guide7, _savedGuideData$Guide8, _savedGuideData$Guide9, _savedGuideData$Guide10, _savedGuideData$Guide11, _savedGuideData$Guide12;\n    return shouldRenderNextPopup ? /*#__PURE__*/_jsxDEV(HotspotPreview, {\n      isHotspotPopupOpen: isHotspotPopupOpen,\n      showHotspotenduser: showHotspotenduser,\n      handleHotspotHover: handleHotspotHover,\n      handleHotspotClick: handleHotspotClick,\n      anchorEl: anchorEl,\n      savedGuideData: savedGuideData,\n      guideStep: guideStep,\n      onClose: onClose,\n      onPrevious: handlePrevious,\n      onContinue: handleContinue,\n      title: title,\n      text: text,\n      imageUrl: imageUrl,\n      currentStep: currentStep + 1,\n      totalSteps: totalSteps,\n      onDontShowAgain: onDontShowAgain,\n      progress: progress,\n      textFieldProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide3 = savedGuideData.GuideStep) === null || _savedGuideData$Guide3 === void 0 ? void 0 : (_savedGuideData$Guide4 = _savedGuideData$Guide3[currentStep]) === null || _savedGuideData$Guide4 === void 0 ? void 0 : _savedGuideData$Guide4.TextFieldProperties,\n      imageProperties: savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide5 = savedGuideData.GuideStep) === null || _savedGuideData$Guide5 === void 0 ? void 0 : (_savedGuideData$Guide6 = _savedGuideData$Guide5[currentStep]) === null || _savedGuideData$Guide6 === void 0 ? void 0 : _savedGuideData$Guide6.ImageProperties,\n      customButton: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide7 = savedGuideData.GuideStep) === null || _savedGuideData$Guide7 === void 0 ? void 0 : (_savedGuideData$Guide8 = _savedGuideData$Guide7[currentStep]) === null || _savedGuideData$Guide8 === void 0 ? void 0 : (_savedGuideData$Guide9 = _savedGuideData$Guide8.ButtonSection) === null || _savedGuideData$Guide9 === void 0 ? void 0 : (_savedGuideData$Guide10 = _savedGuideData$Guide9.map(section => section.CustomButtons.map(button => ({\n        ...button,\n        ContainerId: section.Id // Attach the container ID for grouping\n      })))) === null || _savedGuideData$Guide10 === void 0 ? void 0 : _savedGuideData$Guide10.reduce((acc, curr) => acc.concat(curr), [])) || [],\n      modalProperties: modalProperties,\n      canvasProperties: canvasProperties,\n      htmlSnippet: htmlSnippet,\n      OverlayValue: OverlayValue,\n      hotspotProperties: (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide11 = savedGuideData.GuideStep) === null || _savedGuideData$Guide11 === void 0 ? void 0 : (_savedGuideData$Guide12 = _savedGuideData$Guide11[currentStep - 1]) === null || _savedGuideData$Guide12 === void 0 ? void 0 : _savedGuideData$Guide12.Hotspot) || {}\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 4\n    }, this) : null;\n  };\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      onPrevious();\n    }\n  };\n  useEffect(() => {\n    if (OverlayValue) {\n      setOverlayValue(true);\n    } else {\n      setOverlayValue(false);\n    }\n  }, [OverlayValue]);\n  // Image fit is used directly in the component\n  const getAnchorAndTransformOrigins = position => {\n    switch (position) {\n      case \"top-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          }\n        };\n      case \"top-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          }\n        };\n      case \"bottom-left\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"top\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-right\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      case \"center-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"top-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"top\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          }\n        };\n      case \"left-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          }\n        };\n      case \"bottom-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"bottom\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n      case \"right-center\":\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"right\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"left\"\n          }\n        };\n      default:\n        return {\n          anchorOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          },\n          transformOrigin: {\n            vertical: \"center\",\n            horizontal: \"center\"\n          }\n        };\n    }\n  };\n  const {\n    anchorOrigin,\n    transformOrigin\n  } = getAnchorAndTransformOrigins((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center center\");\n  const textStyle = {\n    fontWeight: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$ = textFieldProperties.TextProperties) !== null && _textFieldProperties$ !== void 0 && _textFieldProperties$.Bold ? \"bold\" : \"normal\",\n    fontStyle: textFieldProperties !== null && textFieldProperties !== void 0 && (_textFieldProperties$2 = textFieldProperties.TextProperties) !== null && _textFieldProperties$2 !== void 0 && _textFieldProperties$2.Italic ? \"italic\" : \"normal\",\n    color: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : (_textFieldProperties$3 = textFieldProperties.TextProperties) === null || _textFieldProperties$3 === void 0 ? void 0 : _textFieldProperties$3.TextColor) || \"#000000\",\n    textAlign: (textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.Alignment) || \"left\"\n  };\n\n  // Image styles are applied directly in the component\n\n  const renderHtmlSnippet = snippet => {\n    // Return the raw HTML snippet for rendering\n    return {\n      __html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\n        return `${p1}${p2}\" target=\"_blank\"${p3}`;\n      })\n    };\n  };\n\n  // Helper function to check if popup has only buttons (no text or images)\n  const hasOnlyButtons = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasButtons && !hasImage && !hasText;\n  };\n\n  // Helper function to check if popup has only text (no buttons or images)\n  const hasOnlyText = () => {\n    const hasImage = imageProperties && imageProperties.length > 0 && imageProperties.some(prop => prop.CustomImage && prop.CustomImage.some(img => img.Url));\n    const hasText = textFieldProperties && textFieldProperties.length > 0 && textFieldProperties.some(field => field.Text && field.Text.trim() !== \"\");\n    const hasButtons = customButton && customButton.length > 0;\n    return hasText && !hasImage && !hasButtons;\n  };\n\n  // Function to calculate the optimal width based on content and buttons\n  const calculateOptimalWidth = () => {\n    var _contentRef$current, _buttonContainerRef$c;\n    // If we have a fixed width from canvas settings and not a compact popup, use that\n    if (canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width && !hasOnlyButtons() && !hasOnlyText()) {\n      return `${canvasProperties.Width}px`;\n    }\n\n    // For popups with only buttons or only text, use auto width\n    if (hasOnlyButtons() || hasOnlyText()) {\n      return \"auto\";\n    }\n\n    // Get the width of content and button container\n    const contentWidth = ((_contentRef$current = contentRef.current) === null || _contentRef$current === void 0 ? void 0 : _contentRef$current.scrollWidth) || 0;\n    const buttonWidth = ((_buttonContainerRef$c = buttonContainerRef.current) === null || _buttonContainerRef$c === void 0 ? void 0 : _buttonContainerRef$c.scrollWidth) || 0;\n\n    // Use the larger of the two, with some minimum and maximum constraints\n    const optimalWidth = Math.max(contentWidth, buttonWidth);\n\n    // Add some padding to ensure text has room to wrap naturally\n    const paddedWidth = optimalWidth + 20; // 10px padding on each side\n\n    // Ensure width is between reasonable bounds\n    const minWidth = 250; // Minimum width\n    const maxWidth = 800; // Maximum width\n\n    const finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\n    return `${finalWidth}px`;\n  };\n\n  // Update dynamic width when content or buttons change\n  useEffect(() => {\n    // Use requestAnimationFrame to ensure DOM has been updated\n    requestAnimationFrame(() => {\n      const newWidth = calculateOptimalWidth();\n      setDynamicWidth(newWidth);\n    });\n  }, [textFieldProperties, imageProperties, customButton, currentStep]);\n\n  // Recalculate popup position when hotspot size changes\n  useEffect(() => {\n    if (xpath && hotspotSize) {\n      const element = getElementByXPath(xpath);\n      if (element) {\n        var _toolTipGuideMetaData;\n        const rect = element.getBoundingClientRect();\n        const hotspotPropData = (_toolTipGuideMetaData = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData === void 0 ? void 0 : _toolTipGuideMetaData.hotspots;\n        const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n        const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n        const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n        setPopupPosition(popupPos);\n      }\n    }\n  }, [hotspotSize, xpath, toolTipGuideMetaData]);\n\n  // Recalculate popup position on window resize\n  useEffect(() => {\n    const handleResize = () => {\n      if (xpath && hotspotSize) {\n        const element = getElementByXPath(xpath);\n        if (element) {\n          var _toolTipGuideMetaData2;\n          const rect = element.getBoundingClientRect();\n          const hotspotPropData = (_toolTipGuideMetaData2 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData2 === void 0 ? void 0 : _toolTipGuideMetaData2.hotspots;\n          const xOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.YPosition) || \"4\");\n          const popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\n          setPopupPosition(popupPos);\n        }\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [xpath, hotspotSize, toolTipGuideMetaData]);\n  const groupedButtons = customButton.reduce((acc, button) => {\n    const containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\n    if (!acc[containerId]) {\n      acc[containerId] = [];\n    }\n    acc[containerId].push(button);\n    return acc;\n  }, {});\n  const canvasStyle = {\n    position: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\",\n    borderRadius: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Radius) || \"4px\",\n    borderWidth: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderSize) || \"0px\",\n    borderColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BorderColor) || \"black\",\n    borderStyle: \"solid\",\n    backgroundColor: (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.BackgroundColor) || \"white\",\n    maxWidth: hasOnlyButtons() || hasOnlyText() ? \"none !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"800px\",\n    width: hasOnlyButtons() || hasOnlyText() ? \"auto !important\" : dynamicWidth ? `${dynamicWidth} !important` : canvasProperties !== null && canvasProperties !== void 0 && canvasProperties.Width ? `${canvasProperties.Width}px !important` : \"300px\"\n  };\n  const sectionHeight = ((_imageProperties = imageProperties[currentStep - 1]) === null || _imageProperties === void 0 ? void 0 : (_imageProperties$Cust = _imageProperties.CustomImage) === null || _imageProperties$Cust === void 0 ? void 0 : (_imageProperties$Cust2 = _imageProperties$Cust[currentStep - 1]) === null || _imageProperties$Cust2 === void 0 ? void 0 : _imageProperties$Cust2.SectionHeight) || \"auto\";\n  const handleButtonAction = action => {\n    if (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\n      const targetUrl = action.TargetUrl;\n      if (action.ActionValue === \"same-tab\") {\n        // Open the URL in the same tab\n        window.location.href = targetUrl;\n      } else {\n        // Open the URL in a new tab\n        window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n      }\n    } else {\n      if (action.Action == \"Previous\" || action.Action == \"previous\" || action.ActionValue == \"Previous\" || action.ActionValue == \"Previous\") {\n        handlePrevious();\n      } else if (action.Action == \"Next\" || action.Action == \"next\" || action.ActionValue == \"Next\" || action.ActionValue == \"next\") {\n        handleContinue();\n      } else if (action.Action == \"Restart\" || action.ActionValue == \"Restart\") {\n        var _savedGuideData$Guide13, _savedGuideData$Guide14;\n        // Reset to the first step\n        setCurrentStep(1);\n        // If there's a specific URL for the first step, navigate to it\n        if (savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide13 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide13 !== void 0 && (_savedGuideData$Guide14 = _savedGuideData$Guide13[0]) !== null && _savedGuideData$Guide14 !== void 0 && _savedGuideData$Guide14.ElementPath) {\n          const firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\n          if (firstStepElement) {\n            firstStepElement.scrollIntoView({\n              behavior: 'smooth'\n            });\n          }\n        }\n      }\n    }\n    setOverlayValue(false);\n  };\n  useEffect(() => {\n    var _guideStep2, _guideStep2$Hotspot;\n    if (guideStep !== null && guideStep !== void 0 && (_guideStep2 = guideStep[currentStep - 1]) !== null && _guideStep2 !== void 0 && (_guideStep2$Hotspot = _guideStep2.Hotspot) !== null && _guideStep2$Hotspot !== void 0 && _guideStep2$Hotspot.ShowByDefault) {\n      // Show tooltip by default\n      setOpenTooltip(true);\n    }\n  }, [guideStep === null || guideStep === void 0 ? void 0 : guideStep[currentStep - 1], currentStep, setOpenTooltip]);\n\n  // Add effect to handle isHotspotPopupOpen prop changes\n  useEffect(() => {\n    if (isHotspotPopupOpen) {\n      var _toolTipGuideMetaData3, _toolTipGuideMetaData4, _savedGuideData$Guide15, _savedGuideData$Guide16, _savedGuideData$Guide17, _savedGuideData$Guide18;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData3 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData3 !== void 0 && _toolTipGuideMetaData3.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData4 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData4 === void 0 ? void 0 : _toolTipGuideMetaData4.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide15 = savedGuideData.GuideStep) === null || _savedGuideData$Guide15 === void 0 ? void 0 : (_savedGuideData$Guide16 = _savedGuideData$Guide15[currentStep - 1]) === null || _savedGuideData$Guide16 === void 0 ? void 0 : _savedGuideData$Guide16.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide17 = savedGuideData.GuideStep) === null || _savedGuideData$Guide17 === void 0 ? void 0 : (_savedGuideData$Guide18 = _savedGuideData$Guide17[0]) === null || _savedGuideData$Guide18 === void 0 ? void 0 : _savedGuideData$Guide18.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      // For \"Hovering Hotspot\", we'll wait for the hover event\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [isHotspotPopupOpen, toolTipGuideMetaData]);\n\n  // Add effect to handle showHotspotenduser prop changes\n  useEffect(() => {\n    if (showHotspotenduser) {\n      var _toolTipGuideMetaData5, _toolTipGuideMetaData6, _savedGuideData$Guide19, _savedGuideData$Guide20, _savedGuideData$Guide21, _savedGuideData$Guide22;\n      // Get the ShowUpon property\n      const hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData5 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData5 !== void 0 && _toolTipGuideMetaData5.hotspots ? toolTipGuideMetaData[currentStep - 1].hotspots : (_toolTipGuideMetaData6 = toolTipGuideMetaData[0]) === null || _toolTipGuideMetaData6 === void 0 ? void 0 : _toolTipGuideMetaData6.hotspots;\n      const hotspotData = selectedTemplateTour === \"Hotspot\" ? savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide19 = savedGuideData.GuideStep) === null || _savedGuideData$Guide19 === void 0 ? void 0 : (_savedGuideData$Guide20 = _savedGuideData$Guide19[currentStep - 1]) === null || _savedGuideData$Guide20 === void 0 ? void 0 : _savedGuideData$Guide20.Hotspot : savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide21 = savedGuideData.GuideStep) === null || _savedGuideData$Guide21 === void 0 ? void 0 : (_savedGuideData$Guide22 = _savedGuideData$Guide21[0]) === null || _savedGuideData$Guide22 === void 0 ? void 0 : _savedGuideData$Guide22.Hotspot;\n\n      // Only show tooltip by default if ShowByDefault is true\n      if (hotspotPropData !== null && hotspotPropData !== void 0 && hotspotPropData.ShowByDefault) {\n        // Set openTooltip to true\n        setOpenTooltip(true);\n      } else {\n        // Otherwise, initially hide the tooltip\n        setOpenTooltip(false);\n      }\n    }\n  }, [showHotspotenduser, toolTipGuideMetaData]);\n\n  // Add a global click handler to detect clicks outside the hotspot to close the tooltip\n  useEffect(() => {\n    const handleGlobalClick = e => {\n      const hotspotElement = document.getElementById(\"hotspotBlink\");\n\n      // Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\n      if (hotspotElement && hotspotElement.contains(e.target)) {\n        return;\n      }\n\n      // We want to keep the tooltip open once it's been displayed\n      // So we're not closing it on clicks outside anymore\n    };\n    document.addEventListener(\"click\", handleGlobalClick);\n    return () => {\n      document.removeEventListener(\"click\", handleGlobalClick);\n    };\n  }, [toolTipGuideMetaData]);\n  // Check if content needs scrolling with improved detection\n  useEffect(() => {\n    const checkScrollNeeded = () => {\n      if (contentRef.current) {\n        // Force a reflow to get accurate measurements\n        contentRef.current.style.height = 'auto';\n        const contentHeight = contentRef.current.scrollHeight;\n        const containerHeight = 320; // max-height value\n        const shouldScroll = contentHeight > containerHeight;\n        setNeedsScrolling(shouldScroll);\n\n        // Force update scrollbar\n        if (scrollbarRef.current) {\n          // Try multiple methods to update the scrollbar\n          if (scrollbarRef.current.updateScroll) {\n            scrollbarRef.current.updateScroll();\n          }\n          // Force re-initialization if needed\n          setTimeout(() => {\n            if (scrollbarRef.current && scrollbarRef.current.updateScroll) {\n              scrollbarRef.current.updateScroll();\n            }\n          }, 10);\n        }\n      }\n    };\n    checkScrollNeeded();\n    const timeouts = [setTimeout(checkScrollNeeded, 50), setTimeout(checkScrollNeeded, 100), setTimeout(checkScrollNeeded, 200), setTimeout(checkScrollNeeded, 500)];\n    let resizeObserver = null;\n    let mutationObserver = null;\n    if (contentRef.current && window.ResizeObserver) {\n      resizeObserver = new ResizeObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      resizeObserver.observe(contentRef.current);\n    }\n    if (contentRef.current && window.MutationObserver) {\n      mutationObserver = new MutationObserver(() => {\n        setTimeout(checkScrollNeeded, 10);\n      });\n      mutationObserver.observe(contentRef.current, {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        attributeFilter: ['style', 'class']\n      });\n    }\n    return () => {\n      timeouts.forEach(clearTimeout);\n      if (resizeObserver) {\n        resizeObserver.disconnect();\n      }\n      if (mutationObserver) {\n        mutationObserver.disconnect();\n      }\n    };\n  }, [currentStep]);\n  // We no longer need the persistent monitoring effect since we want the tooltip\n  // to close when the mouse leaves the hotspot\n\n  function getAlignment(alignment) {\n    switch (alignment) {\n      case \"start\":\n        return \"flex-start\";\n      case \"end\":\n        return \"flex-end\";\n      case \"center\":\n      default:\n        return \"center\";\n    }\n  }\n  const getCanvasPosition = (position = \"center-center\") => {\n    switch (position) {\n      case \"bottom-left\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-right\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"bottom-center\":\n        return {\n          top: \"auto !important\"\n        };\n      case \"center-center\":\n        return {\n          top: \"25% !important\"\n        };\n      case \"left-center\":\n        return {\n          top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\"\n        };\n      case \"right-center\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-left\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-right\":\n        return {\n          top: \"10% !important\"\n        };\n      case \"top-center\":\n        return {\n          top: \"9% !important\"\n        };\n      default:\n        return {\n          top: \"25% !important\"\n        };\n    }\n  };\n\n  // function to get the correct property value based on tour vs normal hotspot\n  const getHotspotProperty = (propName, hotspotPropData, hotspotData) => {\n    if (selectedTemplateTour === \"Hotspot\") {\n      // For tour hotspots, use saved data first, fallback to metadata\n      switch (propName) {\n        case 'PulseAnimation':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.PulseAnimation) !== undefined ? hotspotData.PulseAnimation : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.PulseAnimation;\n        case 'StopAnimation':\n          // Always use stopAnimationUponInteraction for consistency\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.stopAnimationUponInteraction) !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n        case 'ShowUpon':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowUpon) !== undefined ? hotspotData.ShowUpon : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowUpon;\n        case 'ShowByDefault':\n          return (hotspotData === null || hotspotData === void 0 ? void 0 : hotspotData.ShowByDefault) !== undefined ? hotspotData.ShowByDefault : hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.ShowByDefault;\n        default:\n          return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n      }\n    } else {\n      // For normal hotspots, use metadata\n      if (propName === 'StopAnimation') {\n        return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.stopAnimationUponInteraction;\n      }\n      return hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData[propName];\n    }\n  };\n  const applyHotspotStyles = (hotspot, hotspotPropData, hotspotData, left, top) => {\n    hotspot.style.position = \"absolute\";\n    hotspot.style.left = `${left}px`;\n    hotspot.style.top = `${top}px`;\n    hotspot.style.width = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`; // Default size if not provided\n    hotspot.style.height = `${hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Size}px`;\n    hotspot.style.backgroundColor = hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Color;\n    hotspot.style.borderRadius = \"50%\";\n    hotspot.style.zIndex = \"auto !important\"; // Increased z-index\n    hotspot.style.transition = \"none\";\n    hotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\n    hotspot.innerHTML = \"\";\n    if ((hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Info\" || (hotspotPropData === null || hotspotPropData === void 0 ? void 0 : hotspotPropData.Type) === \"Question\") {\n      const textSpan = document.createElement(\"span\");\n      textSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\n      textSpan.style.color = \"white\";\n      textSpan.style.fontSize = \"14px\";\n      textSpan.style.fontWeight = \"bold\";\n      textSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\n      textSpan.style.display = \"flex\";\n      textSpan.style.alignItems = \"center\";\n      textSpan.style.justifyContent = \"center\";\n      textSpan.style.width = \"100%\";\n      textSpan.style.height = \"100%\";\n      hotspot.appendChild(textSpan);\n    }\n\n    // Apply animation class if needed\n    // Track if pulse has been stopped by hover\n    const pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\n    const shouldPulse = selectedTemplateTour === \"Hotspot\" ? pulseAnimationEnabled !== false && !hotspot._pulseStopped : hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped;\n    if (shouldPulse) {\n      hotspot.classList.add(\"pulse-animation\");\n      hotspot.classList.remove(\"pulse-animation-removed\");\n    } else {\n      hotspot.classList.remove(\"pulse-animation\");\n      hotspot.classList.add(\"pulse-animation-removed\");\n    }\n\n    // Ensure the hotspot is visible and clickable\n    hotspot.style.display = \"flex\";\n    hotspot.style.pointerEvents = \"auto\";\n\n    // No need for separate animation control functions here\n    // Animation will be controlled directly in the event handlers\n    // Set initial state of openTooltip based on ShowByDefault and ShowUpon\n    const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n    if (showByDefault) {\n      setOpenTooltip(true);\n    } else {\n      // If not showing by default, only show based on interaction type\n      //setOpenTooltip(false);\n    }\n\n    // Only clone and replace if the hotspot doesn't have event listeners already\n    // This prevents losing the _pulseStopped state unnecessarily\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      const newHotspot = hotspot.cloneNode(true);\n      // Copy the _pulseStopped property if it exists\n      if (hotspot._pulseStopped !== undefined) {\n        newHotspot._pulseStopped = hotspot._pulseStopped;\n      }\n      if (hotspot.parentNode) {\n        hotspot.parentNode.replaceChild(newHotspot, hotspot);\n        hotspot = newHotspot;\n      }\n    }\n\n    // Ensure pointer events are enabled\n    hotspot.style.pointerEvents = \"auto\";\n\n    // Define combined event handlers that handle both animation and tooltip\n    const showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\n    const handleHover = e => {\n      e.stopPropagation();\n      console.log(\"Hover detected on hotspot\");\n\n      // Show tooltip if ShowUpon is \"Hovering Hotspot\"\n      if (showUpon === \"Hovering Hotspot\") {\n        // Set openTooltip to true when hovering\n        setOpenTooltip(true);\n\n        // Call the passed hover handler if it exists\n        if (typeof handleHotspotHover === \"function\") {\n          handleHotspotHover();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n    const handleMouseOut = e => {\n      e.stopPropagation();\n\n      // Hide tooltip when mouse leaves the hotspot\n      // Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\n      const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n      if (showUpon === \"Hovering Hotspot\" && !showByDefault) {\n        // setOpenTooltip(false);\n      }\n    };\n    const handleClick = e => {\n      e.stopPropagation();\n      console.log(\"Click detected on hotspot\");\n\n      // Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\n      if (showUpon === \"Clicking Hotspot\" || !showUpon) {\n        // Toggle the tooltip state\n        setOpenTooltip(!openTooltip);\n\n        // Call the passed click handler if it exists\n        if (typeof handleHotspotClick === \"function\") {\n          handleHotspotClick();\n        }\n\n        // Stop animation if configured to do so\n        const stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\n        if (stopAnimationSetting) {\n          hotspot.classList.remove(\"pulse-animation\");\n          hotspot.classList.add(\"pulse-animation-removed\");\n          hotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\n        }\n      }\n    };\n\n    // Add appropriate event listeners based on ShowUpon property\n    if (!hotspot.hasAttribute('data-listeners-attached')) {\n      if (showUpon === \"Hovering Hotspot\") {\n        // For hover interaction\n        hotspot.addEventListener(\"mouseover\", handleHover);\n        hotspot.addEventListener(\"mouseout\", handleMouseOut);\n\n        // Also add click handler for better user experience\n        hotspot.addEventListener(\"click\", handleClick);\n      } else {\n        // For click interaction (default)\n        hotspot.addEventListener(\"click\", handleClick);\n      }\n\n      // Mark that listeners have been attached\n      hotspot.setAttribute('data-listeners-attached', 'true');\n    }\n  };\n  useEffect(() => {\n    let element;\n    let steps;\n    const fetchGuideDetails = async () => {\n      try {\n        var _savedGuideData$Guide23, _savedGuideData$Guide24, _steps, _steps$;\n        //   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\n        steps = (savedGuideData === null || savedGuideData === void 0 ? void 0 : savedGuideData.GuideStep) || [];\n\n        // For tour hotspots, use the current step's element path\n        const elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData !== null && savedGuideData !== void 0 && (_savedGuideData$Guide23 = savedGuideData.GuideStep) !== null && _savedGuideData$Guide23 !== void 0 && (_savedGuideData$Guide24 = _savedGuideData$Guide23[currentStep - 1]) !== null && _savedGuideData$Guide24 !== void 0 && _savedGuideData$Guide24.ElementPath ? savedGuideData.GuideStep[currentStep - 1].ElementPath : ((_steps = steps) === null || _steps === void 0 ? void 0 : (_steps$ = _steps[0]) === null || _steps$ === void 0 ? void 0 : _steps$.ElementPath) || \"\";\n        element = getElementByXPath(elementPath || \"\");\n        setTargetElement(element);\n        if (element && validateElementPosition(element)) {\n          // element.style.outline = \"2px solid red\";\n\n          // Check if element is visible and scroll to it if needed\n          const rect = element.getBoundingClientRect();\n          const viewportHeight = window.innerHeight;\n          const viewportWidth = window.innerWidth;\n\n          // Check if element is completely out of view or barely visible\n          const isCompletelyOutOfView = rect.bottom < 0 ||\n          // Completely above viewport\n          rect.top > viewportHeight ||\n          // Completely below viewport\n          rect.right < 0 ||\n          // Completely left of viewport\n          rect.left > viewportWidth // Completely right of viewport\n          ;\n\n          // More generous visibility check - element should be reasonably visible\n          const isReasonablyVisible = rect.top >= -50 &&\n          // Allow some element to be above viewport\n          rect.left >= -50 &&\n          // Allow some element to be left of viewport\n          rect.bottom <= viewportHeight + 50 &&\n          // Allow some element below viewport\n          rect.right <= viewportWidth + 50 &&\n          // Allow some element right of viewport\n          rect.width > 0 && rect.height > 0;\n\n          // Add a small delay to ensure proper positioning\n          setTimeout(() => {\n            if (element && (isCompletelyOutOfView || !isReasonablyVisible)) {\n              console.log(\"🎯 Hotspot element not visible, performing auto-scroll\");\n              // Element is not visible, scroll to bring it into view\n              try {\n                element.scrollIntoView({\n                  behavior: 'smooth',\n                  block: 'center',\n                  // Center it for better visibility\n                  inline: 'nearest'\n                });\n                console.log(\"✅ Hotspot auto-scroll completed successfully\");\n              } catch (scrollError) {\n                console.error(\"❌ Hotspot auto-scroll failed:\", scrollError);\n              }\n            } else if (element) {\n              console.log(\"✅ Hotspot element is reasonably visible, minimal adjustment\");\n              // Element is mostly visible, just ensure it's well positioned\n              try {\n                element.scrollIntoView({\n                  behavior: 'smooth',\n                  block: 'nearest',\n                  // Don't force center if already visible\n                  inline: 'nearest'\n                });\n              } catch (error) {\n                console.log(\"Hotspot minimal scroll adjustment failed:\", error);\n              }\n            }\n          }, 100);\n        }\n\n        // Check if this is a hotspot scenario (normal or tour)\n        const isHotspotScenario = selectedTemplate === \"Hotspot\" || selectedTemplateTour === \"Hotspot\" || title === \"Hotspot\" || selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\";\n        if (isHotspotScenario) {\n          var _toolTipGuideMetaData7, _toolTipGuideMetaData8, _hotspotPropData, _hotspotPropData2, _hotspotPropData3, _hotspotPropData4;\n          // Get hotspot properties - prioritize tour data for tour hotspots\n          let hotspotPropData;\n          let hotspotData;\n          if (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData7 = toolTipGuideMetaData[currentStep - 1]) !== null && _toolTipGuideMetaData7 !== void 0 && _toolTipGuideMetaData7.hotspots) {\n            var _savedGuideData$Guide25, _savedGuideData$Guide26;\n            // Tour hotspot - use current step metadata\n            hotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide25 = savedGuideData.GuideStep) === null || _savedGuideData$Guide25 === void 0 ? void 0 : (_savedGuideData$Guide26 = _savedGuideData$Guide25[currentStep - 1]) === null || _savedGuideData$Guide26 === void 0 ? void 0 : _savedGuideData$Guide26.Hotspot;\n          } else if (toolTipGuideMetaData !== null && toolTipGuideMetaData !== void 0 && (_toolTipGuideMetaData8 = toolTipGuideMetaData[0]) !== null && _toolTipGuideMetaData8 !== void 0 && _toolTipGuideMetaData8.hotspots) {\n            var _savedGuideData$Guide27, _savedGuideData$Guide28;\n            // Normal hotspot - use first metadata entry\n            hotspotPropData = toolTipGuideMetaData[0].hotspots;\n            hotspotData = savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide27 = savedGuideData.GuideStep) === null || _savedGuideData$Guide27 === void 0 ? void 0 : (_savedGuideData$Guide28 = _savedGuideData$Guide27[0]) === null || _savedGuideData$Guide28 === void 0 ? void 0 : _savedGuideData$Guide28.Hotspot;\n          } else {\n            var _savedGuideData$Guide29, _savedGuideData$Guide30;\n            // Fallback to default values for tour hotspots without metadata\n            hotspotPropData = {\n              XPosition: \"4\",\n              YPosition: \"4\",\n              Type: \"Question\",\n              Color: \"yellow\",\n              Size: \"16\",\n              PulseAnimation: true,\n              stopAnimationUponInteraction: true,\n              ShowUpon: \"Hovering Hotspot\",\n              ShowByDefault: false\n            };\n            hotspotData = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide29 = savedGuideData.GuideStep) === null || _savedGuideData$Guide29 === void 0 ? void 0 : (_savedGuideData$Guide30 = _savedGuideData$Guide29[currentStep - 1]) === null || _savedGuideData$Guide30 === void 0 ? void 0 : _savedGuideData$Guide30.Hotspot) || {};\n          }\n          const xOffset = parseFloat(((_hotspotPropData = hotspotPropData) === null || _hotspotPropData === void 0 ? void 0 : _hotspotPropData.XPosition) || \"4\");\n          const yOffset = parseFloat(((_hotspotPropData2 = hotspotPropData) === null || _hotspotPropData2 === void 0 ? void 0 : _hotspotPropData2.YPosition) || \"4\");\n          const currentHotspotSize = parseFloat(((_hotspotPropData3 = hotspotPropData) === null || _hotspotPropData3 === void 0 ? void 0 : _hotspotPropData3.Size) || \"30\");\n\n          // Update hotspot size state\n          setHotspotSize(currentHotspotSize);\n          let left, top;\n          if (element) {\n            const rect = element.getBoundingClientRect();\n            left = rect.x + xOffset;\n            top = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\n\n            // Calculate popup position below the hotspot\n            const popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\n            setPopupPosition(popupPos);\n          }\n\n          // Check if hotspot already exists, preserve it to maintain _pulseStopped state\n          const existingHotspot = document.getElementById(\"hotspotBlink\");\n          if (existingHotspot) {\n            hotspot = existingHotspot;\n            // Don't reset _pulseStopped if it already exists\n          } else {\n            // Create new hotspot only if it doesn't exist\n            hotspot = document.createElement(\"div\");\n            hotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\n            hotspot._pulseStopped = false; // Set only on creation\n            document.body.appendChild(hotspot);\n          }\n          hotspot.style.cursor = \"pointer\";\n          hotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\n\n          // Make sure the hotspot is visible and clickable\n          hotspot.style.zIndex = \"9999\";\n\n          // If ShowByDefault is true, set openTooltip to true immediately\n          if ((_hotspotPropData4 = hotspotPropData) !== null && _hotspotPropData4 !== void 0 && _hotspotPropData4.ShowByDefault) {\n            setOpenTooltip(true);\n          }\n\n          // Set styles first\n          applyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\n\n          // Set initial tooltip visibility based on ShowByDefault\n          const showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\n          if (showByDefault) {\n            setOpenTooltip(true);\n          } else {\n            //setOpenTooltip(false);\n          }\n\n          // We don't need to add event listeners here as they're already added in applyHotspotStyles\n        }\n      } catch (error) {\n        console.error(\"Error in fetchGuideDetails:\", error);\n      }\n    };\n    fetchGuideDetails();\n    return () => {\n      const existingHotspot = document.getElementById(\"hotspotBlink\");\n      if (existingHotspot) {\n        existingHotspot.onclick = null;\n        existingHotspot.onmouseover = null;\n        existingHotspot.onmouseout = null;\n      }\n    };\n  }, [savedGuideData, toolTipGuideMetaData, isHotspotPopupOpen, showHotspotenduser, selectedTemplateTour, currentStep\n  // Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\n  ]);\n  const enableProgress = (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide31 = savedGuideData.GuideStep) === null || _savedGuideData$Guide31 === void 0 ? void 0 : (_savedGuideData$Guide32 = _savedGuideData$Guide31[0]) === null || _savedGuideData$Guide32 === void 0 ? void 0 : (_savedGuideData$Guide33 = _savedGuideData$Guide32.Tooltip) === null || _savedGuideData$Guide33 === void 0 ? void 0 : _savedGuideData$Guide33.EnableProgress) || false;\n  function getProgressTemplate(selectedOption) {\n    var _savedGuideData$Guide34, _savedGuideData$Guide35, _savedGuideData$Guide36;\n    if (selectedOption === 1) {\n      return \"dots\";\n    } else if (selectedOption === 2) {\n      return \"linear\";\n    } else if (selectedOption === 3) {\n      return \"BreadCrumbs\";\n    } else if (selectedOption === 4) {\n      return \"breadcrumbs\";\n    }\n    return (savedGuideData === null || savedGuideData === void 0 ? void 0 : (_savedGuideData$Guide34 = savedGuideData.GuideStep) === null || _savedGuideData$Guide34 === void 0 ? void 0 : (_savedGuideData$Guide35 = _savedGuideData$Guide34[0]) === null || _savedGuideData$Guide35 === void 0 ? void 0 : (_savedGuideData$Guide36 = _savedGuideData$Guide35.Tooltip) === null || _savedGuideData$Guide36 === void 0 ? void 0 : _savedGuideData$Guide36.ProgressTemplate) || \"dots\";\n  }\n  const progressTemplate = getProgressTemplate(selectedOption);\n  const renderProgress = () => {\n    if (!enableProgress) return null;\n    if (progressTemplate === \"dots\") {\n      return /*#__PURE__*/_jsxDEV(MobileStepper, {\n        variant: \"dots\",\n        steps: totalSteps,\n        position: \"static\",\n        activeStep: currentStep - 1,\n        sx: {\n          backgroundColor: \"transparent\",\n          position: \"inherit !important\",\n          \"& .MuiMobileStepper-dotActive\": {\n            backgroundColor: ProgressColor // Active dot\n          }\n        },\n        backButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1235,\n          columnNumber: 18\n        }, this),\n        nextButton: /*#__PURE__*/_jsxDEV(Button, {\n          style: {\n            visibility: \"hidden\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1236,\n          columnNumber: 18\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1223,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"BreadCrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"center\",\n          gap: \"5px\",\n          padding: \"8px\"\n        },\n        children: Array.from({\n          length: totalSteps\n        }).map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: \"14px\",\n            height: \"4px\",\n            backgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\",\n            // Active color and inactive color\n            borderRadius: \"100px\"\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1246,\n          columnNumber: 7\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1242,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"breadcrumbs\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\",\n          placeContent: \"flex-start\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            padding: \"8px\",\n            color: ProgressColor\n          },\n          children: [\"Step \", currentStep, \" of \", totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1262,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1261,\n        columnNumber: 5\n      }, this);\n    }\n    if (progressTemplate === \"linear\") {\n      return /*#__PURE__*/_jsxDEV(Box, {\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n            variant: \"determinate\",\n            value: progress,\n            sx: {\n              height: \"6px\",\n              borderRadius: \"20px\",\n              margin: \"6px 10px\",\n              \"& .MuiLinearProgress-bar\": {\n                backgroundColor: ProgressColor // progress bar color\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1273,\n            columnNumber: 7\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1272,\n          columnNumber: 6\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1271,\n        columnNumber: 5\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [targetElement && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: openTooltip && /*#__PURE__*/_jsxDEV(Popover, {\n        open: Boolean(popupPosition) || Boolean(anchorEl),\n        anchorEl: anchorEl,\n        onClose: () => {\n          // We want to keep the tooltip open once it's been displayed\n          // So we're not closing it on Popover close events\n        },\n        anchorOrigin: anchorOrigin,\n        transformOrigin: transformOrigin,\n        anchorReference: \"anchorPosition\",\n        anchorPosition: popupPosition ? {\n          top: popupPosition.top + 10 + (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\n          left: popupPosition.left + 10 + parseFloat(tooltipXaxis || \"0\")\n        } : undefined,\n        sx: {\n          // \"& .MuiBackdrop-root\": {\n          //     position: 'relative !important', // Ensures higher specificity\n          // },\n          \"pointer-events\": anchorEl ? \"auto\" : \"auto\",\n          '& .MuiPaper-root:not(.MuiMobileStepper-root)': {\n            zIndex: 1000,\n            // borderRadius: \"1px\",\n            ...canvasStyle,\n            //...getAnchorAndTransformOrigins,\n            //top: \"16% !important\",\n            // top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\n            //     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\n            //         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\n            //             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\n            //                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\n            //                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\n            //                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\n            //                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\n            //                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\n            ...getCanvasPosition((canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Position) || \"center-center\"),\n            top: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.top) || 0) + (tooltipYaxis && tooltipYaxis != 'undefined' ? parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\")) : 0)}px !important`,\n            left: `${((popupPosition === null || popupPosition === void 0 ? void 0 : popupPosition.left) || 0) + (tooltipXaxis && tooltipXaxis != 'undefined' ? parseFloat(tooltipXaxis) || 0 : 0)}px !important`,\n            overflow: \"hidden\"\n          }\n        },\n        disableScrollLock: true,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            placeContent: \"end\",\n            display: \"flex\"\n          },\n          children: (modalProperties === null || modalProperties === void 0 ? void 0 : modalProperties.DismissOption) && /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => {\n              // Only close if explicitly requested by user clicking the close button\n              //setOpenTooltip(false);\n            },\n            sx: {\n              position: \"fixed\",\n              boxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\n              left: \"auto\",\n              right: \"auto\",\n              margin: \"-15px\",\n              background: \"#fff !important\",\n              border: \"1px solid #ccc\",\n              zIndex: \"999999\",\n              borderRadius: \"50px\",\n              padding: \"5px !important\"\n            },\n            children: /*#__PURE__*/_jsxDEV(CloseIcon, {\n              sx: {\n                zoom: 1,\n                color: \"#000\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1383,\n              columnNumber: 11\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1365,\n            columnNumber: 10\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1363,\n          columnNumber: 8\n        }, this), /*#__PURE__*/_jsxDEV(PerfectScrollbar, {\n          ref: scrollbarRef,\n          style: {\n            maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\"\n          },\n          options: {\n            suppressScrollY: !needsScrolling,\n            suppressScrollX: true,\n            wheelPropagation: false,\n            swipeEasing: true,\n            minScrollbarLength: 20,\n            scrollingThreshold: 1000,\n            scrollYMarginOffset: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\n              overflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\n              width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n              margin: hasOnlyButtons() ? \"0\" : undefined\n            },\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              style: {\n                padding: hasOnlyButtons() ? \"0\" : hasOnlyText() ? \"0\" : (canvasProperties === null || canvasProperties === void 0 ? void 0 : canvasProperties.Padding) || \"10px\",\n                height: hasOnlyButtons() ? \"auto\" : sectionHeight,\n                width: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\n                margin: hasOnlyButtons() ? \"0\" : undefined\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                ref: contentRef,\n                display: \"flex\",\n                flexDirection: \"column\",\n                flexWrap: \"wrap\",\n                justifyContent: \"center\",\n                sx: {\n                  width: hasOnlyText() ? \"auto\" : \"100%\",\n                  padding: hasOnlyText() ? \"0\" : undefined\n                },\n                children: [imageProperties === null || imageProperties === void 0 ? void 0 : imageProperties.map(imageProp => imageProp.CustomImage.map((customImg, imgIndex) => /*#__PURE__*/_jsxDEV(Box, {\n                  component: \"img\",\n                  src: customImg.Url,\n                  alt: customImg.AltText || \"Image\",\n                  sx: {\n                    maxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\n                    textAlign: imageProp.Alignment || \"center\",\n                    objectFit: customImg.Fit || \"contain\",\n                    //  width: \"500px\",\n                    height: `${customImg.SectionHeight || 250}px`,\n                    background: customImg.BackgroundColor || \"#ffffff\",\n                    margin: \"10px 0\"\n                  },\n                  onClick: () => {\n                    if (imageProp.Hyperlink) {\n                      const targetUrl = imageProp.Hyperlink;\n                      window.open(targetUrl, \"_blank\", \"noopener noreferrer\");\n                    }\n                  },\n                  style: {\n                    cursor: imageProp.Hyperlink ? \"pointer\" : \"default\"\n                  }\n                }, `${imageProp.Id}-${imgIndex}`, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1427,\n                  columnNumber: 13\n                }, this))), textFieldProperties === null || textFieldProperties === void 0 ? void 0 : textFieldProperties.map((textField, index) => {\n                  var _textField$TextProper, _textField$TextProper2;\n                  return textField.Text && /*#__PURE__*/_jsxDEV(Typography, {\n                    className: \"qadpt-preview\",\n                    // Use a unique key, either Id or index\n                    sx: {\n                      textAlign: ((_textField$TextProper = textField.TextProperties) === null || _textField$TextProper === void 0 ? void 0 : _textField$TextProper.TextFormat) || textStyle.textAlign,\n                      color: ((_textField$TextProper2 = textField.TextProperties) === null || _textField$TextProper2 === void 0 ? void 0 : _textField$TextProper2.TextColor) || textStyle.color,\n                      whiteSpace: \"pre-wrap\",\n                      wordBreak: \"break-word\",\n                      padding: \"0 5px\"\n                    },\n                    dangerouslySetInnerHTML: renderHtmlSnippet(textField.Text) // Render the raw HTML\n                  }, textField.Id || index, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1455,\n                    columnNumber: 14\n                  }, this);\n                })]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1414,\n                columnNumber: 10\n              }, this), Object.keys(groupedButtons).map(containerId => {\n                var _groupedButtons$conta, _groupedButtons$conta2;\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  ref: buttonContainerRef,\n                  sx: {\n                    display: \"flex\",\n                    justifyContent: getAlignment((_groupedButtons$conta = groupedButtons[containerId][0]) === null || _groupedButtons$conta === void 0 ? void 0 : _groupedButtons$conta.Alignment),\n                    flexWrap: \"wrap\",\n                    margin: hasOnlyButtons() ? 0 : \"5px 0\",\n                    backgroundColor: (_groupedButtons$conta2 = groupedButtons[containerId][0]) === null || _groupedButtons$conta2 === void 0 ? void 0 : _groupedButtons$conta2.BackgroundColor,\n                    padding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\n                    width: hasOnlyButtons() ? \"auto\" : \"100%\",\n                    borderRadius: hasOnlyButtons() ? \"15px\" : undefined\n                  },\n                  children: groupedButtons[containerId].map((button, index) => {\n                    var _button$ButtonPropert, _button$ButtonPropert2, _button$ButtonPropert3, _button$ButtonPropert4, _button$ButtonPropert5, _button$ButtonPropert6, _button$ButtonPropert7;\n                    return /*#__PURE__*/_jsxDEV(Button, {\n                      onClick: () => handleButtonAction(button.ButtonAction),\n                      variant: \"contained\",\n                      sx: {\n                        marginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\n                        margin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\n                        backgroundColor: ((_button$ButtonPropert = button.ButtonProperties) === null || _button$ButtonPropert === void 0 ? void 0 : _button$ButtonPropert.ButtonBackgroundColor) || \"#007bff\",\n                        color: ((_button$ButtonPropert2 = button.ButtonProperties) === null || _button$ButtonPropert2 === void 0 ? void 0 : _button$ButtonPropert2.ButtonTextColor) || \"#fff\",\n                        border: ((_button$ButtonPropert3 = button.ButtonProperties) === null || _button$ButtonPropert3 === void 0 ? void 0 : _button$ButtonPropert3.ButtonBorderColor) || \"transparent\",\n                        fontSize: ((_button$ButtonPropert4 = button.ButtonProperties) === null || _button$ButtonPropert4 === void 0 ? void 0 : _button$ButtonPropert4.FontSize) || \"15px\",\n                        width: ((_button$ButtonPropert5 = button.ButtonProperties) === null || _button$ButtonPropert5 === void 0 ? void 0 : _button$ButtonPropert5.Width) || \"auto\",\n                        padding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\n                        lineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\n                        textTransform: \"none\",\n                        borderRadius: ((_button$ButtonPropert6 = button.ButtonProperties) === null || _button$ButtonPropert6 === void 0 ? void 0 : _button$ButtonPropert6.BorderRadius) || \"8px\",\n                        minWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\n                        boxShadow: \"none !important\",\n                        // Remove box shadow in normal state\n                        \"&:hover\": {\n                          backgroundColor: ((_button$ButtonPropert7 = button.ButtonProperties) === null || _button$ButtonPropert7 === void 0 ? void 0 : _button$ButtonPropert7.ButtonBackgroundColor) || \"#007bff\",\n                          // Keep the same background color on hover\n                          opacity: 0.9,\n                          // Slightly reduce opacity on hover for visual feedback\n                          boxShadow: \"none !important\" // Remove box shadow in hover state\n                        }\n                      },\n                      children: button.ButtonName\n                    }, index, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1487,\n                      columnNumber: 13\n                    }, this);\n                  })\n                }, containerId, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1472,\n                  columnNumber: 11\n                }, this);\n              })]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1407,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1401,\n            columnNumber: 8\n          }, this)\n        }, `scrollbar-${needsScrolling}`, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1387,\n          columnNumber: 8\n        }, this), enableProgress && totalSteps > 1 && selectedTemplate === \"Tour\" && /*#__PURE__*/_jsxDEV(Box, {\n          children: renderProgress()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1523,\n          columnNumber: 75\n        }, this), \" \"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1310,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1295,\n      columnNumber: 5\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          @keyframes pulse {\n            0% {\n              transform: scale(1);\n              opacity: 1;\n            }\n            50% {\n              transform: scale(1.5);\n              opacity: 0.6;\n            }\n            100% {\n              transform: scale(1);\n              opacity: 1;\n            }\n          }\n\n          .pulse-animation {\n            animation: pulse 1.5s infinite;\n            pointer-events: auto !important;\n          }\n\n          .pulse-animation-removed {\n            pointer-events: auto !important;\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1529,\n      columnNumber: 4\n    }, this)]\n  }, void 0, true);\n};\n_s(HotspotPreview, \"PgDB4N+0EhrNv098qSwM/HOvCCQ=\", false, function () {\n  return [useDrawerStore];\n});\n_c = HotspotPreview;\nexport default HotspotPreview;\nvar _c;\n$RefreshReg$(_c, \"HotspotPreview\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "Box", "<PERSON><PERSON>", "IconButton", "LinearProgress", "MobileStepper", "Popover", "Typography", "CloseIcon", "useDrawerStore", "PerfectScrollbar", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "HotspotPreview", "anchorEl", "guideStep", "title", "text", "imageUrl", "onClose", "onPrevious", "onContinue", "videoUrl", "currentStep", "totalSteps", "onDontShowAgain", "progress", "textFieldProperties", "imageProperties", "customButton", "modalProperties", "canvasProperties", "htmlSnippet", "previousButtonStyles", "continueButtonStyles", "OverlayValue", "savedGuideData", "hotspotProperties", "handleHotspotHover", "handleHotspotClick", "isHotspotPopupOpen", "showHotspotenduser", "_s", "_savedGuideData$Guide", "_savedGuideData$Guide2", "_textFieldProperties$", "_textFieldProperties$2", "_textFieldProperties$3", "_imageProperties", "_imageProperties$Cust", "_imageProperties$Cust2", "_savedGuideData$Guide31", "_savedGuideData$Guide32", "_savedGuideData$Guide33", "setCurrentStep", "selectedTemplate", "toolTipGuideMetaData", "elementSelected", "axisData", "tooltipXaxis", "tooltipYaxis", "setOpenTooltip", "openTooltip", "pulseAnimationsH", "hotspotGuideMetaData", "selectedTemplateTour", "selectedOption", "ProgressColor", "state", "targetElement", "setTargetElement", "popupPosition", "setPopupPosition", "dynamicWidth", "setDynamicWidth", "hotspotSize", "setHotspotSize", "contentRef", "buttonContainerRef", "hotspot", "getElementByXPath", "xpath", "result", "document", "evaluate", "XPathResult", "FIRST_ORDERED_NODE_TYPE", "node", "singleNodeValue", "HTMLElement", "parentElement", "error", "console", "validateElementPosition", "element", "rect", "getBoundingClientRect", "width", "height", "top", "left", "Number", "isNaN", "GuideStep", "<PERSON>ement<PERSON><PERSON>", "getElementPosition", "needsScrolling", "setNeedsScrolling", "scrollbarRef", "calculatePopupPosition", "elementRect", "xOffset", "yOffset", "hotspotLeft", "x", "hotspotTop", "y", "dynamicOffsetX", "dynamicOffsetY", "window", "scrollY", "scrollX", "undefined", "position", "_guideStep", "style", "backgroundColor", "viewportHeight", "innerHeight", "viewportWidth", "innerWidth", "isCompletelyOutOfView", "bottom", "right", "isReasonablyVisible", "setTimeout", "log", "scrollIntoView", "behavior", "block", "inline", "scrollError", "setOverlayValue", "handleContinue", "renderNextPopup", "existingHotspot", "getElementById", "display", "remove", "shouldRenderNextPopup", "_savedGuideData$Guide3", "_savedGuideData$Guide4", "_savedGuideData$Guide5", "_savedGuideData$Guide6", "_savedGuideData$Guide7", "_savedGuideData$Guide8", "_savedGuideData$Guide9", "_savedGuideData$Guide10", "_savedGuideData$Guide11", "_savedGuideData$Guide12", "handlePrevious", "TextFieldProperties", "ImageProperties", "ButtonSection", "map", "section", "CustomButtons", "button", "ContainerId", "Id", "reduce", "acc", "curr", "concat", "Hotspot", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAnchorAndTransformOrigins", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "transform<PERSON><PERSON>in", "Position", "textStyle", "fontWeight", "TextProperties", "Bold", "fontStyle", "Italic", "color", "TextColor", "textAlign", "Alignment", "renderHtmlSnippet", "snippet", "__html", "replace", "_match", "p1", "p2", "p3", "hasOnlyButtons", "hasImage", "length", "some", "prop", "CustomImage", "img", "Url", "hasText", "field", "Text", "trim", "hasButtons", "hasOnlyText", "calculateOptimalWidth", "_contentRef$current", "_buttonContainerRef$c", "<PERSON><PERSON><PERSON>", "contentWidth", "current", "scrollWidth", "buttonWidth", "optimalWidth", "Math", "max", "<PERSON><PERSON><PERSON><PERSON>", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "finalWidth", "min", "requestAnimationFrame", "newWidth", "_toolTipGuideMetaData", "hotspotPropData", "hotspots", "parseFloat", "XPosition", "YPosition", "popupPos", "handleResize", "_toolTipGuideMetaData2", "addEventListener", "removeEventListener", "groupedButtons", "containerId", "push", "canvasStyle", "borderRadius", "<PERSON><PERSON>", "borderWidth", "BorderSize", "borderColor", "BorderColor", "borderStyle", "BackgroundColor", "sectionHeight", "SectionHeight", "handleButtonAction", "action", "Action", "targetUrl", "TargetUrl", "ActionValue", "location", "href", "open", "_savedGuideData$Guide13", "_savedGuideData$Guide14", "firstStepElement", "_guideStep2", "_guideStep2$Hotspot", "ShowByDefault", "_toolTipGuideMetaData3", "_toolTipGuideMetaData4", "_savedGuideData$Guide15", "_savedGuideData$Guide16", "_savedGuideData$Guide17", "_savedGuideData$Guide18", "hotspotData", "_toolTipGuideMetaData5", "_toolTipGuideMetaData6", "_savedGuideData$Guide19", "_savedGuideData$Guide20", "_savedGuideData$Guide21", "_savedGuideData$Guide22", "handleGlobalClick", "e", "hotspotElement", "contains", "target", "checkScrollNeeded", "contentHeight", "scrollHeight", "containerHeight", "shouldScroll", "updateScroll", "timeouts", "resizeObserver", "mutationObserver", "ResizeObserver", "observe", "MutationObserver", "childList", "subtree", "attributes", "attributeFilter", "for<PERSON>ach", "clearTimeout", "disconnect", "getAlignment", "alignment", "getCanvasPosition", "getHotspotProperty", "propName", "PulseAnimation", "stopAnimationUponInteraction", "ShowUpon", "applyHotspotStyles", "Size", "Color", "zIndex", "transition", "pointerEvents", "innerHTML", "Type", "textSpan", "createElement", "innerText", "fontSize", "alignItems", "justifyContent", "append<PERSON><PERSON><PERSON>", "pulseAnimationEnabled", "shouldPulse", "_pulseStopped", "classList", "add", "showByDefault", "hasAttribute", "newHotspot", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "showUpon", "handleHover", "stopPropagation", "stopAnimationSetting", "handleMouseOut", "handleClick", "setAttribute", "steps", "fetchGuideDetails", "_savedGuideData$Guide23", "_savedGuideData$Guide24", "_steps", "_steps$", "elementPath", "isHotspotScenario", "_toolTipGuideMetaData7", "_toolTipGuideMetaData8", "_hotspotPropData", "_hotspotPropData2", "_hotspotPropData3", "_hotspotPropData4", "_savedGuideData$Guide25", "_savedGuideData$Guide26", "_savedGuideData$Guide27", "_savedGuideData$Guide28", "_savedGuideData$Guide29", "_savedGuideData$Guide30", "currentHotspotSize", "abs", "id", "body", "cursor", "onclick", "on<PERSON><PERSON>ver", "onmouseout", "enableProgress", "<PERSON><PERSON><PERSON>", "EnableProgress", "getProgressTemplate", "_savedGuideData$Guide34", "_savedGuideData$Guide35", "_savedGuideData$Guide36", "ProgressTemplate", "progressTemplate", "renderProgress", "variant", "activeStep", "sx", "backButton", "visibility", "nextButton", "place<PERSON><PERSON>nt", "gap", "padding", "children", "Array", "from", "_", "index", "value", "margin", "Boolean", "anchorReference", "anchorPosition", "overflow", "disableScrollLock", "DismissOption", "onClick", "boxShadow", "background", "border", "zoom", "ref", "maxHeight", "options", "suppressScrollY", "suppressScrollX", "wheelPropagation", "swipeEasing", "minScrollbar<PERSON><PERSON>th", "scrollingT<PERSON>eshold", "scrollYMarginOffset", "Padding", "flexDirection", "flexWrap", "imageProp", "customImg", "imgIndex", "component", "src", "alt", "AltText", "MaxImageHeight", "objectFit", "Fit", "Hyperlink", "textField", "_textField$TextProper", "_textField$TextProper2", "className", "TextFormat", "whiteSpace", "wordBreak", "dangerouslySetInnerHTML", "Object", "keys", "_groupedButtons$conta", "_groupedButtons$conta2", "_button$ButtonPropert", "_button$ButtonPropert2", "_button$ButtonPropert3", "_button$ButtonPropert4", "_button$ButtonPropert5", "_button$ButtonPropert6", "_button$ButtonPropert7", "ButtonAction", "marginRight", "ButtonProperties", "ButtonBackgroundColor", "ButtonTextColor", "ButtonBorderColor", "FontSize", "lineHeight", "textTransform", "BorderRadius", "opacity", "ButtonName", "_c", "$RefreshReg$"], "sources": ["E:/code/QuickAdopt/quickadapt/QuickAdaptExtension/src/components/GuidesPreview/HotspotPreview.tsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\r\nimport { Box, Button, IconButton, LinearProgress, MobileStepper, Popover, PopoverOrigin, Typography } from \"@mui/material\";\r\nimport { GuideData } from \"../drawer/Drawer\";\r\nimport CloseIcon from \"@mui/icons-material/Close\";\r\nimport useDrawerStore, { DrawerState } from \"../../store/drawerStore\";\r\n// import { GetGudeDetailsByGuideId } from \"../../services/GuideListServices\";\r\nimport PerfectScrollbar from 'react-perfect-scrollbar';\r\nimport 'react-perfect-scrollbar/dist/css/styles.css';\r\n\r\n\r\n\r\ninterface ButtonAction {\r\n  Action: string;\r\n  ActionValue: string;\r\n  TargetUrl: string;\r\n}\r\ninterface PopupProps {\r\n    isHotspotPopupOpen: any;\r\n    showHotspotenduser: any;\r\n    anchorEl: null | HTMLElement;\r\n    guideStep: any[];\r\n    title: string;\r\n    text: string;\r\n    imageUrl?: string;\r\n    videoUrl?: string;\r\n    previousButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    continueButtonStyles?: {\r\n        backgroundColor?: string;\r\n        textColor?: string;\r\n        borderColor?: string;\r\n    };\r\n    onClose: () => void;\r\n    onPrevious: () => void;\r\n    onContinue: () => void;\r\n    currentStep: number;\r\n    totalSteps: number;\r\n    onDontShowAgain: () => void;\r\n    progress: number;\r\n    textFieldProperties?: any;\r\n    imageProperties?: any;\r\n    customButton?: any;\r\n    modalProperties?: { InteractionWithPopup?: boolean; IncludeRequisiteButtons?: boolean; DismissOption?: boolean; ModalPlacedOn?: string };\r\n    canvasProperties?: {\r\n        Position?: string;\r\n        Padding?: string;\r\n        Radius?: string;\r\n        BorderSize?: string;\r\n        BorderColor?: string;\r\n        BackgroundColor?: string;\r\n        Width?: string;\r\n    };\r\n    htmlSnippet: string;\r\n    OverlayValue: boolean;\r\n    savedGuideData: GuideData | null;\r\n    hotspotProperties: any;\r\n    handleHotspotHover: () => any;\r\n    handleHotspotClick: () => any;\r\n}\r\n\r\ninterface ButtonProperties {\r\n  Padding: number;\r\n  Width: number;\r\n  Font: number;\r\n  FontSize: number;\r\n  ButtonTextColor: string;\r\n  ButtonBackgroundColor: string;\r\n}\r\n\r\ninterface ButtonData {\r\n  ButtonStyle: string;\r\n  ButtonName: string;\r\n  Alignment: string;\r\n  BackgroundColor: string;\r\n  ButtonAction: ButtonAction;\r\n  Padding: {\r\n    Top: number;\r\n    Right: number;\r\n    Bottom: number;\r\n    Left: number;\r\n  };\r\n  ButtonProperties: ButtonProperties;\r\n}\r\n\r\ninterface HotspotProperties {\r\n  size: string;\r\n  type: string;\r\n  color: string;\r\n  showUpon: string;\r\n  showByDefault: boolean;\r\n  stopAnimation: boolean;\r\n  pulseAnimation: boolean;\r\n  position: {\r\n    XOffset: string;\r\n    YOffset: string;\r\n  };\r\n}\r\n\r\ninterface Step {\r\n  xpath: string;\r\n  hotspotProperties: HotspotProperties;\r\n  content: string | JSX.Element;\r\n  targetUrl: string;\r\n  imageUrl: string;\r\n  buttonData: ButtonData[];\r\n}\r\n\r\ninterface HotspotGuideProps {\r\n  steps: Step[];\r\n  currentUrl: string;\r\n  onClose: () => void;\r\n}\r\n\r\nconst HotspotPreview: React.FC<PopupProps> = ({\r\n    anchorEl,\r\n    guideStep,\r\n    title,\r\n    text,\r\n    imageUrl,\r\n    onClose,\r\n    onPrevious,\r\n    onContinue,\r\n    videoUrl,\r\n    currentStep,\r\n    totalSteps,\r\n    onDontShowAgain,\r\n    progress,\r\n    textFieldProperties,\r\n    imageProperties,\r\n    customButton,\r\n    modalProperties,\r\n    canvasProperties,\r\n    htmlSnippet,\r\n    previousButtonStyles,\r\n    continueButtonStyles,\r\n    OverlayValue,\r\n    savedGuideData,\r\n    hotspotProperties,\r\n    handleHotspotHover,\r\n    handleHotspotClick,\r\n    isHotspotPopupOpen,\r\n   showHotspotenduser\r\n\r\n}) => {\r\n\tconst {\r\n\t\tsetCurrentStep,\r\n\t\tselectedTemplate,\r\n\t\ttoolTipGuideMetaData,\r\n\t\telementSelected,\r\n\t\taxisData,\r\n\t\ttooltipXaxis,\r\n\t\ttooltipYaxis,\r\n\t\tsetOpenTooltip,\r\n\t\topenTooltip,\r\n\t\tpulseAnimationsH,\r\n\t\thotspotGuideMetaData,\r\n\t\tselectedTemplateTour,\r\n\t\tselectedOption,\r\n\t\tProgressColor,\r\n\t} = useDrawerStore((state: DrawerState) => state);\r\n\tconst [targetElement, setTargetElement] = useState<HTMLElement | null>(null);\r\n\t// State to track if the popover should be shown\r\n\t// State for popup visibility is managed through openTooltip\r\n\tconst [popupPosition, setPopupPosition] = useState<{ top: number; left: number } | null>(null);\r\n\tconst [dynamicWidth, setDynamicWidth] = useState<string | null>(null);\r\n\tconst [hotspotSize, setHotspotSize] = useState<number>(30); // Track hotspot size for dynamic popup positioning\r\n\tconst contentRef = useRef<HTMLDivElement>(null);\r\n\tconst buttonContainerRef = useRef<HTMLDivElement>(null);\r\n\tlet hotspot: any;\r\n\tconst getElementByXPath = (xpath: string): HTMLElement | null => {\r\n\t\tif (!xpath) return null;\r\n\t\ttry {\r\n\t\t\tconst result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);\r\n\t\t\tconst node = result.singleNodeValue;\r\n\t\t\tif (node instanceof HTMLElement) {\r\n\t\t\t\treturn node;\r\n\t\t\t} else if (node?.parentElement) {\r\n\t\t\t\treturn node.parentElement; // Return parent if it's a text node\r\n\t\t\t} else {\r\n\t\t\t\treturn null;\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error(\"Error evaluating XPath:\", xpath, error);\r\n\t\t\treturn null;\r\n\t\t}\r\n\t};\r\n\r\n\tconst validateElementPosition = (element: HTMLElement) => {\r\n\t\tconst rect = element.getBoundingClientRect();\r\n\t\treturn (\r\n\t\t\trect.width > 0 &&\r\n\t\t\trect.height > 0 &&\r\n\t\t\trect.top !== 0 &&\r\n\t\t\trect.left !== 0 &&\r\n\t\t\t!Number.isNaN(rect.top) &&\r\n\t\t\t!Number.isNaN(rect.left)\r\n\t\t);\r\n\t};\r\n\tlet xpath: any;\r\n\tif (savedGuideData) xpath = savedGuideData?.GuideStep?.[0]?.ElementPath;\r\n\tconst getElementPosition = (xpath: string | undefined) => {\r\n\t\tconst element = getElementByXPath(xpath || \"\");\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\treturn {\r\n\t\t\t\ttop: rect.top, //+ window.scrollY + yOffset, // Adjust for vertical scroll\r\n\t\t\t\tleft: rect.left, // + window.scrollX + xOffset, // Adjust for horizontal scroll\r\n\t\t\t};\r\n\t\t}\r\n\t\treturn null;\r\n\t};\r\n\t  // State to track if scrolling is needed\r\n\t  const [needsScrolling, setNeedsScrolling] = useState(false);\r\n\t  const scrollbarRef = useRef<any>(null);\r\n\t// Function to calculate popup position below the hotspot\r\n\tconst calculatePopupPosition = (elementRect: DOMRect, hotspotSize: number, xOffset: number, yOffset: number) => {\r\n\t\tconst hotspotLeft = elementRect.x + xOffset;\r\n\t\tconst hotspotTop = elementRect.y + yOffset;\r\n\r\n\t\t// Position popup below the hotspot for better user experience\r\n\t\tconst dynamicOffsetX = hotspotSize + 5; // Align horizontally with hotspot\r\n\t\tconst dynamicOffsetY = hotspotSize + 10; // Position below hotspot with spacing\r\n\r\n\t\treturn {\r\n\t\t\ttop: hotspotTop + window.scrollY + dynamicOffsetY,\r\n\t\t\tleft: hotspotLeft + window.scrollX + dynamicOffsetX\r\n\t\t};\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\tif (element) {\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY, // Account for scrolling\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tif (typeof window !== undefined) {\r\n\t\t\tconst position = getElementPosition(xpath || \"\");\r\n\t\t\tif (position) {\r\n\t\t\t\tsetPopupPosition(position);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [xpath]);\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(xpath);\r\n\t\t// setTargetElement(element);\r\n\t\tif (element) {\r\n\t\t}\r\n\t}, [savedGuideData]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst element = getElementByXPath(guideStep?.[currentStep - 1]?.ElementPath);\r\n\t\tsetTargetElement(element);\r\n\t\tif (element) {\r\n\t\t\telement.style.backgroundColor = \"red !important\";\r\n\r\n\t\t\t// Update popup position when target element changes\r\n\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\tsetPopupPosition({\r\n\t\t\t\ttop: rect.top + window.scrollY,\r\n\t\t\t\tleft: rect.left + window.scrollX,\r\n\t\t\t});\r\n\r\n\t\t\t// Check if element is visible and scroll to it if needed (for tour step navigation)\r\n\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t// Check if element is completely out of view\r\n\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t);\r\n\r\n\t\t\t// More generous visibility check - element should be reasonably visible\r\n\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\trect.top >= -50 && // Allow some element to be above viewport\r\n\t\t\t\trect.left >= -50 && // Allow some element to be left of viewport\r\n\t\t\t\trect.bottom <= viewportHeight + 50 && // Allow some element below viewport\r\n\t\t\t\trect.right <= viewportWidth + 50 && // Allow some element right of viewport\r\n\t\t\t\trect.width > 0 &&\r\n\t\t\t\trect.height > 0\r\n\t\t\t);\r\n\r\n\t\t\t// Add a small delay to ensure proper positioning\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tif (element && (isCompletelyOutOfView || !isReasonablyVisible)) {\r\n\t\t\t\t\tconsole.log(\"🎯 Tour hotspot element not visible, performing auto-scroll\");\r\n\t\t\t\t\t// Element is not visible, scroll to bring it into view\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\telement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'center', // Center it for better visibility\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tconsole.log(\"✅ Tour hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\tconsole.error(\"❌ Tour hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (element) {\r\n\t\t\t\t\tconsole.log(\"✅ Tour hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\telement.scrollIntoView({\r\n\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.log(\"Tour hotspot minimal scroll adjustment failed:\", error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}, 100);\r\n\t\t}\r\n\t}, [guideStep, currentStep]);\r\n\r\n\t// Hotspot styles are applied directly in the applyHotspotStyles function\r\n\t// State for overlay value\r\n\tconst [, setOverlayValue] = useState(false);\r\n\tconst handleContinue = () => {\r\n\t\tif (selectedTemplate !== \"Tour\") {\r\n\t\t\tif (currentStep < totalSteps) {\r\n\t\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\t\tonContinue();\r\n\t\t\t\trenderNextPopup(currentStep < totalSteps);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tsetCurrentStep(currentStep + 1);\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.style.display = \"none\";\r\n\t\t\t\texistingHotspot.remove();\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n\r\n\tconst renderNextPopup = (shouldRenderNextPopup: boolean) => {\r\n\t\treturn shouldRenderNextPopup ? (\r\n\t\t\t<HotspotPreview\r\n\t\t\t\tisHotspotPopupOpen={isHotspotPopupOpen}\r\n\t\t\t\tshowHotspotenduser={showHotspotenduser}\r\n\t\t\t\thandleHotspotHover={handleHotspotHover}\r\n\t\t\t\thandleHotspotClick={handleHotspotClick}\r\n\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\tsavedGuideData={savedGuideData}\r\n\t\t\t\tguideStep={guideStep}\r\n\t\t\t\tonClose={onClose}\r\n\t\t\t\tonPrevious={handlePrevious}\r\n\t\t\t\tonContinue={handleContinue}\r\n\t\t\t\ttitle={title}\r\n\t\t\t\ttext={text}\r\n\t\t\t\timageUrl={imageUrl}\r\n\t\t\t\tcurrentStep={currentStep + 1}\r\n\t\t\t\ttotalSteps={totalSteps}\r\n\t\t\t\tonDontShowAgain={onDontShowAgain}\r\n\t\t\t\tprogress={progress}\r\n\t\t\t\ttextFieldProperties={savedGuideData?.GuideStep?.[currentStep]?.TextFieldProperties}\r\n\t\t\t\timageProperties={savedGuideData?.GuideStep?.[currentStep]?.ImageProperties}\r\n\t\t\t\tcustomButton={\r\n\t\t\t\t\tsavedGuideData?.GuideStep?.[currentStep]?.ButtonSection?.map((section: any) =>\r\n\t\t\t\t\t\tsection.CustomButtons.map((button: any) => ({\r\n\t\t\t\t\t\t\t...button,\r\n\t\t\t\t\t\t\tContainerId: section.Id, // Attach the container ID for grouping\r\n\t\t\t\t\t\t}))\r\n\t\t\t\t\t)?.reduce((acc: string | any[], curr: any) => acc.concat(curr), []) || []\r\n\t\t\t\t}\r\n\t\t\t\tmodalProperties={modalProperties}\r\n\t\t\t\tcanvasProperties={canvasProperties}\r\n\t\t\t\thtmlSnippet={htmlSnippet}\r\n\t\t\t\tOverlayValue={OverlayValue}\r\n\t\t\t\thotspotProperties={savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {}}\r\n\t\t\t/>\r\n\t\t) : null;\r\n\t};\r\n\r\n\tconst handlePrevious = () => {\r\n\t\tif (currentStep > 1) {\r\n\t\t\tsetCurrentStep(currentStep - 1);\r\n\t\t\tonPrevious();\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (OverlayValue) {\r\n\t\t\tsetOverlayValue(true);\r\n\t\t} else {\r\n\t\t\tsetOverlayValue(false);\r\n\t\t}\r\n\t}, [OverlayValue]);\r\n\t// Image fit is used directly in the component\r\n\tconst getAnchorAndTransformOrigins = (\r\n\t\tposition: string\r\n\t): { anchorOrigin: PopoverOrigin; transformOrigin: PopoverOrigin } => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"top\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"top\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"bottom\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"right\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"left\" },\r\n\t\t\t\t};\r\n\t\t\tdefault:\r\n\t\t\t\treturn {\r\n\t\t\t\t\tanchorOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t\ttransformOrigin: { vertical: \"center\", horizontal: \"center\" },\r\n\t\t\t\t};\r\n\t\t}\r\n\t};\r\n\r\n\tconst { anchorOrigin, transformOrigin } = getAnchorAndTransformOrigins(canvasProperties?.Position || \"center center\");\r\n\r\n\tconst textStyle = {\r\n\t\tfontWeight: textFieldProperties?.TextProperties?.Bold ? \"bold\" : \"normal\",\r\n\t\tfontStyle: textFieldProperties?.TextProperties?.Italic ? \"italic\" : \"normal\",\r\n\t\tcolor: textFieldProperties?.TextProperties?.TextColor || \"#000000\",\r\n\t\ttextAlign: textFieldProperties?.Alignment || \"left\",\r\n\t};\r\n\r\n\t// Image styles are applied directly in the component\r\n\r\n\tconst renderHtmlSnippet = (snippet: string) => {\r\n\t\t// Return the raw HTML snippet for rendering\r\n\t\treturn {\r\n\t\t\t__html: snippet.replace(/(<a\\s+[^>]*href=\")([^\"]*)(\"[^>]*>)/g, (_match, p1, p2, p3) => {\r\n\t\t\t\treturn `${p1}${p2}\" target=\"_blank\"${p3}`;\r\n\t\t\t}),\r\n\t\t};\r\n\t};\r\n\r\n\t// Helper function to check if popup has only buttons (no text or images)\r\n\tconst hasOnlyButtons = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasButtons && !hasImage && !hasText;\r\n\t};\r\n\r\n\t// Helper function to check if popup has only text (no buttons or images)\r\n\tconst hasOnlyText = () => {\r\n\t\tconst hasImage = imageProperties && imageProperties.length > 0 &&\r\n\t\t\timageProperties.some((prop: any) =>\r\n\t\t\t\tprop.CustomImage && prop.CustomImage.some((img: any) => img.Url)\r\n\t\t\t);\r\n\r\n\t\tconst hasText = textFieldProperties && textFieldProperties.length > 0 &&\r\n\t\t\ttextFieldProperties.some((field: any) => field.Text && field.Text.trim() !== \"\");\r\n\r\n\t\tconst hasButtons = customButton && customButton.length > 0;\r\n\r\n\t\treturn hasText && !hasImage && !hasButtons;\r\n\t};\r\n\r\n\t// Function to calculate the optimal width based on content and buttons\r\n\tconst calculateOptimalWidth = () => {\r\n\t\t// If we have a fixed width from canvas settings and not a compact popup, use that\r\n\t\tif (canvasProperties?.Width && !hasOnlyButtons() && !hasOnlyText()) {\r\n\t\t\treturn `${canvasProperties.Width}px`;\r\n\t\t}\r\n\r\n\t\t// For popups with only buttons or only text, use auto width\r\n\t\tif (hasOnlyButtons() || hasOnlyText()) {\r\n\t\t\treturn \"auto\";\r\n\t\t}\r\n\r\n\t\t// Get the width of content and button container\r\n\t\tconst contentWidth = contentRef.current?.scrollWidth || 0;\r\n\t\tconst buttonWidth = buttonContainerRef.current?.scrollWidth || 0;\r\n\r\n\t\t// Use the larger of the two, with some minimum and maximum constraints\r\n\t\tconst optimalWidth = Math.max(contentWidth, buttonWidth);\r\n\r\n\t\t// Add some padding to ensure text has room to wrap naturally\r\n\t\tconst paddedWidth = optimalWidth + 20; // 10px padding on each side\r\n\r\n\t\t// Ensure width is between reasonable bounds\r\n\t\tconst minWidth = 250; // Minimum width\r\n\t\tconst maxWidth = 800; // Maximum width\r\n\r\n\t\tconst finalWidth = Math.max(minWidth, Math.min(paddedWidth, maxWidth));\r\n\r\n\t\treturn `${finalWidth}px`;\r\n\t};\r\n\r\n\t// Update dynamic width when content or buttons change\r\n\tuseEffect(() => {\r\n\t\t// Use requestAnimationFrame to ensure DOM has been updated\r\n\t\trequestAnimationFrame(() => {\r\n\t\t\tconst newWidth = calculateOptimalWidth();\r\n\t\t\tsetDynamicWidth(newWidth);\r\n\t\t});\r\n\t}, [textFieldProperties, imageProperties, customButton, currentStep]);\r\n\r\n\t// Recalculate popup position when hotspot size changes\r\n\tuseEffect(() => {\r\n\t\tif (xpath && hotspotSize) {\r\n\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\tif (element) {\r\n\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [hotspotSize, xpath, toolTipGuideMetaData]);\r\n\r\n\t// Recalculate popup position on window resize\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tif (xpath && hotspotSize) {\r\n\t\t\t\tconst element = getElementByXPath(xpath);\r\n\t\t\t\tif (element) {\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst hotspotPropData = toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\r\n\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, hotspotSize, xOffset, yOffset);\r\n\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\t\treturn () => window.removeEventListener('resize', handleResize);\r\n\t}, [xpath, hotspotSize, toolTipGuideMetaData]);\r\n\r\n\tconst groupedButtons = customButton.reduce((acc: any, button: any) => {\r\n\t\tconst containerId = button.ContainerId || \"default\"; // Use a ContainerId or fallback\r\n\t\tif (!acc[containerId]) {\r\n\t\t\tacc[containerId] = [];\r\n\t\t}\r\n\t\tacc[containerId].push(button);\r\n\t\treturn acc;\r\n\t}, {});\r\n\r\n\tconst canvasStyle = {\r\n\t\tposition: canvasProperties?.Position || \"center-center\",\r\n\t\tborderRadius: canvasProperties?.Radius || \"4px\",\r\n\t\tborderWidth: canvasProperties?.BorderSize || \"0px\",\r\n\t\tborderColor: canvasProperties?.BorderColor || \"black\",\r\n\t\tborderStyle: \"solid\",\r\n\t\tbackgroundColor: canvasProperties?.BackgroundColor || \"white\",\r\n\t\tmaxWidth: (hasOnlyButtons() || hasOnlyText()) ? \"none !important\" :\r\n\t\t\t\t  dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t\t  canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"800px\",\r\n\t\twidth: (hasOnlyButtons() || hasOnlyText()) ? \"auto !important\" :\r\n\t\t\t   dynamicWidth ? `${dynamicWidth} !important` :\r\n\t\t\t   canvasProperties?.Width ? `${canvasProperties.Width}px !important` : \"300px\",\r\n\t};\r\n\tconst sectionHeight = imageProperties[currentStep - 1]?.CustomImage?.[currentStep - 1]?.SectionHeight || \"auto\";\r\n\tconst handleButtonAction = (action: any) => {\r\n\t\tif (action.Action === \"open-url\" || action.Action === \"open\" || action.Action === \"openurl\") {\r\n\t\t\tconst targetUrl = action.TargetUrl;\r\n\t\t\tif (action.ActionValue === \"same-tab\") {\r\n\t\t\t\t// Open the URL in the same tab\r\n\t\t\t\twindow.location.href = targetUrl;\r\n\t\t\t} else {\r\n\t\t\t\t// Open the URL in a new tab\r\n\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tif (\r\n\t\t\t\taction.Action == \"Previous\" ||\r\n\t\t\t\taction.Action == \"previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\" ||\r\n\t\t\t\taction.ActionValue == \"Previous\"\r\n\t\t\t) {\r\n\t\t\t\thandlePrevious();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Next\" ||\r\n\t\t\t\taction.Action == \"next\" ||\r\n\t\t\t\taction.ActionValue == \"Next\" ||\r\n\t\t\t\taction.ActionValue == \"next\"\r\n\t\t\t) {\r\n\t\t\t\thandleContinue();\r\n\t\t\t} else if (\r\n\t\t\t\taction.Action == \"Restart\" ||\r\n\t\t\t\taction.ActionValue == \"Restart\"\r\n\t\t\t) {\r\n\t\t\t\t// Reset to the first step\r\n\t\t\t\tsetCurrentStep(1);\r\n\t\t\t\t// If there's a specific URL for the first step, navigate to it\r\n\t\t\t\tif (savedGuideData?.GuideStep?.[0]?.ElementPath) {\r\n\t\t\t\t\tconst firstStepElement = getElementByXPath(savedGuideData.GuideStep[0].ElementPath);\r\n\t\t\t\t\tif (firstStepElement) {\r\n\t\t\t\t\t\tfirstStepElement.scrollIntoView({ behavior: 'smooth' });\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t\tsetOverlayValue(false);\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tif (guideStep?.[currentStep - 1]?.Hotspot?.ShowByDefault) {\r\n\t\t\t// Show tooltip by default\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t}\r\n\t}, [guideStep?.[currentStep - 1], currentStep, setOpenTooltip]);\r\n\r\n\t// Add effect to handle isHotspotPopupOpen prop changes\r\n\tuseEffect(() => {\r\n\t\tif (isHotspotPopupOpen) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\t// For \"Hovering Hotspot\", we'll wait for the hover event\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [isHotspotPopupOpen, toolTipGuideMetaData]);\r\n\r\n\t// Add effect to handle showHotspotenduser prop changes\r\n\tuseEffect(() => {\r\n\t\tif (showHotspotenduser) {\r\n\t\t\t// Get the ShowUpon property\r\n\t\t\tconst hotspotPropData = selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots\r\n\t\t\t\t? toolTipGuideMetaData[currentStep - 1].hotspots\r\n\t\t\t\t: toolTipGuideMetaData[0]?.hotspots;\r\n\t\t\tconst hotspotData = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t\t? savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot\r\n\t\t\t\t: savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\r\n\t\t\t// Only show tooltip by default if ShowByDefault is true\r\n\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t// Set openTooltip to true\r\n\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t} else {\r\n\t\t\t\t// Otherwise, initially hide the tooltip\r\n\t\t\t\tsetOpenTooltip(false);\r\n\t\t\t}\r\n\t\t}\r\n\t}, [showHotspotenduser, toolTipGuideMetaData]);\r\n\r\n\t// Add a global click handler to detect clicks outside the hotspot to close the tooltip\r\n\tuseEffect(() => {\r\n\t\tconst handleGlobalClick = (e: MouseEvent) => {\r\n\t\t\tconst hotspotElement = document.getElementById(\"hotspotBlink\");\r\n\r\n\t\t\t// Skip if clicking on the hotspot (those events are handled by the hotspot's own event listeners)\r\n\t\t\tif (hotspotElement && hotspotElement.contains(e.target as Node)) {\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t// So we're not closing it on clicks outside anymore\r\n\t\t};\r\n\r\n\t\tdocument.addEventListener(\"click\", handleGlobalClick);\r\n\r\n\t\treturn () => {\r\n\t\t\tdocument.removeEventListener(\"click\", handleGlobalClick);\r\n\t\t};\r\n\t}, [toolTipGuideMetaData]);\r\n\t// Check if content needs scrolling with improved detection\r\n\tuseEffect(() => {\r\n\t\tconst checkScrollNeeded = () => {\r\n\t\t\tif (contentRef.current) {\r\n\t\t\t\t// Force a reflow to get accurate measurements\r\n\t\t\t\tcontentRef.current.style.height = 'auto';\r\n\t\t\t\tconst contentHeight = contentRef.current.scrollHeight;\r\n\t\t\t\tconst containerHeight = 320; // max-height value\r\n\t\t\t\tconst shouldScroll = contentHeight > containerHeight;\r\n\r\n\r\n\t\t\t\tsetNeedsScrolling(shouldScroll);\r\n\r\n\t\t\t\t// Force update scrollbar\r\n\t\t\t\tif (scrollbarRef.current) {\r\n\t\t\t\t\t// Try multiple methods to update the scrollbar\r\n\t\t\t\t\tif (scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// Force re-initialization if needed\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (scrollbarRef.current && scrollbarRef.current.updateScroll) {\r\n\t\t\t\t\t\t\tscrollbarRef.current.updateScroll();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 10);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t\r\n\t\tcheckScrollNeeded();\r\n\r\n\t\t\r\n\t\tconst timeouts = [\r\n\t\t\tsetTimeout(checkScrollNeeded, 50),\r\n\t\t\tsetTimeout(checkScrollNeeded, 100),\r\n\t\t\tsetTimeout(checkScrollNeeded, 200),\r\n\t\t\tsetTimeout(checkScrollNeeded, 500)\r\n\t\t];\r\n\r\n\t\t\r\n\t\tlet resizeObserver: ResizeObserver | null = null;\r\n\t\tlet mutationObserver: MutationObserver | null = null;\r\n\r\n\t\tif (contentRef.current && window.ResizeObserver) {\r\n\t\t\tresizeObserver = new ResizeObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tresizeObserver.observe(contentRef.current);\r\n\t\t}\r\n\r\n\t\t\r\n\t\tif (contentRef.current && window.MutationObserver) {\r\n\t\t\tmutationObserver = new MutationObserver(() => {\r\n\t\t\t\tsetTimeout(checkScrollNeeded, 10);\r\n\t\t\t});\r\n\t\t\tmutationObserver.observe(contentRef.current, {\r\n\t\t\t\tchildList: true,\r\n\t\t\t\tsubtree: true,\r\n\t\t\t\tattributes: true,\r\n\t\t\t\tattributeFilter: ['style', 'class']\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn () => {\r\n\t\t\ttimeouts.forEach(clearTimeout);\r\n\t\t\tif (resizeObserver) {\r\n\t\t\t\tresizeObserver.disconnect();\r\n\t\t\t}\r\n\t\t\tif (mutationObserver) {\r\n\t\t\t\tmutationObserver.disconnect();\r\n\t\t\t}\r\n\t\t};\r\n\t}, [currentStep]);\r\n\t// We no longer need the persistent monitoring effect since we want the tooltip\r\n\t// to close when the mouse leaves the hotspot\r\n\r\n\tfunction getAlignment(alignment: string) {\r\n\t\tswitch (alignment) {\r\n\t\t\tcase \"start\":\r\n\t\t\t\treturn \"flex-start\";\r\n\t\t\tcase \"end\":\r\n\t\t\t\treturn \"flex-end\";\r\n\t\t\tcase \"center\":\r\n\t\t\tdefault:\r\n\t\t\t\treturn \"center\";\r\n\t\t}\r\n\t}\r\n\tconst getCanvasPosition = (position: string = \"center-center\") => {\r\n\t\tswitch (position) {\r\n\t\t\tcase \"bottom-left\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-right\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"bottom-center\":\r\n\t\t\t\treturn { top: \"auto !important\" };\r\n\t\t\tcase \"center-center\":\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t\tcase \"left-center\":\r\n\t\t\t\treturn { top: imageUrl === \"\" ? \"40% !important\" : \"20% !important\" };\r\n\t\t\tcase \"right-center\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-left\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-right\":\r\n\t\t\t\treturn { top: \"10% !important\" };\r\n\t\t\tcase \"top-center\":\r\n\t\t\t\treturn { top: \"9% !important\" };\r\n\t\t\tdefault:\r\n\t\t\t\treturn { top: \"25% !important\" };\r\n\t\t}\r\n\t};\r\n\r\n\t\t// function to get the correct property value based on tour vs normal hotspot\r\n\tconst getHotspotProperty = (propName: string, hotspotPropData: any, hotspotData: any) => {\r\n\t\tif (selectedTemplateTour === \"Hotspot\") {\r\n\t\t\t// For tour hotspots, use saved data first, fallback to metadata\r\n\t\t\tswitch (propName) {\r\n\t\t\t\tcase 'PulseAnimation':\r\n\t\t\t\t\treturn hotspotData?.PulseAnimation !== undefined ? hotspotData.PulseAnimation : hotspotPropData?.PulseAnimation;\r\n\t\t\t\tcase 'StopAnimation':\r\n\t\t\t\t\t// Always use stopAnimationUponInteraction for consistency\r\n\t\t\t\t\treturn hotspotData?.stopAnimationUponInteraction !== undefined ? hotspotData.stopAnimationUponInteraction : hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t\tcase 'ShowUpon':\r\n\t\t\t\t\treturn hotspotData?.ShowUpon !== undefined ? hotspotData.ShowUpon : hotspotPropData?.ShowUpon;\r\n\t\t\t\tcase 'ShowByDefault':\r\n\t\t\t\t\treturn hotspotData?.ShowByDefault !== undefined ? hotspotData.ShowByDefault : hotspotPropData?.ShowByDefault;\r\n\t\t\t\tdefault:\r\n\t\t\t\t\treturn hotspotPropData?.[propName];\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\t// For normal hotspots, use metadata\r\n\t\t\tif (propName === 'StopAnimation') {\r\n\t\t\t\treturn hotspotPropData?.stopAnimationUponInteraction;\r\n\t\t\t}\r\n\t\t\treturn hotspotPropData?.[propName];\r\n\t\t}\r\n\t};\r\n\r\n\tconst applyHotspotStyles = (hotspot: any, hotspotPropData: any, hotspotData: any, left: any, top: any) => {\r\n\t\thotspot.style.position = \"absolute\";\r\n\t\thotspot.style.left = `${left}px`;\r\n\t\thotspot.style.top = `${top}px`;\r\n\t\thotspot.style.width = `${hotspotPropData?.Size}px`; // Default size if not provided\r\n\t\thotspot.style.height = `${hotspotPropData?.Size}px`;\r\n\t\thotspot.style.backgroundColor = hotspotPropData?.Color;\r\n\t\thotspot.style.borderRadius = \"50%\";\r\n\t\thotspot.style.zIndex = \"auto !important\"; // Increased z-index\r\n\t\thotspot.style.transition = \"none\";\r\n\t\thotspot.style.pointerEvents = \"auto\"; // Ensure clicks are registered\r\n\t\thotspot.innerHTML = \"\";\r\n\r\n\t\tif (hotspotPropData?.Type === \"Info\" || hotspotPropData?.Type === \"Question\") {\r\n\t\t\tconst textSpan = document.createElement(\"span\");\r\n\t\t\ttextSpan.innerText = hotspotPropData.Type === \"Info\" ? \"i\" : \"?\";\r\n\t\t\ttextSpan.style.color = \"white\";\r\n\t\t\ttextSpan.style.fontSize = \"14px\";\r\n\t\t\ttextSpan.style.fontWeight = \"bold\";\r\n\t\t\ttextSpan.style.fontStyle = hotspotPropData.Type === \"Info\" ? \"italic\" : \"normal\";\r\n\t\t\ttextSpan.style.display = \"flex\";\r\n\t\t\ttextSpan.style.alignItems = \"center\";\r\n\t\t\ttextSpan.style.justifyContent = \"center\";\r\n\t\t\ttextSpan.style.width = \"100%\";\r\n\t\t\ttextSpan.style.height = \"100%\";\r\n\t\t\thotspot.appendChild(textSpan);\r\n\t\t}\r\n\r\n\t\t// Apply animation class if needed\r\n\t\t// Track if pulse has been stopped by hover\r\n\t\tconst pulseAnimationEnabled = getHotspotProperty('PulseAnimation', hotspotPropData, hotspotData);\r\n\t\tconst shouldPulse = selectedTemplateTour === \"Hotspot\"\r\n\t\t\t? (pulseAnimationEnabled !== false && !hotspot._pulseStopped)\r\n\t\t\t: (hotspotPropData && pulseAnimationsH && !hotspot._pulseStopped);\r\n\r\n\t\tif (shouldPulse) {\r\n            hotspot.classList.add(\"pulse-animation\");\r\n            hotspot.classList.remove(\"pulse-animation-removed\");\r\n        } else {\r\n            hotspot.classList.remove(\"pulse-animation\");\r\n            hotspot.classList.add(\"pulse-animation-removed\");\r\n        }\r\n\r\n\t\t// Ensure the hotspot is visible and clickable\r\n\t\thotspot.style.display = \"flex\";\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// No need for separate animation control functions here\r\n\t\t// Animation will be controlled directly in the event handlers\r\n\t\t// Set initial state of openTooltip based on ShowByDefault and ShowUpon\r\n\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\tif (showByDefault) {\r\n\t\t\tsetOpenTooltip(true);\r\n\t\t} else {\r\n\t\t\t// If not showing by default, only show based on interaction type\r\n\t\t\t//setOpenTooltip(false);\r\n\t\t}\r\n\r\n\t\t// Only clone and replace if the hotspot doesn't have event listeners already\r\n\t\t// This prevents losing the _pulseStopped state unnecessarily\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tconst newHotspot = hotspot.cloneNode(true) as HTMLElement;\r\n\t\t\t// Copy the _pulseStopped property if it exists\r\n\t\t\tif (hotspot._pulseStopped !== undefined) {\r\n\t            (newHotspot as any)._pulseStopped = hotspot._pulseStopped;\r\n\t        }\r\n\t\t\tif (hotspot.parentNode) {\r\n\t\t\t\thotspot.parentNode.replaceChild(newHotspot, hotspot);\r\n\t\t\t\thotspot = newHotspot;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// Ensure pointer events are enabled\r\n\t\thotspot.style.pointerEvents = \"auto\";\r\n\r\n\t\t// Define combined event handlers that handle both animation and tooltip\r\n\t\tconst showUpon = getHotspotProperty('ShowUpon', hotspotPropData, hotspotData);\r\n\t\tconst handleHover = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Hover detected on hotspot\");\r\n\r\n\t\t\t// Show tooltip if ShowUpon is \"Hovering Hotspot\"\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// Set openTooltip to true when hovering\r\n\t\t\t\tsetOpenTooltip(true);\r\n\r\n\t\t\t\t// Call the passed hover handler if it exists\r\n\t\t\t\tif (typeof handleHotspotHover === \"function\") {\r\n\t\t\t\t\thandleHotspotHover();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\n\t\t\t\tconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleMouseOut = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\r\n\t\t\t// Hide tooltip when mouse leaves the hotspot\r\n\t\t\t// Only if ShowUpon is \"Hovering Hotspot\" and not ShowByDefault\r\n\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\tif (showUpon === \"Hovering Hotspot\" && !showByDefault) {\r\n\t\t\t\t// setOpenTooltip(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tconst handleClick = (e: Event) => {\r\n\t\t\te.stopPropagation();\r\n\t\t\tconsole.log(\"Click detected on hotspot\");\r\n\r\n\t\t\t// Toggle tooltip if ShowUpon is \"Clicking Hotspot\" or not specified\r\n\t\t\tif (showUpon === \"Clicking Hotspot\" || !showUpon) {\r\n\t\t\t\t// Toggle the tooltip state\r\n\t\t\t\tsetOpenTooltip(!openTooltip);\r\n\r\n\t\t\t\t// Call the passed click handler if it exists\r\n\t\t\t\tif (typeof handleHotspotClick === \"function\") {\r\n\t\t\t\t\thandleHotspotClick();\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Stop animation if configured to do so\r\nconst stopAnimationSetting = getHotspotProperty('StopAnimation', hotspotPropData, hotspotData);\r\n\t\t\t\tif (stopAnimationSetting) {\r\n\t\t\t\t\thotspot.classList.remove(\"pulse-animation\");\r\n\t\t\t\t\thotspot.classList.add(\"pulse-animation-removed\");\r\n\t\t\t\t\thotspot._pulseStopped = true; // Mark as stopped so it won't be re-applied\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\t// Add appropriate event listeners based on ShowUpon property\r\n\t\tif (!hotspot.hasAttribute('data-listeners-attached')) {\r\n\t\t\tif (showUpon === \"Hovering Hotspot\") {\r\n\t\t\t\t// For hover interaction\r\n\t\t\t\thotspot.addEventListener(\"mouseover\", handleHover);\r\n\t\t\t\thotspot.addEventListener(\"mouseout\", handleMouseOut);\r\n\r\n\t\t\t\t// Also add click handler for better user experience\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t} else {\r\n\t\t\t\t// For click interaction (default)\r\n\t\t\t\thotspot.addEventListener(\"click\", handleClick);\r\n\t\t\t}\r\n\r\n\t\t\t// Mark that listeners have been attached\r\n\t\t\thotspot.setAttribute('data-listeners-attached', 'true');\r\n\t\t}\r\n\t};\r\n\tuseEffect(() => {\r\n\t\tlet element: HTMLElement | null;\r\n\t\tlet steps: any[];\r\n\r\n\t\tconst fetchGuideDetails = async () => {\r\n\t\t\ttry {\r\n\t\t\t\t//   const data = await GetGudeDetailsByGuideId(savedGuideData?.GuideId);\r\n\t\t\t\tsteps = savedGuideData?.GuideStep || [];\r\n\r\n\t\t\t\t// For tour hotspots, use the current step's element path\r\n\t\t\t\tconst elementPath = selectedTemplateTour === \"Hotspot\" && savedGuideData?.GuideStep?.[currentStep - 1]?.ElementPath\r\n\t\t\t\t\t? (savedGuideData.GuideStep[currentStep - 1] as any).ElementPath\r\n\t\t\t\t\t: steps?.[0]?.ElementPath || \"\";\r\n\r\n\t\t\t\telement = getElementByXPath(elementPath || \"\");\r\n\t\t\t\tsetTargetElement(element);\r\n\r\n\t\t\t\tif (element && validateElementPosition(element)) {\r\n\t\t\t\t\t// element.style.outline = \"2px solid red\";\r\n\r\n\t\t\t\t\t// Check if element is visible and scroll to it if needed\r\n\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\tconst viewportHeight = window.innerHeight;\r\n\t\t\t\t\tconst viewportWidth = window.innerWidth;\r\n\r\n\t\t\t\t\t// Check if element is completely out of view or barely visible\r\n\t\t\t\t\tconst isCompletelyOutOfView = (\r\n\t\t\t\t\t\trect.bottom < 0 || // Completely above viewport\r\n\t\t\t\t\t\trect.top > viewportHeight || // Completely below viewport\r\n\t\t\t\t\t\trect.right < 0 || // Completely left of viewport\r\n\t\t\t\t\t\trect.left > viewportWidth // Completely right of viewport\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// More generous visibility check - element should be reasonably visible\r\n\t\t\t\t\tconst isReasonablyVisible = (\r\n\t\t\t\t\t\trect.top >= -50 && // Allow some element to be above viewport\r\n\t\t\t\t\t\trect.left >= -50 && // Allow some element to be left of viewport\r\n\t\t\t\t\t\trect.bottom <= viewportHeight + 50 && // Allow some element below viewport\r\n\t\t\t\t\t\trect.right <= viewportWidth + 50 && // Allow some element right of viewport\r\n\t\t\t\t\t\trect.width > 0 &&\r\n\t\t\t\t\t\trect.height > 0\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\t// Add a small delay to ensure proper positioning\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tif (element && (isCompletelyOutOfView || !isReasonablyVisible)) {\r\n\t\t\t\t\t\t\tconsole.log(\"🎯 Hotspot element not visible, performing auto-scroll\");\r\n\t\t\t\t\t\t\t// Element is not visible, scroll to bring it into view\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\telement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\tblock: 'center', // Center it for better visibility\r\n\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot auto-scroll completed successfully\");\r\n\t\t\t\t\t\t\t} catch (scrollError) {\r\n\t\t\t\t\t\t\t\tconsole.error(\"❌ Hotspot auto-scroll failed:\", scrollError);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} else if (element) {\r\n\t\t\t\t\t\t\tconsole.log(\"✅ Hotspot element is reasonably visible, minimal adjustment\");\r\n\t\t\t\t\t\t\t// Element is mostly visible, just ensure it's well positioned\r\n\t\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t\telement.scrollIntoView({\r\n\t\t\t\t\t\t\t\t\tbehavior: 'smooth',\r\n\t\t\t\t\t\t\t\t\tblock: 'nearest', // Don't force center if already visible\r\n\t\t\t\t\t\t\t\t\tinline: 'nearest'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\t\tconsole.log(\"Hotspot minimal scroll adjustment failed:\", error);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 100);\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// Check if this is a hotspot scenario (normal or tour)\r\n\t\t\t\tconst isHotspotScenario = selectedTemplate === \"Hotspot\" ||\r\n\t\t\t\t\tselectedTemplateTour === \"Hotspot\" ||\r\n\t\t\t\t\ttitle === \"Hotspot\" ||\r\n\t\t\t\t\t(selectedTemplate === \"Tour\" && selectedTemplateTour === \"Hotspot\");\r\n\r\n\t\t\t\tif (isHotspotScenario) {\r\n\t\t\t\t\t\r\n\t\t\t\t\t\r\n\r\n\t\t\t\t\t// Get hotspot properties - prioritize tour data for tour hotspots\r\n\t\t\t\t\tlet hotspotPropData;\r\n\t\t\t\t\tlet hotspotData;\r\n\r\n\t\t\t\t\tif (selectedTemplateTour === \"Hotspot\" && toolTipGuideMetaData?.[currentStep - 1]?.hotspots) {\r\n\t\t\t\t\t\t// Tour hotspot - use current step metadata\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[currentStep - 1].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot;\r\n\t\t\t\t\t} else if (toolTipGuideMetaData?.[0]?.hotspots) {\r\n\t\t\t\t\t\t// Normal hotspot - use first metadata entry\r\n\t\t\t\t\t\thotspotPropData = toolTipGuideMetaData[0].hotspots;\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[0]?.Hotspot;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Fallback to default values for tour hotspots without metadata\r\n\t\t\t\t\t\thotspotPropData = {\r\n\t\t\t\t\t\t\tXPosition: \"4\",\r\n\t\t\t\t\t\t\tYPosition: \"4\",\r\n\t\t\t\t\t\t\tType: \"Question\",\r\n\t\t\t\t\t\t\tColor: \"yellow\",\r\n\t\t\t\t\t\t\tSize: \"16\",\r\n\t\t\t\t\t\t\tPulseAnimation: true,\r\n\t\t\t\t\t\t\tstopAnimationUponInteraction: true,\r\n\t\t\t\t\t\t\tShowUpon: \"Hovering Hotspot\",\r\n\t\t\t\t\t\t\tShowByDefault: false,\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\thotspotData = savedGuideData?.GuideStep?.[currentStep - 1]?.Hotspot || {};\r\n\t\t\t\t\t}\r\n\t\t\t\t\tconst xOffset = parseFloat(hotspotPropData?.XPosition || \"4\");\r\n\t\t\t\t\tconst yOffset = parseFloat(hotspotPropData?.YPosition || \"4\");\r\n\t\t\t\t\tconst currentHotspotSize = parseFloat(hotspotPropData?.Size || \"30\");\r\n\r\n\t\t\t\t\t// Update hotspot size state\r\n\t\t\t\t\tsetHotspotSize(currentHotspotSize);\r\n\r\n\t\t\t\t\tlet left, top;\r\n\t\t\t\t\tif (element) {\r\n\t\t\t\t\t\tconst rect = element.getBoundingClientRect();\r\n\t\t\t\t\t\tleft = rect.x + xOffset;\r\n\t\t\t\t\t\ttop = rect.y + (yOffset > 0 ? -yOffset : Math.abs(yOffset));\r\n\r\n\t\t\t\t\t\t// Calculate popup position below the hotspot\r\n\t\t\t\t\t\tconst popupPos = calculatePopupPosition(rect, currentHotspotSize, xOffset, yOffset);\r\n\t\t\t\t\t\tsetPopupPosition(popupPos);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Check if hotspot already exists, preserve it to maintain _pulseStopped state\r\n\t\t\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\t\t\tif (existingHotspot) {\r\n\t\t\t\t\t\thotspot = existingHotspot;\r\n\t\t\t\t\t\t// Don't reset _pulseStopped if it already exists\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// Create new hotspot only if it doesn't exist\r\n\t\t\t\t\t\thotspot = document.createElement(\"div\");\r\n\t\t\t\t\t\thotspot.id = \"hotspotBlink\"; // Fixed ID for easier reference\r\n\t\t\t\t\t\thotspot._pulseStopped = false; // Set only on creation\r\n\t\t\t\t\t\tdocument.body.appendChild(hotspot);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\thotspot.style.cursor = \"pointer\";\r\n\t\t\t\t\thotspot.style.pointerEvents = \"auto\"; // Ensure it can receive mouse events\r\n\r\n\t\t\t\t\t// Make sure the hotspot is visible and clickable\r\n\t\t\t\t\thotspot.style.zIndex = \"9999\";\r\n\r\n\t\t\t\t\t// If ShowByDefault is true, set openTooltip to true immediately\r\n\t\t\t\t\tif (hotspotPropData?.ShowByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// Set styles first\r\n\t\t\t\t\tapplyHotspotStyles(hotspot, hotspotPropData, hotspotData, left, top);\r\n\r\n\t\t\t\t\t// Set initial tooltip visibility based on ShowByDefault\r\n\t\t\t\t\tconst showByDefault = getHotspotProperty('ShowByDefault', hotspotPropData, hotspotData);\r\n\t\t\t\t\tif (showByDefault) {\r\n\t\t\t\t\t\tsetOpenTooltip(true);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// We don't need to add event listeners here as they're already added in applyHotspotStyles\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error(\"Error in fetchGuideDetails:\", error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchGuideDetails();\r\n\r\n\t\treturn () => {\r\n\t\t\tconst existingHotspot = document.getElementById(\"hotspotBlink\");\r\n\t\t\tif (existingHotspot) {\r\n\t\t\t\texistingHotspot.onclick = null;\r\n\t\t\t\texistingHotspot.onmouseover = null;\r\n\t\t\t\texistingHotspot.onmouseout = null;\r\n\t\t\t}\r\n\t\t};\r\n\t}, [\r\n\t\tsavedGuideData,\r\n\t\ttoolTipGuideMetaData,\r\n\t\tisHotspotPopupOpen,\r\n\t\tshowHotspotenduser,\r\n\t\tselectedTemplateTour,\r\n\t\tcurrentStep,\r\n\t\t// Removed handleHotspotClick and handleHotspotHover to prevent unnecessary re-renders\r\n\t]);\r\n\tconst enableProgress = savedGuideData?.GuideStep?.[0]?.Tooltip?.EnableProgress || false;\r\n\r\n\tfunction getProgressTemplate(selectedOption: any) {\r\n\t\tif (selectedOption === 1) {\r\n\t\t\treturn \"dots\";\r\n\t\t} else if (selectedOption === 2) {\r\n\t\t\treturn \"linear\";\r\n\t\t} else if (selectedOption === 3) {\r\n\t\t\treturn \"BreadCrumbs\";\r\n\t\t} else if (selectedOption === 4) {\r\n\t\t\treturn \"breadcrumbs\";\r\n\t\t}\r\n\r\n\t\treturn savedGuideData?.GuideStep?.[0]?.Tooltip?.ProgressTemplate || \"dots\";\r\n\t}\r\n\tconst progressTemplate = getProgressTemplate(selectedOption);\r\n\tconst renderProgress = () => {\r\n\t\tif (!enableProgress) return null;\r\n\r\n\t\tif (progressTemplate === \"dots\") {\r\n\t\t\treturn (\r\n\t\t\t\t<MobileStepper\r\n\t\t\t\t\tvariant=\"dots\"\r\n\t\t\t\t\tsteps={totalSteps}\r\n\t\t\t\t\tposition=\"static\"\r\n\t\t\t\t\tactiveStep={currentStep - 1}\r\n\t\t\t\t\tsx={{\r\n\t\t\t\t\t\tbackgroundColor: \"transparent\",\r\n\t\t\t\t\t\tposition: \"inherit !important\",\r\n\t\t\t\t\t\t\"& .MuiMobileStepper-dotActive\": {\r\n\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // Active dot\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}}\r\n\t\t\t\t\tbackButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t\tnextButton={<Button style={{ visibility: \"hidden\" }} />}\r\n\t\t\t\t/>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"BreadCrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"center\", gap: \"5px\", padding: \"8px\" }}>\r\n\t\t\t\t\t{/* Custom Step Indicators */}\r\n\r\n\t\t\t\t\t{Array.from({ length: totalSteps }).map((_, index) => (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\twidth: \"14px\",\r\n\t\t\t\t\t\t\t\theight: \"4px\",\r\n\t\t\t\t\t\t\t\tbackgroundColor: index === currentStep - 1 ? ProgressColor : \"#e0e0e0\", // Active color and inactive color\r\n\t\t\t\t\t\t\t\tborderRadius: \"100px\",\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t))}\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\t\tif (progressTemplate === \"breadcrumbs\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box sx={{ display: \"flex\", alignItems: \"center\", placeContent: \"flex-start\" }}>\r\n\t\t\t\t\t<Typography sx={{ padding: \"8px\", color: ProgressColor }}>\r\n\t\t\t\t\t\tStep {currentStep} of {totalSteps}\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\tif (progressTemplate === \"linear\") {\r\n\t\t\treturn (\r\n\t\t\t\t<Box>\r\n\t\t\t\t\t<Typography variant=\"body2\">\r\n\t\t\t\t\t\t<LinearProgress\r\n\t\t\t\t\t\t\tvariant=\"determinate\"\r\n\t\t\t\t\t\t\tvalue={progress}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\theight: \"6px\",\r\n\t\t\t\t\t\t\t\t\t\tborderRadius: \"20px\",\r\n\t\t\t\t\t\t\t\t\t\tmargin: \"6px 10px\",\r\n\t\t\t\t\t\t\t\t\"& .MuiLinearProgress-bar\": {\r\n\t\t\t\t\t\t\t\t\tbackgroundColor: ProgressColor, // progress bar color\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t</Typography>\r\n\t\t\t\t</Box>\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\treturn null;\r\n\t};\r\n\treturn (\r\n\t\t<>\r\n\t\t\t{targetElement && (\r\n\t\t\t\t<div>\r\n\t\t\t\t\t{/* {overlay && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\ttop: 0,\r\n\t\t\t\t\t\t\t\tleft: 0,\r\n\t\t\t\t\t\t\t\tright: 0,\r\n\t\t\t\t\t\t\t\tbottom: 0,\r\n\t\t\t\t\t\t\t\tbackgroundColor: \"rgba(0, 0, 0, 0.5)\",\r\n\t\t\t\t\t\t\t\tzIndex: 999,\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t)} */}\r\n\t\t\t\t\t{openTooltip && (\r\n\t\t\t\t\t\t<Popover\r\n\t\t\t\t\t\t\topen={Boolean(popupPosition) || Boolean(anchorEl)}\r\n\t\t\t\t\t\t\tanchorEl={anchorEl}\r\n\t\t\t\t\t\t\tonClose={() => {\r\n\t\t\t\t\t\t\t\t// We want to keep the tooltip open once it's been displayed\r\n\t\t\t\t\t\t\t\t// So we're not closing it on Popover close events\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tanchorOrigin={anchorOrigin}\r\n\t\t\t\t\t\t\ttransformOrigin={transformOrigin}\r\n\t\t\t\t\t\t\tanchorReference=\"anchorPosition\"\r\n\t\t\t\t\t\t\tanchorPosition={\r\n\t\t\t\t\t\t\t\tpopupPosition\r\n\t\t\t\t\t\t\t\t\t? {\r\n\t\t\t\t\t\t\t\t\t\t\ttop: popupPosition.top +10+ (parseFloat(tooltipYaxis || \"0\") > 0 ? -parseFloat(tooltipYaxis || \"0\") : Math.abs(parseFloat(tooltipYaxis || \"0\"))),\r\n\t\t\t\t\t\t\t\t\t\t\tleft: popupPosition.left +10+ parseFloat(tooltipXaxis || \"0\"),\r\n\t\t\t\t\t\t\t\t\t  }\r\n\t\t\t\t\t\t\t\t\t: undefined\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t// \"& .MuiBackdrop-root\": {\r\n\t\t\t\t\t\t\t\t//     position: 'relative !important', // Ensures higher specificity\r\n\t\t\t\t\t\t\t\t// },\r\n\t\t\t\t\t\t\t\t\"pointer-events\": anchorEl ? \"auto\" : \"auto\",\r\n\t\t\t\t\t\t\t\t'& .MuiPaper-root:not(.MuiMobileStepper-root)': {\r\n\t\t\t\t\t\t\t\t\tzIndex: 1000,\r\n\t\t\t\t\t\t\t\t\t// borderRadius: \"1px\",\r\n\t\t\t\t\t\t\t\t\t...canvasStyle,\r\n\t\t\t\t\t\t\t\t\t//...getAnchorAndTransformOrigins,\r\n\t\t\t\t\t\t\t\t\t//top: \"16% !important\",\r\n\t\t\t\t\t\t\t\t\t// top: canvasProperties?.Position === \"bottom-left\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//     canvasProperties?.Position === \"bottom-right\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//         canvasProperties?.Position === \"bottom-center\" ? \"auto !important\" :\r\n\t\t\t\t\t\t\t\t\t//             canvasProperties?.Position === \"center-center\" ? \"30% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                 canvasProperties?.Position === \"left-center\" ? (imageUrl === \"\" ? \"40% !important\" : \"20% !important\") :\r\n\t\t\t\t\t\t\t\t\t//                     canvasProperties?.Position === \"right-center\" ? \"20% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-left\" ? \"10% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position === \"top-center\" ? \"9% !important\" :\r\n\t\t\t\t\t\t\t\t\t//                         canvasProperties?.Position===\"top-right\"?\"10% !important\":    \"\",\r\n\t\t\t\t\t\t\t\t\t...getCanvasPosition(canvasProperties?.Position || \"center-center\"),\r\n\t\t\t\t\t\t\t\t\ttop: `${(popupPosition?.top || 0)\r\n\t\t\t\t\t\t\t\t\t\t+ (tooltipYaxis && tooltipYaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t\t? parseFloat(tooltipYaxis || \"0\") > 0\r\n\t\t\t\t\t\t\t\t\t\t\t\t? -parseFloat(tooltipYaxis || \"0\")\r\n\t\t\t\t\t\t\t\t\t\t\t\t: Math.abs(parseFloat(tooltipYaxis || \"0\"))\r\n\t\t\t\t\t\t\t\t\t\t\t: 0)}px !important`,\r\n\t\t\t\t\t\t\t\t\tleft: `${(popupPosition?.left || 0) + (tooltipXaxis && tooltipXaxis != 'undefined'\r\n\t\t\t\t\t\t\t\t\t\t? (parseFloat(tooltipXaxis) || 0)\r\n\t\t\t\t\t\t\t\t\t\t: 0 )}px !important`,\r\n\t\t\t\t\t\t\t\t\toverflow: \"hidden\",\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tdisableScrollLock={true}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{ placeContent: \"end\", display: \"flex\" }}>\r\n\t\t\t\t\t\t\t\t{modalProperties?.DismissOption && (\r\n\t\t\t\t\t\t\t\t\t<IconButton\r\n\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t// Only close if explicitly requested by user clicking the close button\r\n\t\t\t\t\t\t\t\t\t\t\t//setOpenTooltip(false);\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\tposition: \"fixed\",\r\n\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"rgba(0, 0, 0, 0.06) 0px 4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\tleft: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tright: \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\tmargin: \"-15px\",\r\n\t\t\t\t\t\t\t\t\t\t\tbackground: \"#fff !important\",\r\n\t\t\t\t\t\t\t\t\t\t\tborder: \"1px solid #ccc\",\r\n\t\t\t\t\t\t\t\t\t\t\tzIndex: \"999999\",\r\n\t\t\t\t\t\t\t\t\t\t\tborderRadius: \"50px\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: \"5px !important\",\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<CloseIcon sx={{ zoom: 1, color: \"#000\" }} />\r\n\t\t\t\t\t\t\t\t\t</IconButton>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<PerfectScrollbar\r\n\t\t\t\tkey={`scrollbar-${needsScrolling}`}\r\n\t\t\t\tref={scrollbarRef}\r\n\t\t\t\tstyle={{ maxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",}}\r\n\t\t\t\toptions={{\r\n\t\t\t\t\tsuppressScrollY: !needsScrolling,\r\n\t\t\t\t\tsuppressScrollX: true,\r\n\t\t\t\t\twheelPropagation: false,\r\n\t\t\t\t\tswipeEasing: true,\r\n\t\t\t\t\tminScrollbarLength: 20,\r\n\t\t\t\t\tscrollingThreshold: 1000,\r\n\t\t\t\t\tscrollYMarginOffset: 0\r\n\t\t\t\t}}\r\n\t\t\t>\r\n\t\t\t\t\t\t\t<div style={{\r\n\t\t\t\t\t\t\t\tmaxHeight: hasOnlyButtons() || hasOnlyText() ? \"auto\" : \"400px\",\r\n\t\t\t\t\t\t\t\toverflow: hasOnlyButtons() || hasOnlyText() ? \"visible\" : \"hidden auto\",\r\n\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t<Box style={{\r\n\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"0\" :\r\n\t\t\t\t\t\t\t\t\t\t\thasOnlyText() ? \"0\" : (canvasProperties?.Padding || \"10px\"),\r\n\t\t\t\t\t\t\t\t\theight: hasOnlyButtons() ? \"auto\" : sectionHeight,\r\n\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() || hasOnlyText() ? \"auto\" : undefined,\r\n\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t}}>\r\n\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\tref={contentRef}\r\n\t\t\t\t\t\t\t\t\t\tdisplay=\"flex\"\r\n\t\t\t\t\t\t\t\t\t\tflexDirection=\"column\"\r\n\t\t\t\t\t\t\t\t\t\tflexWrap=\"wrap\"\r\n\t\t\t\t\t\t\t\t\t\tjustifyContent=\"center\"\r\n\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyText() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyText() ? \"0\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t{imageProperties?.map((imageProp: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\timageProp.CustomImage.map((customImg: any, imgIndex: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={`${imageProp.Id}-${imgIndex}`}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tcomponent=\"img\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsrc={customImg.Url}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\talt={customImg.AltText || \"Image\"}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmaxHeight: imageProp.MaxImageHeight || customImg.MaxImageHeight || \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: imageProp.Alignment || \"center\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tobjectFit: customImg.Fit || \"contain\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t//  width: \"500px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\theight: `${customImg.SectionHeight || 250}px`,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackground: customImg.BackgroundColor || \"#ffffff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: \"10px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tif (imageProp.Hyperlink) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst targetUrl = imageProp.Hyperlink;\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twindow.open(targetUrl, \"_blank\", \"noopener noreferrer\");\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tstyle={{ cursor: imageProp.Hyperlink ? \"pointer\" : \"default\" }}\r\n\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t\t\t\t{textFieldProperties?.map(\r\n\t\t\t\t\t\t\t\t\t\t\t(textField: any, index: any) =>\r\n\t\t\t\t\t\t\t\t\t\t\t\ttextField.Text && (\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t<Typography\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tclassName=\"qadpt-preview\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tkey={textField.Id || index} // Use a unique key, either Id or index\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextAlign: textField.TextProperties?.TextFormat || textStyle.textAlign,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: textField.TextProperties?.TextColor || textStyle.color,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twhiteSpace: \"pre-wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\twordBreak: \"break-word\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: \"0 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdangerouslySetInnerHTML={renderHtmlSnippet(textField.Text)} // Render the raw HTML\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t\t)\r\n\t\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\t{Object.keys(groupedButtons).map((containerId) => (\r\n\t\t\t\t\t\t\t\t\t\t<Box\r\n\t\t\t\t\t\t\t\t\t\t\tref={buttonContainerRef}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={containerId}\r\n\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\tdisplay: \"flex\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tjustifyContent: getAlignment(groupedButtons[containerId][0]?.Alignment),\r\n\t\t\t\t\t\t\t\t\t\t\t\tflexWrap: \"wrap\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? 0 : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: groupedButtons[containerId][0]?.BackgroundColor,\r\n\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"4px\" : \"5px 0\",\r\n\t\t\t\t\t\t\t\t\t\t\t\twidth: hasOnlyButtons() ? \"auto\" : \"100%\",\r\n\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: hasOnlyButtons() ? \"15px\" : undefined\r\n\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t{groupedButtons[containerId].map((button: any, index: number) => (\r\n\t\t\t\t\t\t\t\t\t\t\t\t<Button\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tkey={index}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => handleButtonAction(button.ButtonAction)}\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tvariant=\"contained\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\tsx={{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmarginRight: hasOnlyButtons() ? \"5px\" : \"13px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tmargin: hasOnlyButtons() ? \"4px\" : \"0 5px 5px 5px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tcolor: button.ButtonProperties?.ButtonTextColor || \"#fff\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborder: button.ButtonProperties?.ButtonBorderColor || \"transparent\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tfontSize: button.ButtonProperties?.FontSize || \"15px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\twidth: button.ButtonProperties?.Width || \"auto\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tpadding: hasOnlyButtons() ? \"var(--button-padding) !important\" : \"4px 8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tlineHeight: hasOnlyButtons() ? \"var(--button-lineheight)\" : \"normal\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\ttextTransform: \"none\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tborderRadius: button.ButtonProperties?.BorderRadius || \"8px\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tminWidth: hasOnlyButtons() ? \"fit-content\" : undefined,\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in normal state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\"&:hover\": {\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tbackgroundColor: button.ButtonProperties?.ButtonBackgroundColor || \"#007bff\", // Keep the same background color on hover\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\topacity: 0.9, // Slightly reduce opacity on hover for visual feedback\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\tboxShadow: \"none !important\", // Remove box shadow in hover state\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{button.ButtonName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</Button>\r\n\t\t\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t\t\t</Box>\r\n\t\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t\t</Box>\r\n\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t</PerfectScrollbar>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t{enableProgress && totalSteps>1 && selectedTemplate === \"Tour\" && <Box>{renderProgress()}</Box>}{\" \"}\r\n\t\t\t\t\t\t</Popover>\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\t\t\t)}\r\n\r\n\t\t\t<style>\r\n\t\t\t\t{`\r\n          @keyframes pulse {\r\n            0% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n            50% {\r\n              transform: scale(1.5);\r\n              opacity: 0.6;\r\n            }\r\n            100% {\r\n              transform: scale(1);\r\n              opacity: 1;\r\n            }\r\n          }\r\n\r\n          .pulse-animation {\r\n            animation: pulse 1.5s infinite;\r\n            pointer-events: auto !important;\r\n          }\r\n\r\n          .pulse-animation-removed {\r\n            pointer-events: auto !important;\r\n          }\r\n        `}\r\n\t\t\t</style>\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default HotspotPreview;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,GAAG,EAAEC,MAAM,EAAEC,UAAU,EAAEC,cAAc,EAAEC,aAAa,EAAEC,OAAO,EAAiBC,UAAU,QAAQ,eAAe;AAE1H,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,cAAc,MAAuB,yBAAyB;AACrE;AACA,OAAOC,gBAAgB,MAAM,yBAAyB;AACtD,OAAO,6CAA6C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA6GrD,MAAMC,cAAoC,GAAGA,CAAC;EAC1CC,QAAQ;EACRC,SAAS;EACTC,KAAK;EACLC,IAAI;EACJC,QAAQ;EACRC,OAAO;EACPC,UAAU;EACVC,UAAU;EACVC,QAAQ;EACRC,WAAW;EACXC,UAAU;EACVC,eAAe;EACfC,QAAQ;EACRC,mBAAmB;EACnBC,eAAe;EACfC,YAAY;EACZC,eAAe;EACfC,gBAAgB;EAChBC,WAAW;EACXC,oBAAoB;EACpBC,oBAAoB;EACpBC,YAAY;EACZC,cAAc;EACdC,iBAAiB;EACjBC,kBAAkB;EAClBC,kBAAkB;EAClBC,kBAAkB;EACnBC;AAEH,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;EACL,MAAM;IACLC,cAAc;IACdC,gBAAgB;IAChBC,oBAAoB;IACpBC,eAAe;IACfC,QAAQ;IACRC,YAAY;IACZC,YAAY;IACZC,cAAc;IACdC,WAAW;IACXC,gBAAgB;IAChBC,oBAAoB;IACpBC,oBAAoB;IACpBC,cAAc;IACdC;EACD,CAAC,GAAG5D,cAAc,CAAE6D,KAAkB,IAAKA,KAAK,CAAC;EACjD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzE,QAAQ,CAAqB,IAAI,CAAC;EAC5E;EACA;EACA,MAAM,CAAC0E,aAAa,EAAEC,gBAAgB,CAAC,GAAG3E,QAAQ,CAAuC,IAAI,CAAC;EAC9F,MAAM,CAAC4E,YAAY,EAAEC,eAAe,CAAC,GAAG7E,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC8E,WAAW,EAAEC,cAAc,CAAC,GAAG/E,QAAQ,CAAS,EAAE,CAAC,CAAC,CAAC;EAC5D,MAAMgF,UAAU,GAAG/E,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAMgF,kBAAkB,GAAGhF,MAAM,CAAiB,IAAI,CAAC;EACvD,IAAIiF,OAAY;EAChB,MAAMC,iBAAiB,GAAIC,KAAa,IAAyB;IAChE,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI;IACvB,IAAI;MACH,MAAMC,MAAM,GAAGC,QAAQ,CAACC,QAAQ,CAACH,KAAK,EAAEE,QAAQ,EAAE,IAAI,EAAEE,WAAW,CAACC,uBAAuB,EAAE,IAAI,CAAC;MAClG,MAAMC,IAAI,GAAGL,MAAM,CAACM,eAAe;MACnC,IAAID,IAAI,YAAYE,WAAW,EAAE;QAChC,OAAOF,IAAI;MACZ,CAAC,MAAM,IAAIA,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEG,aAAa,EAAE;QAC/B,OAAOH,IAAI,CAACG,aAAa,CAAC,CAAC;MAC5B,CAAC,MAAM;QACN,OAAO,IAAI;MACZ;IACD,CAAC,CAAC,OAAOC,KAAK,EAAE;MACfC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEV,KAAK,EAAEU,KAAK,CAAC;MACtD,OAAO,IAAI;IACZ;EACD,CAAC;EAED,MAAME,uBAAuB,GAAIC,OAAoB,IAAK;IACzD,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;IAC5C,OACCD,IAAI,CAACE,KAAK,GAAG,CAAC,IACdF,IAAI,CAACG,MAAM,GAAG,CAAC,IACfH,IAAI,CAACI,GAAG,KAAK,CAAC,IACdJ,IAAI,CAACK,IAAI,KAAK,CAAC,IACf,CAACC,MAAM,CAACC,KAAK,CAACP,IAAI,CAACI,GAAG,CAAC,IACvB,CAACE,MAAM,CAACC,KAAK,CAACP,IAAI,CAACK,IAAI,CAAC;EAE1B,CAAC;EACD,IAAInB,KAAU;EACd,IAAI7C,cAAc,EAAE6C,KAAK,GAAG7C,cAAc,aAAdA,cAAc,wBAAAO,qBAAA,GAAdP,cAAc,CAAEmE,SAAS,cAAA5D,qBAAA,wBAAAC,sBAAA,GAAzBD,qBAAA,CAA4B,CAAC,CAAC,cAAAC,sBAAA,uBAA9BA,sBAAA,CAAgC4D,WAAW;EACvE,MAAMC,kBAAkB,GAAIxB,KAAyB,IAAK;IACzD,MAAMa,OAAO,GAAGd,iBAAiB,CAACC,KAAK,IAAI,EAAE,CAAC;IAC9C,IAAIa,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5C,OAAO;QACNG,GAAG,EAAEJ,IAAI,CAACI,GAAG;QAAE;QACfC,IAAI,EAAEL,IAAI,CAACK,IAAI,CAAE;MAClB,CAAC;IACF;IACA,OAAO,IAAI;EACZ,CAAC;EACC;EACA,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAG9G,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM+G,YAAY,GAAG9G,MAAM,CAAM,IAAI,CAAC;EACxC;EACA,MAAM+G,sBAAsB,GAAGA,CAACC,WAAoB,EAAEnC,WAAmB,EAAEoC,OAAe,EAAEC,OAAe,KAAK;IAC/G,MAAMC,WAAW,GAAGH,WAAW,CAACI,CAAC,GAAGH,OAAO;IAC3C,MAAMI,UAAU,GAAGL,WAAW,CAACM,CAAC,GAAGJ,OAAO;;IAE1C;IACA,MAAMK,cAAc,GAAG1C,WAAW,GAAG,CAAC,CAAC,CAAC;IACxC,MAAM2C,cAAc,GAAG3C,WAAW,GAAG,EAAE,CAAC,CAAC;;IAEzC,OAAO;MACNwB,GAAG,EAAEgB,UAAU,GAAGI,MAAM,CAACC,OAAO,GAAGF,cAAc;MACjDlB,IAAI,EAAEa,WAAW,GAAGM,MAAM,CAACE,OAAO,GAAGJ;IACtC,CAAC;EACF,CAAC;EACDzH,SAAS,CAAC,MAAM;IACf,MAAMkG,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;IACxC,IAAIa,OAAO,EAAE;MACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5CxB,gBAAgB,CAAC;QAChB2B,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGoB,MAAM,CAACC,OAAO;QAAE;QAChCpB,IAAI,EAAEL,IAAI,CAACK,IAAI,GAAGmB,MAAM,CAACE;MAC1B,CAAC,CAAC;IACH;EACD,CAAC,EAAE,CAACxC,KAAK,CAAC,CAAC;EACXrF,SAAS,CAAC,MAAM;IACf,IAAI,OAAO2H,MAAM,KAAKG,SAAS,EAAE;MAChC,MAAMC,QAAQ,GAAGlB,kBAAkB,CAACxB,KAAK,IAAI,EAAE,CAAC;MAChD,IAAI0C,QAAQ,EAAE;QACbnD,gBAAgB,CAACmD,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAAC1C,KAAK,CAAC,CAAC;EACXrF,SAAS,CAAC,MAAM;IACf,MAAMkG,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;IACxC;IACA,IAAIa,OAAO,EAAE,CACb;EACD,CAAC,EAAE,CAAC1D,cAAc,CAAC,CAAC;EAEpBxC,SAAS,CAAC,MAAM;IAAA,IAAAgI,UAAA;IACf,MAAM9B,OAAO,GAAGd,iBAAiB,CAACjE,SAAS,aAATA,SAAS,wBAAA6G,UAAA,GAAT7G,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAqG,UAAA,uBAA5BA,UAAA,CAA8BpB,WAAW,CAAC;IAC5ElC,gBAAgB,CAACwB,OAAO,CAAC;IACzB,IAAIA,OAAO,EAAE;MACZA,OAAO,CAAC+B,KAAK,CAACC,eAAe,GAAG,gBAAgB;;MAEhD;MACA,MAAM/B,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;MAC5CxB,gBAAgB,CAAC;QAChB2B,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGoB,MAAM,CAACC,OAAO;QAC9BpB,IAAI,EAAEL,IAAI,CAACK,IAAI,GAAGmB,MAAM,CAACE;MAC1B,CAAC,CAAC;;MAEF;MACA,MAAMM,cAAc,GAAGR,MAAM,CAACS,WAAW;MACzC,MAAMC,aAAa,GAAGV,MAAM,CAACW,UAAU;;MAEvC;MACA,MAAMC,qBAAqB,GAC1BpC,IAAI,CAACqC,MAAM,GAAG,CAAC;MAAI;MACnBrC,IAAI,CAACI,GAAG,GAAG4B,cAAc;MAAI;MAC7BhC,IAAI,CAACsC,KAAK,GAAG,CAAC;MAAI;MAClBtC,IAAI,CAACK,IAAI,GAAG6B,aAAa,CAAC;MAC1B;;MAED;MACA,MAAMK,mBAAmB,GACxBvC,IAAI,CAACI,GAAG,IAAI,CAAC,EAAE;MAAI;MACnBJ,IAAI,CAACK,IAAI,IAAI,CAAC,EAAE;MAAI;MACpBL,IAAI,CAACqC,MAAM,IAAIL,cAAc,GAAG,EAAE;MAAI;MACtChC,IAAI,CAACsC,KAAK,IAAIJ,aAAa,GAAG,EAAE;MAAI;MACpClC,IAAI,CAACE,KAAK,GAAG,CAAC,IACdF,IAAI,CAACG,MAAM,GAAG,CACd;;MAED;MACAqC,UAAU,CAAC,MAAM;QAChB,IAAIzC,OAAO,KAAKqC,qBAAqB,IAAI,CAACG,mBAAmB,CAAC,EAAE;UAC/D1C,OAAO,CAAC4C,GAAG,CAAC,6DAA6D,CAAC;UAC1E;UACA,IAAI;YACH1C,OAAO,CAAC2C,cAAc,CAAC;cACtBC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,QAAQ;cAAE;cACjBC,MAAM,EAAE;YACT,CAAC,CAAC;YACFhD,OAAO,CAAC4C,GAAG,CAAC,mDAAmD,CAAC;UACjE,CAAC,CAAC,OAAOK,WAAW,EAAE;YACrBjD,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEkD,WAAW,CAAC;UACjE;QACD,CAAC,MAAM,IAAI/C,OAAO,EAAE;UACnBF,OAAO,CAAC4C,GAAG,CAAC,kEAAkE,CAAC;UAC/E;UACA,IAAI;YACH1C,OAAO,CAAC2C,cAAc,CAAC;cACtBC,QAAQ,EAAE,QAAQ;cAClBC,KAAK,EAAE,SAAS;cAAE;cAClBC,MAAM,EAAE;YACT,CAAC,CAAC;UACH,CAAC,CAAC,OAAOjD,KAAK,EAAE;YACfC,OAAO,CAAC4C,GAAG,CAAC,gDAAgD,EAAE7C,KAAK,CAAC;UACrE;QACD;MACD,CAAC,EAAE,GAAG,CAAC;IACR;EACD,CAAC,EAAE,CAAC5E,SAAS,EAAEQ,WAAW,CAAC,CAAC;;EAE5B;EACA;EACA,MAAM,GAAGuH,eAAe,CAAC,GAAGjJ,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAMkJ,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAIxF,gBAAgB,KAAK,MAAM,EAAE;MAChC,IAAIhC,WAAW,GAAGC,UAAU,EAAE;QAC7B8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;QAC/BF,UAAU,CAAC,CAAC;QACZ2H,eAAe,CAACzH,WAAW,GAAGC,UAAU,CAAC;MAC1C;IACD,CAAC,MAAM;MACN8B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/B,MAAM0H,eAAe,GAAG9D,QAAQ,CAAC+D,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACpB,KAAK,CAACsB,OAAO,GAAG,MAAM;QACtCF,eAAe,CAACG,MAAM,CAAC,CAAC;MACzB;IACD;EACD,CAAC;EAED,MAAMJ,eAAe,GAAIK,qBAA8B,IAAK;IAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IAC3D,OAAOV,qBAAqB,gBAC3B3I,OAAA,CAACG,cAAc;MACd2B,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCH,kBAAkB,EAAEA,kBAAmB;MACvCC,kBAAkB,EAAEA,kBAAmB;MACvCzB,QAAQ,EAAEA,QAAS;MACnBsB,cAAc,EAAEA,cAAe;MAC/BrB,SAAS,EAAEA,SAAU;MACrBI,OAAO,EAAEA,OAAQ;MACjBC,UAAU,EAAE4I,cAAe;MAC3B3I,UAAU,EAAE0H,cAAe;MAC3B/H,KAAK,EAAEA,KAAM;MACbC,IAAI,EAAEA,IAAK;MACXC,QAAQ,EAAEA,QAAS;MACnBK,WAAW,EAAEA,WAAW,GAAG,CAAE;MAC7BC,UAAU,EAAEA,UAAW;MACvBC,eAAe,EAAEA,eAAgB;MACjCC,QAAQ,EAAEA,QAAS;MACnBC,mBAAmB,EAAES,cAAc,aAAdA,cAAc,wBAAAkH,sBAAA,GAAdlH,cAAc,CAAEmE,SAAS,cAAA+C,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4B/H,WAAW,CAAC,cAAAgI,sBAAA,uBAAxCA,sBAAA,CAA0CU,mBAAoB;MACnFrI,eAAe,EAAEQ,cAAc,aAAdA,cAAc,wBAAAoH,sBAAA,GAAdpH,cAAc,CAAEmE,SAAS,cAAAiD,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BjI,WAAW,CAAC,cAAAkI,sBAAA,uBAAxCA,sBAAA,CAA0CS,eAAgB;MAC3ErI,YAAY,EACX,CAAAO,cAAc,aAAdA,cAAc,wBAAAsH,sBAAA,GAAdtH,cAAc,CAAEmE,SAAS,cAAAmD,sBAAA,wBAAAC,sBAAA,GAAzBD,sBAAA,CAA4BnI,WAAW,CAAC,cAAAoI,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0CQ,aAAa,cAAAP,sBAAA,wBAAAC,uBAAA,GAAvDD,sBAAA,CAAyDQ,GAAG,CAAEC,OAAY,IACzEA,OAAO,CAACC,aAAa,CAACF,GAAG,CAAEG,MAAW,KAAM;QAC3C,GAAGA,MAAM;QACTC,WAAW,EAAEH,OAAO,CAACI,EAAE,CAAE;MAC1B,CAAC,CAAC,CACH,CAAC,cAAAZ,uBAAA,uBALDA,uBAAA,CAKGa,MAAM,CAAC,CAACC,GAAmB,EAAEC,IAAS,KAAKD,GAAG,CAACE,MAAM,CAACD,IAAI,CAAC,EAAE,EAAE,CAAC,KAAI,EACvE;MACD9I,eAAe,EAAEA,eAAgB;MACjCC,gBAAgB,EAAEA,gBAAiB;MACnCC,WAAW,EAAEA,WAAY;MACzBG,YAAY,EAAEA,YAAa;MAC3BE,iBAAiB,EAAE,CAAAD,cAAc,aAAdA,cAAc,wBAAA0H,uBAAA,GAAd1H,cAAc,CAAEmE,SAAS,cAAAuD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BvI,WAAW,GAAG,CAAC,CAAC,cAAAwI,uBAAA,uBAA5CA,uBAAA,CAA8Ce,OAAO,KAAI,CAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/E,CAAC,GACC,IAAI;EACT,CAAC;EAED,MAAMlB,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAIzI,WAAW,GAAG,CAAC,EAAE;MACpB+B,cAAc,CAAC/B,WAAW,GAAG,CAAC,CAAC;MAC/BH,UAAU,CAAC,CAAC;IACb;EACD,CAAC;EACDxB,SAAS,CAAC,MAAM;IACf,IAAIuC,YAAY,EAAE;MACjB2G,eAAe,CAAC,IAAI,CAAC;IACtB,CAAC,MAAM;MACNA,eAAe,CAAC,KAAK,CAAC;IACvB;EACD,CAAC,EAAE,CAAC3G,YAAY,CAAC,CAAC;EAClB;EACA,MAAMgJ,4BAA4B,GACjCxD,QAAgB,IACqD;IACrE,QAAQA,QAAQ;MACf,KAAK,UAAU;QACd,OAAO;UACNyD,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAO,CAAC;UACrDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,WAAW;QACf,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACtDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAQ;QACzD,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,YAAY;QAChB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,KAAK;YAAEC,UAAU,EAAE;UAAS,CAAC;UACvDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,aAAa;QACjB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO,CAAC;UACxDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ;QAC5D,CAAC;MACF,KAAK,eAAe;QACnB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;MACF,KAAK,cAAc;QAClB,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAQ,CAAC;UACzDC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAO;QAC3D,CAAC;MACF;QACC,OAAO;UACNF,YAAY,EAAE;YAAEC,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS,CAAC;UAC1DC,eAAe,EAAE;YAAEF,QAAQ,EAAE,QAAQ;YAAEC,UAAU,EAAE;UAAS;QAC7D,CAAC;IACH;EACD,CAAC;EAED,MAAM;IAAEF,YAAY;IAAEG;EAAgB,CAAC,GAAGJ,4BAA4B,CAAC,CAAApJ,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyJ,QAAQ,KAAI,eAAe,CAAC;EAErH,MAAMC,SAAS,GAAG;IACjBC,UAAU,EAAE/J,mBAAmB,aAAnBA,mBAAmB,gBAAAkB,qBAAA,GAAnBlB,mBAAmB,CAAEgK,cAAc,cAAA9I,qBAAA,eAAnCA,qBAAA,CAAqC+I,IAAI,GAAG,MAAM,GAAG,QAAQ;IACzEC,SAAS,EAAElK,mBAAmB,aAAnBA,mBAAmB,gBAAAmB,sBAAA,GAAnBnB,mBAAmB,CAAEgK,cAAc,cAAA7I,sBAAA,eAAnCA,sBAAA,CAAqCgJ,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5EC,KAAK,EAAE,CAAApK,mBAAmB,aAAnBA,mBAAmB,wBAAAoB,sBAAA,GAAnBpB,mBAAmB,CAAEgK,cAAc,cAAA5I,sBAAA,uBAAnCA,sBAAA,CAAqCiJ,SAAS,KAAI,SAAS;IAClEC,SAAS,EAAE,CAAAtK,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEuK,SAAS,KAAI;EAC9C,CAAC;;EAED;;EAEA,MAAMC,iBAAiB,GAAIC,OAAe,IAAK;IAC9C;IACA,OAAO;MACNC,MAAM,EAAED,OAAO,CAACE,OAAO,CAAC,qCAAqC,EAAE,CAACC,MAAM,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,KAAK;QACtF,OAAO,GAAGF,EAAE,GAAGC,EAAE,oBAAoBC,EAAE,EAAE;MAC1C,CAAC;IACF,CAAC;EACF,CAAC;;EAED;EACA,MAAMC,cAAc,GAAGA,CAAA,KAAM;IAC5B,MAAMC,QAAQ,GAAGhL,eAAe,IAAIA,eAAe,CAACiL,MAAM,GAAG,CAAC,IAC7DjL,eAAe,CAACkL,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGxL,mBAAmB,IAAIA,mBAAmB,CAACkL,MAAM,GAAG,CAAC,IACpElL,mBAAmB,CAACmL,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAG1L,YAAY,IAAIA,YAAY,CAACgL,MAAM,GAAG,CAAC;IAE1D,OAAOU,UAAU,IAAI,CAACX,QAAQ,IAAI,CAACO,OAAO;EAC3C,CAAC;;EAED;EACA,MAAMK,WAAW,GAAGA,CAAA,KAAM;IACzB,MAAMZ,QAAQ,GAAGhL,eAAe,IAAIA,eAAe,CAACiL,MAAM,GAAG,CAAC,IAC7DjL,eAAe,CAACkL,IAAI,CAAEC,IAAS,IAC9BA,IAAI,CAACC,WAAW,IAAID,IAAI,CAACC,WAAW,CAACF,IAAI,CAAEG,GAAQ,IAAKA,GAAG,CAACC,GAAG,CAChE,CAAC;IAEF,MAAMC,OAAO,GAAGxL,mBAAmB,IAAIA,mBAAmB,CAACkL,MAAM,GAAG,CAAC,IACpElL,mBAAmB,CAACmL,IAAI,CAAEM,KAAU,IAAKA,KAAK,CAACC,IAAI,IAAID,KAAK,CAACC,IAAI,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;IAEjF,MAAMC,UAAU,GAAG1L,YAAY,IAAIA,YAAY,CAACgL,MAAM,GAAG,CAAC;IAE1D,OAAOM,OAAO,IAAI,CAACP,QAAQ,IAAI,CAACW,UAAU;EAC3C,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,mBAAA,EAAAC,qBAAA;IACnC;IACA,IAAI5L,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE6L,KAAK,IAAI,CAACjB,cAAc,CAAC,CAAC,IAAI,CAACa,WAAW,CAAC,CAAC,EAAE;MACnE,OAAO,GAAGzL,gBAAgB,CAAC6L,KAAK,IAAI;IACrC;;IAEA;IACA,IAAIjB,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,EAAE;MACtC,OAAO,MAAM;IACd;;IAEA;IACA,MAAMK,YAAY,GAAG,EAAAH,mBAAA,GAAA7I,UAAU,CAACiJ,OAAO,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBK,WAAW,KAAI,CAAC;IACzD,MAAMC,WAAW,GAAG,EAAAL,qBAAA,GAAA7I,kBAAkB,CAACgJ,OAAO,cAAAH,qBAAA,uBAA1BA,qBAAA,CAA4BI,WAAW,KAAI,CAAC;;IAEhE;IACA,MAAME,YAAY,GAAGC,IAAI,CAACC,GAAG,CAACN,YAAY,EAAEG,WAAW,CAAC;;IAExD;IACA,MAAMI,WAAW,GAAGH,YAAY,GAAG,EAAE,CAAC,CAAC;;IAEvC;IACA,MAAMI,QAAQ,GAAG,GAAG,CAAC,CAAC;IACtB,MAAMC,QAAQ,GAAG,GAAG,CAAC,CAAC;;IAEtB,MAAMC,UAAU,GAAGL,IAAI,CAACC,GAAG,CAACE,QAAQ,EAAEH,IAAI,CAACM,GAAG,CAACJ,WAAW,EAAEE,QAAQ,CAAC,CAAC;IAEtE,OAAO,GAAGC,UAAU,IAAI;EACzB,CAAC;;EAED;EACA3O,SAAS,CAAC,MAAM;IACf;IACA6O,qBAAqB,CAAC,MAAM;MAC3B,MAAMC,QAAQ,GAAGjB,qBAAqB,CAAC,CAAC;MACxC/I,eAAe,CAACgK,QAAQ,CAAC;IAC1B,CAAC,CAAC;EACH,CAAC,EAAE,CAAC/M,mBAAmB,EAAEC,eAAe,EAAEC,YAAY,EAAEN,WAAW,CAAC,CAAC;;EAErE;EACA3B,SAAS,CAAC,MAAM;IACf,IAAIqF,KAAK,IAAIN,WAAW,EAAE;MACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;MACxC,IAAIa,OAAO,EAAE;QAAA,IAAA6I,qBAAA;QACZ,MAAM5I,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;QAC5C,MAAM4I,eAAe,IAAAD,qBAAA,GAAGnL,oBAAoB,CAAC,CAAC,CAAC,cAAAmL,qBAAA,uBAAvBA,qBAAA,CAAyBE,QAAQ;QACzD,MAAM9H,OAAO,GAAG+H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;QAC7D,MAAM/H,OAAO,GAAG8H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;QAE7D,MAAMC,QAAQ,GAAGpI,sBAAsB,CAACd,IAAI,EAAEpB,WAAW,EAAEoC,OAAO,EAAEC,OAAO,CAAC;QAC5ExC,gBAAgB,CAACyK,QAAQ,CAAC;MAC3B;IACD;EACD,CAAC,EAAE,CAACtK,WAAW,EAAEM,KAAK,EAAEzB,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,MAAMsP,YAAY,GAAGA,CAAA,KAAM;MAC1B,IAAIjK,KAAK,IAAIN,WAAW,EAAE;QACzB,MAAMmB,OAAO,GAAGd,iBAAiB,CAACC,KAAK,CAAC;QACxC,IAAIa,OAAO,EAAE;UAAA,IAAAqJ,sBAAA;UACZ,MAAMpJ,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;UAC5C,MAAM4I,eAAe,IAAAO,sBAAA,GAAG3L,oBAAoB,CAAC,CAAC,CAAC,cAAA2L,sBAAA,uBAAvBA,sBAAA,CAAyBN,QAAQ;UACzD,MAAM9H,OAAO,GAAG+H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAM/H,OAAO,GAAG8H,UAAU,CAAC,CAAAF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEI,SAAS,KAAI,GAAG,CAAC;UAE7D,MAAMC,QAAQ,GAAGpI,sBAAsB,CAACd,IAAI,EAAEpB,WAAW,EAAEoC,OAAO,EAAEC,OAAO,CAAC;UAC5ExC,gBAAgB,CAACyK,QAAQ,CAAC;QAC3B;MACD;IACD,CAAC;IAED1H,MAAM,CAAC6H,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAM3H,MAAM,CAAC8H,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EAChE,CAAC,EAAE,CAACjK,KAAK,EAAEN,WAAW,EAAEnB,oBAAoB,CAAC,CAAC;EAE9C,MAAM8L,cAAc,GAAGzN,YAAY,CAAC6I,MAAM,CAAC,CAACC,GAAQ,EAAEJ,MAAW,KAAK;IACrE,MAAMgF,WAAW,GAAGhF,MAAM,CAACC,WAAW,IAAI,SAAS,CAAC,CAAC;IACrD,IAAI,CAACG,GAAG,CAAC4E,WAAW,CAAC,EAAE;MACtB5E,GAAG,CAAC4E,WAAW,CAAC,GAAG,EAAE;IACtB;IACA5E,GAAG,CAAC4E,WAAW,CAAC,CAACC,IAAI,CAACjF,MAAM,CAAC;IAC7B,OAAOI,GAAG;EACX,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,MAAM8E,WAAW,GAAG;IACnB9H,QAAQ,EAAE,CAAA5F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyJ,QAAQ,KAAI,eAAe;IACvDkE,YAAY,EAAE,CAAA3N,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE4N,MAAM,KAAI,KAAK;IAC/CC,WAAW,EAAE,CAAA7N,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE8N,UAAU,KAAI,KAAK;IAClDC,WAAW,EAAE,CAAA/N,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEgO,WAAW,KAAI,OAAO;IACrDC,WAAW,EAAE,OAAO;IACpBlI,eAAe,EAAE,CAAA/F,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEkO,eAAe,KAAI,OAAO;IAC7D3B,QAAQ,EAAG3B,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC7D/I,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE6L,KAAK,GAAG,GAAG7L,gBAAgB,CAAC6L,KAAK,eAAe,GAAG,OAAO;IAChF3H,KAAK,EAAG0G,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAI,iBAAiB,GAC1D/I,YAAY,GAAG,GAAGA,YAAY,aAAa,GAC3C1C,gBAAgB,aAAhBA,gBAAgB,eAAhBA,gBAAgB,CAAE6L,KAAK,GAAG,GAAG7L,gBAAgB,CAAC6L,KAAK,eAAe,GAAG;EAC1E,CAAC;EACD,MAAMsC,aAAa,GAAG,EAAAlN,gBAAA,GAAApB,eAAe,CAACL,WAAW,GAAG,CAAC,CAAC,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAhCD,gBAAA,CAAkCgK,WAAW,cAAA/J,qBAAA,wBAAAC,sBAAA,GAA7CD,qBAAA,CAAgD1B,WAAW,GAAG,CAAC,CAAC,cAAA2B,sBAAA,uBAAhEA,sBAAA,CAAkEiN,aAAa,KAAI,MAAM;EAC/G,MAAMC,kBAAkB,GAAIC,MAAW,IAAK;IAC3C,IAAIA,MAAM,CAACC,MAAM,KAAK,UAAU,IAAID,MAAM,CAACC,MAAM,KAAK,MAAM,IAAID,MAAM,CAACC,MAAM,KAAK,SAAS,EAAE;MAC5F,MAAMC,SAAS,GAAGF,MAAM,CAACG,SAAS;MAClC,IAAIH,MAAM,CAACI,WAAW,KAAK,UAAU,EAAE;QACtC;QACAlJ,MAAM,CAACmJ,QAAQ,CAACC,IAAI,GAAGJ,SAAS;MACjC,CAAC,MAAM;QACN;QACAhJ,MAAM,CAACqJ,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;MACxD;IACD,CAAC,MAAM;MACN,IACCF,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACC,MAAM,IAAI,UAAU,IAC3BD,MAAM,CAACI,WAAW,IAAI,UAAU,IAChCJ,MAAM,CAACI,WAAW,IAAI,UAAU,EAC/B;QACDzG,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACNqG,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACC,MAAM,IAAI,MAAM,IACvBD,MAAM,CAACI,WAAW,IAAI,MAAM,IAC5BJ,MAAM,CAACI,WAAW,IAAI,MAAM,EAC3B;QACD1H,cAAc,CAAC,CAAC;MACjB,CAAC,MAAM,IACNsH,MAAM,CAACC,MAAM,IAAI,SAAS,IAC1BD,MAAM,CAACI,WAAW,IAAI,SAAS,EAC9B;QAAA,IAAAI,uBAAA,EAAAC,uBAAA;QACD;QACAxN,cAAc,CAAC,CAAC,CAAC;QACjB;QACA,IAAIlB,cAAc,aAAdA,cAAc,gBAAAyO,uBAAA,GAAdzO,cAAc,CAAEmE,SAAS,cAAAsK,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,eAA9BA,uBAAA,CAAgCtK,WAAW,EAAE;UAChD,MAAMuK,gBAAgB,GAAG/L,iBAAiB,CAAC5C,cAAc,CAACmE,SAAS,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC;UACnF,IAAIuK,gBAAgB,EAAE;YACrBA,gBAAgB,CAACtI,cAAc,CAAC;cAAEC,QAAQ,EAAE;YAAS,CAAC,CAAC;UACxD;QACD;MACD;IACD;IACAI,eAAe,CAAC,KAAK,CAAC;EACvB,CAAC;EACDlJ,SAAS,CAAC,MAAM;IAAA,IAAAoR,WAAA,EAAAC,mBAAA;IACf,IAAIlQ,SAAS,aAATA,SAAS,gBAAAiQ,WAAA,GAATjQ,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,cAAAyP,WAAA,gBAAAC,mBAAA,GAA5BD,WAAA,CAA8BlG,OAAO,cAAAmG,mBAAA,eAArCA,mBAAA,CAAuCC,aAAa,EAAE;MACzD;MACArN,cAAc,CAAC,IAAI,CAAC;IACrB;EACD,CAAC,EAAE,CAAC9C,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAGQ,WAAW,GAAG,CAAC,CAAC,EAAEA,WAAW,EAAEsC,cAAc,CAAC,CAAC;;EAE/D;EACAjE,SAAS,CAAC,MAAM;IACf,IAAI4C,kBAAkB,EAAE;MAAA,IAAA2O,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAM5C,eAAe,GAAG3K,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAA2N,sBAAA,GAApB3N,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAA4P,sBAAA,eAAvCA,sBAAA,CAAyCtC,QAAQ,GAC5GrL,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACsN,QAAQ,IAAAuC,sBAAA,GAC9C5N,oBAAoB,CAAC,CAAC,CAAC,cAAA4N,sBAAA,uBAAvBA,sBAAA,CAAyBvC,QAAQ;MACpC,MAAM4C,WAAW,GAAGxN,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAiP,uBAAA,GAAdjP,cAAc,CAAEmE,SAAS,cAAA8K,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B9P,WAAW,GAAG,CAAC,CAAC,cAAA+P,uBAAA,uBAA5CA,uBAAA,CAA8CxG,OAAO,GACrD1I,cAAc,aAAdA,cAAc,wBAAAmP,uBAAA,GAAdnP,cAAc,CAAEmE,SAAS,cAAAgL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgC1G,OAAO;;MAE1C;MACA;MACA,IAAI8D,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEsC,aAAa,EAAE;QACnC;QACArN,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACrB,kBAAkB,EAAEgB,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,IAAI6C,kBAAkB,EAAE;MAAA,IAAAiP,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;MACvB;MACA,MAAMnD,eAAe,GAAG3K,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAkO,sBAAA,GAApBlO,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAmQ,sBAAA,eAAvCA,sBAAA,CAAyC7C,QAAQ,GAC5GrL,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACsN,QAAQ,IAAA8C,sBAAA,GAC9CnO,oBAAoB,CAAC,CAAC,CAAC,cAAAmO,sBAAA,uBAAvBA,sBAAA,CAAyB9C,QAAQ;MACpC,MAAM4C,WAAW,GAAGxN,oBAAoB,KAAK,SAAS,GACnD7B,cAAc,aAAdA,cAAc,wBAAAwP,uBAAA,GAAdxP,cAAc,CAAEmE,SAAS,cAAAqL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BrQ,WAAW,GAAG,CAAC,CAAC,cAAAsQ,uBAAA,uBAA5CA,uBAAA,CAA8C/G,OAAO,GACrD1I,cAAc,aAAdA,cAAc,wBAAA0P,uBAAA,GAAd1P,cAAc,CAAEmE,SAAS,cAAAuL,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCjH,OAAO;;MAE1C;MACA,IAAI8D,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAEsC,aAAa,EAAE;QACnC;QACArN,cAAc,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QACN;QACAA,cAAc,CAAC,KAAK,CAAC;MACtB;IACD;EACD,CAAC,EAAE,CAACpB,kBAAkB,EAAEe,oBAAoB,CAAC,CAAC;;EAE9C;EACA5D,SAAS,CAAC,MAAM;IACf,MAAMoS,iBAAiB,GAAIC,CAAa,IAAK;MAC5C,MAAMC,cAAc,GAAG/M,QAAQ,CAAC+D,cAAc,CAAC,cAAc,CAAC;;MAE9D;MACA,IAAIgJ,cAAc,IAAIA,cAAc,CAACC,QAAQ,CAACF,CAAC,CAACG,MAAc,CAAC,EAAE;QAChE;MACD;;MAEA;MACA;IACD,CAAC;IAEDjN,QAAQ,CAACiK,gBAAgB,CAAC,OAAO,EAAE4C,iBAAiB,CAAC;IAErD,OAAO,MAAM;MACZ7M,QAAQ,CAACkK,mBAAmB,CAAC,OAAO,EAAE2C,iBAAiB,CAAC;IACzD,CAAC;EACF,CAAC,EAAE,CAACxO,oBAAoB,CAAC,CAAC;EAC1B;EACA5D,SAAS,CAAC,MAAM;IACf,MAAMyS,iBAAiB,GAAGA,CAAA,KAAM;MAC/B,IAAIxN,UAAU,CAACiJ,OAAO,EAAE;QACvB;QACAjJ,UAAU,CAACiJ,OAAO,CAACjG,KAAK,CAAC3B,MAAM,GAAG,MAAM;QACxC,MAAMoM,aAAa,GAAGzN,UAAU,CAACiJ,OAAO,CAACyE,YAAY;QACrD,MAAMC,eAAe,GAAG,GAAG,CAAC,CAAC;QAC7B,MAAMC,YAAY,GAAGH,aAAa,GAAGE,eAAe;QAGpD7L,iBAAiB,CAAC8L,YAAY,CAAC;;QAE/B;QACA,IAAI7L,YAAY,CAACkH,OAAO,EAAE;UACzB;UACA,IAAIlH,YAAY,CAACkH,OAAO,CAAC4E,YAAY,EAAE;YACtC9L,YAAY,CAACkH,OAAO,CAAC4E,YAAY,CAAC,CAAC;UACpC;UACA;UACAnK,UAAU,CAAC,MAAM;YAChB,IAAI3B,YAAY,CAACkH,OAAO,IAAIlH,YAAY,CAACkH,OAAO,CAAC4E,YAAY,EAAE;cAC9D9L,YAAY,CAACkH,OAAO,CAAC4E,YAAY,CAAC,CAAC;YACpC;UACD,CAAC,EAAE,EAAE,CAAC;QACP;MACD;IACD,CAAC;IAGDL,iBAAiB,CAAC,CAAC;IAGnB,MAAMM,QAAQ,GAAG,CAChBpK,UAAU,CAAC8J,iBAAiB,EAAE,EAAE,CAAC,EACjC9J,UAAU,CAAC8J,iBAAiB,EAAE,GAAG,CAAC,EAClC9J,UAAU,CAAC8J,iBAAiB,EAAE,GAAG,CAAC,EAClC9J,UAAU,CAAC8J,iBAAiB,EAAE,GAAG,CAAC,CAClC;IAGD,IAAIO,cAAqC,GAAG,IAAI;IAChD,IAAIC,gBAAyC,GAAG,IAAI;IAEpD,IAAIhO,UAAU,CAACiJ,OAAO,IAAIvG,MAAM,CAACuL,cAAc,EAAE;MAChDF,cAAc,GAAG,IAAIE,cAAc,CAAC,MAAM;QACzCvK,UAAU,CAAC8J,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFO,cAAc,CAACG,OAAO,CAAClO,UAAU,CAACiJ,OAAO,CAAC;IAC3C;IAGA,IAAIjJ,UAAU,CAACiJ,OAAO,IAAIvG,MAAM,CAACyL,gBAAgB,EAAE;MAClDH,gBAAgB,GAAG,IAAIG,gBAAgB,CAAC,MAAM;QAC7CzK,UAAU,CAAC8J,iBAAiB,EAAE,EAAE,CAAC;MAClC,CAAC,CAAC;MACFQ,gBAAgB,CAACE,OAAO,CAAClO,UAAU,CAACiJ,OAAO,EAAE;QAC5CmF,SAAS,EAAE,IAAI;QACfC,OAAO,EAAE,IAAI;QACbC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;MACnC,CAAC,CAAC;IACH;IAEA,OAAO,MAAM;MACZT,QAAQ,CAACU,OAAO,CAACC,YAAY,CAAC;MAC9B,IAAIV,cAAc,EAAE;QACnBA,cAAc,CAACW,UAAU,CAAC,CAAC;MAC5B;MACA,IAAIV,gBAAgB,EAAE;QACrBA,gBAAgB,CAACU,UAAU,CAAC,CAAC;MAC9B;IACD,CAAC;EACF,CAAC,EAAE,CAAChS,WAAW,CAAC,CAAC;EACjB;EACA;;EAEA,SAASiS,YAAYA,CAACC,SAAiB,EAAE;IACxC,QAAQA,SAAS;MAChB,KAAK,OAAO;QACX,OAAO,YAAY;MACpB,KAAK,KAAK;QACT,OAAO,UAAU;MAClB,KAAK,QAAQ;MACb;QACC,OAAO,QAAQ;IACjB;EACD;EACA,MAAMC,iBAAiB,GAAGA,CAAC/L,QAAgB,GAAG,eAAe,KAAK;IACjE,QAAQA,QAAQ;MACf,KAAK,aAAa;QACjB,OAAO;UAAExB,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,cAAc;QAClB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAkB,CAAC;MAClC,KAAK,eAAe;QACnB,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,aAAa;QACjB,OAAO;UAAEA,GAAG,EAAEjF,QAAQ,KAAK,EAAE,GAAG,gBAAgB,GAAG;QAAiB,CAAC;MACtE,KAAK,cAAc;QAClB,OAAO;UAAEiF,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,UAAU;QACd,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,WAAW;QACf,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;MACjC,KAAK,YAAY;QAChB,OAAO;UAAEA,GAAG,EAAE;QAAgB,CAAC;MAChC;QACC,OAAO;UAAEA,GAAG,EAAE;QAAiB,CAAC;IAClC;EACD,CAAC;;EAEA;EACD,MAAMwN,kBAAkB,GAAGA,CAACC,QAAgB,EAAEhF,eAAoB,EAAE6C,WAAgB,KAAK;IACxF,IAAIxN,oBAAoB,KAAK,SAAS,EAAE;MACvC;MACA,QAAQ2P,QAAQ;QACf,KAAK,gBAAgB;UACpB,OAAO,CAAAnC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEoC,cAAc,MAAKnM,SAAS,GAAG+J,WAAW,CAACoC,cAAc,GAAGjF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEiF,cAAc;QAChH,KAAK,eAAe;UACnB;UACA,OAAO,CAAApC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEqC,4BAA4B,MAAKpM,SAAS,GAAG+J,WAAW,CAACqC,4BAA4B,GAAGlF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkF,4BAA4B;QAC1J,KAAK,UAAU;UACd,OAAO,CAAArC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEsC,QAAQ,MAAKrM,SAAS,GAAG+J,WAAW,CAACsC,QAAQ,GAAGnF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEmF,QAAQ;QAC9F,KAAK,eAAe;UACnB,OAAO,CAAAtC,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEP,aAAa,MAAKxJ,SAAS,GAAG+J,WAAW,CAACP,aAAa,GAAGtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsC,aAAa;QAC7G;UACC,OAAOtC,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;MACpC;IACD,CAAC,MAAM;MACN;MACA,IAAIA,QAAQ,KAAK,eAAe,EAAE;QACjC,OAAOhF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEkF,4BAA4B;MACrD;MACA,OAAOlF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGgF,QAAQ,CAAC;IACnC;EACD,CAAC;EAED,MAAMI,kBAAkB,GAAGA,CAACjP,OAAY,EAAE6J,eAAoB,EAAE6C,WAAgB,EAAErL,IAAS,EAAED,GAAQ,KAAK;IACzGpB,OAAO,CAAC8C,KAAK,CAACF,QAAQ,GAAG,UAAU;IACnC5C,OAAO,CAAC8C,KAAK,CAACzB,IAAI,GAAG,GAAGA,IAAI,IAAI;IAChCrB,OAAO,CAAC8C,KAAK,CAAC1B,GAAG,GAAG,GAAGA,GAAG,IAAI;IAC9BpB,OAAO,CAAC8C,KAAK,CAAC5B,KAAK,GAAG,GAAG2I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,IAAI,IAAI,CAAC,CAAC;IACpDlP,OAAO,CAAC8C,KAAK,CAAC3B,MAAM,GAAG,GAAG0I,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEqF,IAAI,IAAI;IACnDlP,OAAO,CAAC8C,KAAK,CAACC,eAAe,GAAG8G,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEsF,KAAK;IACtDnP,OAAO,CAAC8C,KAAK,CAAC6H,YAAY,GAAG,KAAK;IAClC3K,OAAO,CAAC8C,KAAK,CAACsM,MAAM,GAAG,iBAAiB,CAAC,CAAC;IAC1CpP,OAAO,CAAC8C,KAAK,CAACuM,UAAU,GAAG,MAAM;IACjCrP,OAAO,CAAC8C,KAAK,CAACwM,aAAa,GAAG,MAAM,CAAC,CAAC;IACtCtP,OAAO,CAACuP,SAAS,GAAG,EAAE;IAEtB,IAAI,CAAA1F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,MAAK,MAAM,IAAI,CAAA3F,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE2F,IAAI,MAAK,UAAU,EAAE;MAC7E,MAAMC,QAAQ,GAAGrP,QAAQ,CAACsP,aAAa,CAAC,MAAM,CAAC;MAC/CD,QAAQ,CAACE,SAAS,GAAG9F,eAAe,CAAC2F,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;MAChEC,QAAQ,CAAC3M,KAAK,CAACkE,KAAK,GAAG,OAAO;MAC9ByI,QAAQ,CAAC3M,KAAK,CAAC8M,QAAQ,GAAG,MAAM;MAChCH,QAAQ,CAAC3M,KAAK,CAAC6D,UAAU,GAAG,MAAM;MAClC8I,QAAQ,CAAC3M,KAAK,CAACgE,SAAS,GAAG+C,eAAe,CAAC2F,IAAI,KAAK,MAAM,GAAG,QAAQ,GAAG,QAAQ;MAChFC,QAAQ,CAAC3M,KAAK,CAACsB,OAAO,GAAG,MAAM;MAC/BqL,QAAQ,CAAC3M,KAAK,CAAC+M,UAAU,GAAG,QAAQ;MACpCJ,QAAQ,CAAC3M,KAAK,CAACgN,cAAc,GAAG,QAAQ;MACxCL,QAAQ,CAAC3M,KAAK,CAAC5B,KAAK,GAAG,MAAM;MAC7BuO,QAAQ,CAAC3M,KAAK,CAAC3B,MAAM,GAAG,MAAM;MAC9BnB,OAAO,CAAC+P,WAAW,CAACN,QAAQ,CAAC;IAC9B;;IAEA;IACA;IACA,MAAMO,qBAAqB,GAAGpB,kBAAkB,CAAC,gBAAgB,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IAChG,MAAMuD,WAAW,GAAG/Q,oBAAoB,KAAK,SAAS,GAClD8Q,qBAAqB,KAAK,KAAK,IAAI,CAAChQ,OAAO,CAACkQ,aAAa,GACzDrG,eAAe,IAAI7K,gBAAgB,IAAI,CAACgB,OAAO,CAACkQ,aAAc;IAElE,IAAID,WAAW,EAAE;MACPjQ,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,iBAAiB,CAAC;MACxCpQ,OAAO,CAACmQ,SAAS,CAAC9L,MAAM,CAAC,yBAAyB,CAAC;IACvD,CAAC,MAAM;MACHrE,OAAO,CAACmQ,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;MAC3CrE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACpD;;IAEN;IACApQ,OAAO,CAAC8C,KAAK,CAACsB,OAAO,GAAG,MAAM;IAC9BpE,OAAO,CAAC8C,KAAK,CAACwM,aAAa,GAAG,MAAM;;IAEpC;IACA;IACA;IACA,MAAMe,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IACvF,IAAI2D,aAAa,EAAE;MAClBvR,cAAc,CAAC,IAAI,CAAC;IACrB,CAAC,MAAM;MACN;MACA;IAAA;;IAGD;IACA;IACA,IAAI,CAACkB,OAAO,CAACsQ,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,MAAMC,UAAU,GAAGvQ,OAAO,CAACwQ,SAAS,CAAC,IAAI,CAAgB;MACzD;MACA,IAAIxQ,OAAO,CAACkQ,aAAa,KAAKvN,SAAS,EAAE;QAC9B4N,UAAU,CAASL,aAAa,GAAGlQ,OAAO,CAACkQ,aAAa;MAC7D;MACN,IAAIlQ,OAAO,CAACyQ,UAAU,EAAE;QACvBzQ,OAAO,CAACyQ,UAAU,CAACC,YAAY,CAACH,UAAU,EAAEvQ,OAAO,CAAC;QACpDA,OAAO,GAAGuQ,UAAU;MACrB;IACD;;IAEA;IACAvQ,OAAO,CAAC8C,KAAK,CAACwM,aAAa,GAAG,MAAM;;IAEpC;IACA,MAAMqB,QAAQ,GAAG/B,kBAAkB,CAAC,UAAU,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;IAC7E,MAAMkE,WAAW,GAAI1D,CAAQ,IAAK;MACjCA,CAAC,CAAC2D,eAAe,CAAC,CAAC;MACnBhQ,OAAO,CAAC4C,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIkN,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACA7R,cAAc,CAAC,IAAI,CAAC;;QAEpB;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACA,MAAMuT,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;QAC9F,IAAIoE,oBAAoB,EAAE;UACzB9Q,OAAO,CAACmQ,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;UAC3CrE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChDpQ,OAAO,CAACkQ,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;IAED,MAAMa,cAAc,GAAI7D,CAAQ,IAAK;MACpCA,CAAC,CAAC2D,eAAe,CAAC,CAAC;;MAEnB;MACA;MACA,MAAMR,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;MACvF,IAAIiE,QAAQ,KAAK,kBAAkB,IAAI,CAACN,aAAa,EAAE;QACtD;MAAA;IAEF,CAAC;IAED,MAAMW,WAAW,GAAI9D,CAAQ,IAAK;MACjCA,CAAC,CAAC2D,eAAe,CAAC,CAAC;MACnBhQ,OAAO,CAAC4C,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACA,IAAIkN,QAAQ,KAAK,kBAAkB,IAAI,CAACA,QAAQ,EAAE;QACjD;QACA7R,cAAc,CAAC,CAACC,WAAW,CAAC;;QAE5B;QACA,IAAI,OAAOvB,kBAAkB,KAAK,UAAU,EAAE;UAC7CA,kBAAkB,CAAC,CAAC;QACrB;;QAEA;QACJ,MAAMsT,oBAAoB,GAAGlC,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;QAC1F,IAAIoE,oBAAoB,EAAE;UACzB9Q,OAAO,CAACmQ,SAAS,CAAC9L,MAAM,CAAC,iBAAiB,CAAC;UAC3CrE,OAAO,CAACmQ,SAAS,CAACC,GAAG,CAAC,yBAAyB,CAAC;UAChDpQ,OAAO,CAACkQ,aAAa,GAAG,IAAI,CAAC,CAAC;QAC/B;MACD;IACD,CAAC;;IAED;IACA,IAAI,CAAClQ,OAAO,CAACsQ,YAAY,CAAC,yBAAyB,CAAC,EAAE;MACrD,IAAIK,QAAQ,KAAK,kBAAkB,EAAE;QACpC;QACA3Q,OAAO,CAACqK,gBAAgB,CAAC,WAAW,EAAEuG,WAAW,CAAC;QAClD5Q,OAAO,CAACqK,gBAAgB,CAAC,UAAU,EAAE0G,cAAc,CAAC;;QAEpD;QACA/Q,OAAO,CAACqK,gBAAgB,CAAC,OAAO,EAAE2G,WAAW,CAAC;MAC/C,CAAC,MAAM;QACN;QACAhR,OAAO,CAACqK,gBAAgB,CAAC,OAAO,EAAE2G,WAAW,CAAC;MAC/C;;MAEA;MACAhR,OAAO,CAACiR,YAAY,CAAC,yBAAyB,EAAE,MAAM,CAAC;IACxD;EACD,CAAC;EACDpW,SAAS,CAAC,MAAM;IACf,IAAIkG,OAA2B;IAC/B,IAAImQ,KAAY;IAEhB,MAAMC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI;QAAA,IAAAC,uBAAA,EAAAC,uBAAA,EAAAC,MAAA,EAAAC,OAAA;QACH;QACAL,KAAK,GAAG,CAAA7T,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEmE,SAAS,KAAI,EAAE;;QAEvC;QACA,MAAMgQ,WAAW,GAAGtS,oBAAoB,KAAK,SAAS,IAAI7B,cAAc,aAAdA,cAAc,gBAAA+T,uBAAA,GAAd/T,cAAc,CAAEmE,SAAS,cAAA4P,uBAAA,gBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B5U,WAAW,GAAG,CAAC,CAAC,cAAA6U,uBAAA,eAA5CA,uBAAA,CAA8C5P,WAAW,GAC/GpE,cAAc,CAACmE,SAAS,CAAChF,WAAW,GAAG,CAAC,CAAC,CAASiF,WAAW,GAC9D,EAAA6P,MAAA,GAAAJ,KAAK,cAAAI,MAAA,wBAAAC,OAAA,GAALD,MAAA,CAAQ,CAAC,CAAC,cAAAC,OAAA,uBAAVA,OAAA,CAAY9P,WAAW,KAAI,EAAE;QAEhCV,OAAO,GAAGd,iBAAiB,CAACuR,WAAW,IAAI,EAAE,CAAC;QAC9CjS,gBAAgB,CAACwB,OAAO,CAAC;QAEzB,IAAIA,OAAO,IAAID,uBAAuB,CAACC,OAAO,CAAC,EAAE;UAChD;;UAEA;UACA,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;UAC5C,MAAM+B,cAAc,GAAGR,MAAM,CAACS,WAAW;UACzC,MAAMC,aAAa,GAAGV,MAAM,CAACW,UAAU;;UAEvC;UACA,MAAMC,qBAAqB,GAC1BpC,IAAI,CAACqC,MAAM,GAAG,CAAC;UAAI;UACnBrC,IAAI,CAACI,GAAG,GAAG4B,cAAc;UAAI;UAC7BhC,IAAI,CAACsC,KAAK,GAAG,CAAC;UAAI;UAClBtC,IAAI,CAACK,IAAI,GAAG6B,aAAa,CAAC;UAC1B;;UAED;UACA,MAAMK,mBAAmB,GACxBvC,IAAI,CAACI,GAAG,IAAI,CAAC,EAAE;UAAI;UACnBJ,IAAI,CAACK,IAAI,IAAI,CAAC,EAAE;UAAI;UACpBL,IAAI,CAACqC,MAAM,IAAIL,cAAc,GAAG,EAAE;UAAI;UACtChC,IAAI,CAACsC,KAAK,IAAIJ,aAAa,GAAG,EAAE;UAAI;UACpClC,IAAI,CAACE,KAAK,GAAG,CAAC,IACdF,IAAI,CAACG,MAAM,GAAG,CACd;;UAED;UACAqC,UAAU,CAAC,MAAM;YAChB,IAAIzC,OAAO,KAAKqC,qBAAqB,IAAI,CAACG,mBAAmB,CAAC,EAAE;cAC/D1C,OAAO,CAAC4C,GAAG,CAAC,wDAAwD,CAAC;cACrE;cACA,IAAI;gBACH1C,OAAO,CAAC2C,cAAc,CAAC;kBACtBC,QAAQ,EAAE,QAAQ;kBAClBC,KAAK,EAAE,QAAQ;kBAAE;kBACjBC,MAAM,EAAE;gBACT,CAAC,CAAC;gBACFhD,OAAO,CAAC4C,GAAG,CAAC,8CAA8C,CAAC;cAC5D,CAAC,CAAC,OAAOK,WAAW,EAAE;gBACrBjD,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEkD,WAAW,CAAC;cAC5D;YACD,CAAC,MAAM,IAAI/C,OAAO,EAAE;cACnBF,OAAO,CAAC4C,GAAG,CAAC,6DAA6D,CAAC;cAC1E;cACA,IAAI;gBACH1C,OAAO,CAAC2C,cAAc,CAAC;kBACtBC,QAAQ,EAAE,QAAQ;kBAClBC,KAAK,EAAE,SAAS;kBAAE;kBAClBC,MAAM,EAAE;gBACT,CAAC,CAAC;cACH,CAAC,CAAC,OAAOjD,KAAK,EAAE;gBACfC,OAAO,CAAC4C,GAAG,CAAC,2CAA2C,EAAE7C,KAAK,CAAC;cAChE;YACD;UACD,CAAC,EAAE,GAAG,CAAC;QACR;;QAEA;QACA,MAAM6Q,iBAAiB,GAAGjT,gBAAgB,KAAK,SAAS,IACvDU,oBAAoB,KAAK,SAAS,IAClCjD,KAAK,KAAK,SAAS,IAClBuC,gBAAgB,KAAK,MAAM,IAAIU,oBAAoB,KAAK,SAAU;QAEpE,IAAIuS,iBAAiB,EAAE;UAAA,IAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAItB;UACA,IAAIlI,eAAe;UACnB,IAAI6C,WAAW;UAEf,IAAIxN,oBAAoB,KAAK,SAAS,IAAIT,oBAAoB,aAApBA,oBAAoB,gBAAAiT,sBAAA,GAApBjT,oBAAoB,CAAGjC,WAAW,GAAG,CAAC,CAAC,cAAAkV,sBAAA,eAAvCA,sBAAA,CAAyC5H,QAAQ,EAAE;YAAA,IAAAkI,uBAAA,EAAAC,uBAAA;YAC5F;YACApI,eAAe,GAAGpL,oBAAoB,CAACjC,WAAW,GAAG,CAAC,CAAC,CAACsN,QAAQ;YAChE4C,WAAW,GAAGrP,cAAc,aAAdA,cAAc,wBAAA2U,uBAAA,GAAd3U,cAAc,CAAEmE,SAAS,cAAAwQ,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4BxV,WAAW,GAAG,CAAC,CAAC,cAAAyV,uBAAA,uBAA5CA,uBAAA,CAA8ClM,OAAO;UACpE,CAAC,MAAM,IAAItH,oBAAoB,aAApBA,oBAAoB,gBAAAkT,sBAAA,GAApBlT,oBAAoB,CAAG,CAAC,CAAC,cAAAkT,sBAAA,eAAzBA,sBAAA,CAA2B7H,QAAQ,EAAE;YAAA,IAAAoI,uBAAA,EAAAC,uBAAA;YAC/C;YACAtI,eAAe,GAAGpL,oBAAoB,CAAC,CAAC,CAAC,CAACqL,QAAQ;YAClD4C,WAAW,GAAGrP,cAAc,aAAdA,cAAc,wBAAA6U,uBAAA,GAAd7U,cAAc,CAAEmE,SAAS,cAAA0Q,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,uBAA9BA,uBAAA,CAAgCpM,OAAO;UACtD,CAAC,MAAM;YAAA,IAAAqM,uBAAA,EAAAC,uBAAA;YACN;YACAxI,eAAe,GAAG;cACjBG,SAAS,EAAE,GAAG;cACdC,SAAS,EAAE,GAAG;cACduF,IAAI,EAAE,UAAU;cAChBL,KAAK,EAAE,QAAQ;cACfD,IAAI,EAAE,IAAI;cACVJ,cAAc,EAAE,IAAI;cACpBC,4BAA4B,EAAE,IAAI;cAClCC,QAAQ,EAAE,kBAAkB;cAC5B7C,aAAa,EAAE;YAChB,CAAC;YACDO,WAAW,GAAG,CAAArP,cAAc,aAAdA,cAAc,wBAAA+U,uBAAA,GAAd/U,cAAc,CAAEmE,SAAS,cAAA4Q,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B5V,WAAW,GAAG,CAAC,CAAC,cAAA6V,uBAAA,uBAA5CA,uBAAA,CAA8CtM,OAAO,KAAI,CAAC,CAAC;UAC1E;UACA,MAAM/D,OAAO,GAAG+H,UAAU,CAAC,EAAA6H,gBAAA,GAAA/H,eAAe,cAAA+H,gBAAA,uBAAfA,gBAAA,CAAiB5H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAM/H,OAAO,GAAG8H,UAAU,CAAC,EAAA8H,iBAAA,GAAAhI,eAAe,cAAAgI,iBAAA,uBAAfA,iBAAA,CAAiB5H,SAAS,KAAI,GAAG,CAAC;UAC7D,MAAMqI,kBAAkB,GAAGvI,UAAU,CAAC,EAAA+H,iBAAA,GAAAjI,eAAe,cAAAiI,iBAAA,uBAAfA,iBAAA,CAAiB5C,IAAI,KAAI,IAAI,CAAC;;UAEpE;UACArP,cAAc,CAACyS,kBAAkB,CAAC;UAElC,IAAIjR,IAAI,EAAED,GAAG;UACb,IAAIL,OAAO,EAAE;YACZ,MAAMC,IAAI,GAAGD,OAAO,CAACE,qBAAqB,CAAC,CAAC;YAC5CI,IAAI,GAAGL,IAAI,CAACmB,CAAC,GAAGH,OAAO;YACvBZ,GAAG,GAAGJ,IAAI,CAACqB,CAAC,IAAIJ,OAAO,GAAG,CAAC,GAAG,CAACA,OAAO,GAAGkH,IAAI,CAACoJ,GAAG,CAACtQ,OAAO,CAAC,CAAC;;YAE3D;YACA,MAAMiI,QAAQ,GAAGpI,sBAAsB,CAACd,IAAI,EAAEsR,kBAAkB,EAAEtQ,OAAO,EAAEC,OAAO,CAAC;YACnFxC,gBAAgB,CAACyK,QAAQ,CAAC;UAC3B;;UAEA;UACA,MAAMhG,eAAe,GAAG9D,QAAQ,CAAC+D,cAAc,CAAC,cAAc,CAAC;UAC/D,IAAID,eAAe,EAAE;YACpBlE,OAAO,GAAGkE,eAAe;YACzB;UACD,CAAC,MAAM;YACN;YACAlE,OAAO,GAAGI,QAAQ,CAACsP,aAAa,CAAC,KAAK,CAAC;YACvC1P,OAAO,CAACwS,EAAE,GAAG,cAAc,CAAC,CAAC;YAC7BxS,OAAO,CAACkQ,aAAa,GAAG,KAAK,CAAC,CAAC;YAC/B9P,QAAQ,CAACqS,IAAI,CAAC1C,WAAW,CAAC/P,OAAO,CAAC;UACnC;UAEAA,OAAO,CAAC8C,KAAK,CAAC4P,MAAM,GAAG,SAAS;UAChC1S,OAAO,CAAC8C,KAAK,CAACwM,aAAa,GAAG,MAAM,CAAC,CAAC;;UAEtC;UACAtP,OAAO,CAAC8C,KAAK,CAACsM,MAAM,GAAG,MAAM;;UAE7B;UACA,KAAA2C,iBAAA,GAAIlI,eAAe,cAAAkI,iBAAA,eAAfA,iBAAA,CAAiB5F,aAAa,EAAE;YACnCrN,cAAc,CAAC,IAAI,CAAC;UACrB;;UAEA;UACAmQ,kBAAkB,CAACjP,OAAO,EAAE6J,eAAe,EAAE6C,WAAW,EAAErL,IAAI,EAAED,GAAG,CAAC;;UAEpE;UACA,MAAMiP,aAAa,GAAGzB,kBAAkB,CAAC,eAAe,EAAE/E,eAAe,EAAE6C,WAAW,CAAC;UACvF,IAAI2D,aAAa,EAAE;YAClBvR,cAAc,CAAC,IAAI,CAAC;UACrB,CAAC,MAAM;YACN;UAAA;;UAGD;QACD;MACD,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACfC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACpD;IACD,CAAC;IAEDuQ,iBAAiB,CAAC,CAAC;IAEnB,OAAO,MAAM;MACZ,MAAMjN,eAAe,GAAG9D,QAAQ,CAAC+D,cAAc,CAAC,cAAc,CAAC;MAC/D,IAAID,eAAe,EAAE;QACpBA,eAAe,CAACyO,OAAO,GAAG,IAAI;QAC9BzO,eAAe,CAAC0O,WAAW,GAAG,IAAI;QAClC1O,eAAe,CAAC2O,UAAU,GAAG,IAAI;MAClC;IACD,CAAC;EACF,CAAC,EAAE,CACFxV,cAAc,EACdoB,oBAAoB,EACpBhB,kBAAkB,EAClBC,kBAAkB,EAClBwB,oBAAoB,EACpB1C;EACA;EAAA,CACA,CAAC;EACF,MAAMsW,cAAc,GAAG,CAAAzV,cAAc,aAAdA,cAAc,wBAAAe,uBAAA,GAAdf,cAAc,CAAEmE,SAAS,cAAApD,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgC0U,OAAO,cAAAzU,uBAAA,uBAAvCA,uBAAA,CAAyC0U,cAAc,KAAI,KAAK;EAEvF,SAASC,mBAAmBA,CAAC9T,cAAmB,EAAE;IAAA,IAAA+T,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;IACjD,IAAIjU,cAAc,KAAK,CAAC,EAAE;MACzB,OAAO,MAAM;IACd,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,QAAQ;IAChB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB,CAAC,MAAM,IAAIA,cAAc,KAAK,CAAC,EAAE;MAChC,OAAO,aAAa;IACrB;IAEA,OAAO,CAAA9B,cAAc,aAAdA,cAAc,wBAAA6V,uBAAA,GAAd7V,cAAc,CAAEmE,SAAS,cAAA0R,uBAAA,wBAAAC,uBAAA,GAAzBD,uBAAA,CAA4B,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA9BD,uBAAA,CAAgCJ,OAAO,cAAAK,uBAAA,uBAAvCA,uBAAA,CAAyCC,gBAAgB,KAAI,MAAM;EAC3E;EACA,MAAMC,gBAAgB,GAAGL,mBAAmB,CAAC9T,cAAc,CAAC;EAC5D,MAAMoU,cAAc,GAAGA,CAAA,KAAM;IAC5B,IAAI,CAACT,cAAc,EAAE,OAAO,IAAI;IAEhC,IAAIQ,gBAAgB,KAAK,MAAM,EAAE;MAChC,oBACC3X,OAAA,CAACP,aAAa;QACboY,OAAO,EAAC,MAAM;QACdtC,KAAK,EAAEzU,UAAW;QAClBmG,QAAQ,EAAC,QAAQ;QACjB6Q,UAAU,EAAEjX,WAAW,GAAG,CAAE;QAC5BkX,EAAE,EAAE;UACH3Q,eAAe,EAAE,aAAa;UAC9BH,QAAQ,EAAE,oBAAoB;UAC9B,+BAA+B,EAAE;YAChCG,eAAe,EAAE3D,aAAa,CAAE;UACjC;QACD,CAAE;QACFuU,UAAU,eAAEhY,OAAA,CAACV,MAAM;UAAC6H,KAAK,EAAE;YAAE8Q,UAAU,EAAE;UAAS;QAAE;UAAA5N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxD0N,UAAU,eAAElY,OAAA,CAACV,MAAM;UAAC6H,KAAK,EAAE;YAAE8Q,UAAU,EAAE;UAAS;QAAE;UAAA5N,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEJ;IACA,IAAImN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACC3X,OAAA,CAACX,GAAG;QAAC0Y,EAAE,EAAE;UAAEtP,OAAO,EAAE,MAAM;UAAEyL,UAAU,EAAE,QAAQ;UAAEiE,YAAY,EAAE,QAAQ;UAAEC,GAAG,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAM,CAAE;QAAAC,QAAA,EAGrGC,KAAK,CAACC,IAAI,CAAC;UAAErM,MAAM,EAAErL;QAAW,CAAC,CAAC,CAAC4I,GAAG,CAAC,CAAC+O,CAAC,EAAEC,KAAK,kBAChD1Y,OAAA;UAECmH,KAAK,EAAE;YACN5B,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,KAAK;YACb4B,eAAe,EAAEsR,KAAK,KAAK7X,WAAW,GAAG,CAAC,GAAG4C,aAAa,GAAG,SAAS;YAAE;YACxEuL,YAAY,EAAE;UACf;QAAE,GANG0J,KAAK;UAAArO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAOV,CACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAER;IACA,IAAImN,gBAAgB,KAAK,aAAa,EAAE;MACvC,oBACC3X,OAAA,CAACX,GAAG;QAAC0Y,EAAE,EAAE;UAAEtP,OAAO,EAAE,MAAM;UAAEyL,UAAU,EAAE,QAAQ;UAAEiE,YAAY,EAAE;QAAa,CAAE;QAAAG,QAAA,eAC9EtY,OAAA,CAACL,UAAU;UAACoY,EAAE,EAAE;YAAEM,OAAO,EAAE,KAAK;YAAEhN,KAAK,EAAE5H;UAAc,CAAE;UAAA6U,QAAA,GAAC,OACpD,EAACzX,WAAW,EAAC,MAAI,EAACC,UAAU;QAAA;UAAAuJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,IAAImN,gBAAgB,KAAK,QAAQ,EAAE;MAClC,oBACC3X,OAAA,CAACX,GAAG;QAAAiZ,QAAA,eACHtY,OAAA,CAACL,UAAU;UAACkY,OAAO,EAAC,OAAO;UAAAS,QAAA,eAC1BtY,OAAA,CAACR,cAAc;YACdqY,OAAO,EAAC,aAAa;YACrBc,KAAK,EAAE3X,QAAS;YAChB+W,EAAE,EAAE;cACHvS,MAAM,EAAE,KAAK;cACXwJ,YAAY,EAAE,MAAM;cACpB4J,MAAM,EAAE,UAAU;cACpB,0BAA0B,EAAE;gBAC3BxR,eAAe,EAAE3D,aAAa,CAAE;cACjC;YACD;UAAE;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAER;IAEA,OAAO,IAAI;EACZ,CAAC;EACD,oBACCxK,OAAA,CAAAE,SAAA;IAAAoY,QAAA,GACE3U,aAAa,iBACb3D,OAAA;MAAAsY,QAAA,EAcElV,WAAW,iBACXpD,OAAA,CAACN,OAAO;QACPwQ,IAAI,EAAE2I,OAAO,CAAChV,aAAa,CAAC,IAAIgV,OAAO,CAACzY,QAAQ,CAAE;QAClDA,QAAQ,EAAEA,QAAS;QACnBK,OAAO,EAAEA,CAAA,KAAM;UACd;UACA;QAAA,CACC;QACFiK,YAAY,EAAEA,YAAa;QAC3BG,eAAe,EAAEA,eAAgB;QACjCiO,eAAe,EAAC,gBAAgB;QAChCC,cAAc,EACblV,aAAa,GACV;UACA4B,GAAG,EAAE5B,aAAa,CAAC4B,GAAG,GAAE,EAAE,IAAG2I,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAAG,CAACkL,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,GAAGsK,IAAI,CAACoJ,GAAG,CAACxI,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC;UAChJwC,IAAI,EAAE7B,aAAa,CAAC6B,IAAI,GAAE,EAAE,GAAE0I,UAAU,CAACnL,YAAY,IAAI,GAAG;QAC5D,CAAC,GACD+D,SACH;QACD+Q,EAAE,EAAE;UACH;UACA;UACA;UACA,gBAAgB,EAAE3X,QAAQ,GAAG,MAAM,GAAG,MAAM;UAC5C,8CAA8C,EAAE;YAC/CqT,MAAM,EAAE,IAAI;YACZ;YACA,GAAG1E,WAAW;YACd;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,GAAGiE,iBAAiB,CAAC,CAAA3R,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEyJ,QAAQ,KAAI,eAAe,CAAC;YACnErF,GAAG,EAAE,GAAG,CAAC,CAAA5B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4B,GAAG,KAAI,CAAC,KAC5BvC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC3CkL,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,GAAG,CAAC,GAClC,CAACkL,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,GAChCsK,IAAI,CAACoJ,GAAG,CAACxI,UAAU,CAAClL,YAAY,IAAI,GAAG,CAAC,CAAC,GAC1C,CAAC,CAAC,eAAe;YACrBwC,IAAI,EAAE,GAAG,CAAC,CAAA7B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE6B,IAAI,KAAI,CAAC,KAAKzC,YAAY,IAAIA,YAAY,IAAI,WAAW,GAC9EmL,UAAU,CAACnL,YAAY,CAAC,IAAI,CAAC,GAC9B,CAAC,CAAE,eAAe;YACrB+V,QAAQ,EAAE;UACX;QACD,CAAE;QACFC,iBAAiB,EAAE,IAAK;QAAAX,QAAA,gBAExBtY,OAAA;UAAKmH,KAAK,EAAE;YAAEgR,YAAY,EAAE,KAAK;YAAE1P,OAAO,EAAE;UAAO,CAAE;UAAA6P,QAAA,EACnD,CAAAlX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE8X,aAAa,kBAC9BlZ,OAAA,CAACT,UAAU;YACV4Z,OAAO,EAAEA,CAAA,KAAM;cACd;cACA;YAAA,CACC;YACFpB,EAAE,EAAE;cACH9Q,QAAQ,EAAE,OAAO;cACjBmS,SAAS,EAAE,iCAAiC;cAC5C1T,IAAI,EAAE,MAAM;cACZiC,KAAK,EAAE,MAAM;cACbiR,MAAM,EAAE,OAAO;cACfS,UAAU,EAAE,iBAAiB;cAC7BC,MAAM,EAAE,gBAAgB;cACxB7F,MAAM,EAAE,QAAQ;cAChBzE,YAAY,EAAE,MAAM;cACpBqJ,OAAO,EAAE;YACV,CAAE;YAAAC,QAAA,eAEFtY,OAAA,CAACJ,SAAS;cAACmY,EAAE,EAAE;gBAAEwB,IAAI,EAAE,CAAC;gBAAElO,KAAK,EAAE;cAAO;YAAE;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QACZ;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eACNxK,OAAA,CAACF,gBAAgB;UAEpB0Z,GAAG,EAAEtT,YAAa;UAClBiB,KAAK,EAAE;YAAEsS,SAAS,EAAExN,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG;UAAQ,CAAE;UAC3E4M,OAAO,EAAE;YACRC,eAAe,EAAE,CAAC3T,cAAc;YAChC4T,eAAe,EAAE,IAAI;YACrBC,gBAAgB,EAAE,KAAK;YACvBC,WAAW,EAAE,IAAI;YACjBC,kBAAkB,EAAE,EAAE;YACtBC,kBAAkB,EAAE,IAAI;YACxBC,mBAAmB,EAAE;UACtB,CAAE;UAAA3B,QAAA,eAECtY,OAAA;YAAKmH,KAAK,EAAE;cACXsS,SAAS,EAAExN,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,OAAO;cAC/DkM,QAAQ,EAAE/M,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;cACvEvH,KAAK,EAAE0G,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG9F,SAAS;cAC7D4R,MAAM,EAAE3M,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGjF;YAClC,CAAE;YAAAsR,QAAA,eACDtY,OAAA,CAACX,GAAG;cAAC8H,KAAK,EAAE;gBACXkR,OAAO,EAAEpM,cAAc,CAAC,CAAC,GAAG,GAAG,GAC7Ba,WAAW,CAAC,CAAC,GAAG,GAAG,GAAI,CAAAzL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE6Y,OAAO,KAAI,MAAO;gBAC7D1U,MAAM,EAAEyG,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGuD,aAAa;gBACjDjK,KAAK,EAAE0G,cAAc,CAAC,CAAC,IAAIa,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG9F,SAAS;gBAC7D4R,MAAM,EAAE3M,cAAc,CAAC,CAAC,GAAG,GAAG,GAAGjF;cAClC,CAAE;cAAAsR,QAAA,gBACDtY,OAAA,CAACX,GAAG;gBACHma,GAAG,EAAErV,UAAW;gBAChBsE,OAAO,EAAC,MAAM;gBACd0R,aAAa,EAAC,QAAQ;gBACtBC,QAAQ,EAAC,MAAM;gBACfjG,cAAc,EAAC,QAAQ;gBACvB4D,EAAE,EAAE;kBACHxS,KAAK,EAAEuH,WAAW,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;kBACtCuL,OAAO,EAAEvL,WAAW,CAAC,CAAC,GAAG,GAAG,GAAG9F;gBAChC,CAAE;gBAAAsR,QAAA,GAEDpX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEwI,GAAG,CAAE2Q,SAAc,IACpCA,SAAS,CAAC/N,WAAW,CAAC5C,GAAG,CAAC,CAAC4Q,SAAc,EAAEC,QAAgB,kBAC1Dva,OAAA,CAACX,GAAG;kBAEHmb,SAAS,EAAC,KAAK;kBACfC,GAAG,EAAEH,SAAS,CAAC9N,GAAI;kBACnBkO,GAAG,EAAEJ,SAAS,CAACK,OAAO,IAAI,OAAQ;kBAClC5C,EAAE,EAAE;oBACH0B,SAAS,EAAEY,SAAS,CAACO,cAAc,IAAIN,SAAS,CAACM,cAAc,IAAI,OAAO;oBAC1ErP,SAAS,EAAE8O,SAAS,CAAC7O,SAAS,IAAI,QAAQ;oBAC1CqP,SAAS,EAAEP,SAAS,CAACQ,GAAG,IAAI,SAAS;oBACrC;oBACAtV,MAAM,EAAE,GAAG8U,SAAS,CAAC7K,aAAa,IAAI,GAAG,IAAI;oBAC7C4J,UAAU,EAAEiB,SAAS,CAAC/K,eAAe,IAAI,SAAS;oBAClDqJ,MAAM,EAAE;kBACT,CAAE;kBACFO,OAAO,EAAEA,CAAA,KAAM;oBACd,IAAIkB,SAAS,CAACU,SAAS,EAAE;sBACxB,MAAMlL,SAAS,GAAGwK,SAAS,CAACU,SAAS;sBACrClU,MAAM,CAACqJ,IAAI,CAACL,SAAS,EAAE,QAAQ,EAAE,qBAAqB,CAAC;oBACxD;kBACD,CAAE;kBACF1I,KAAK,EAAE;oBAAE4P,MAAM,EAAEsD,SAAS,CAACU,SAAS,GAAG,SAAS,GAAG;kBAAU;gBAAE,GAnB1D,GAAGV,SAAS,CAACtQ,EAAE,IAAIwQ,QAAQ,EAAE;kBAAAlQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAoBlC,CACD,CACF,CAAC,EAEAvJ,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEyI,GAAG,CACxB,CAACsR,SAAc,EAAEtC,KAAU;kBAAA,IAAAuC,qBAAA,EAAAC,sBAAA;kBAAA,OAC1BF,SAAS,CAACrO,IAAI,iBACb3M,OAAA,CAACL,UAAU;oBACVwb,SAAS,EAAC,eAAe;oBACG;oBAC5BpD,EAAE,EAAE;sBACHxM,SAAS,EAAE,EAAA0P,qBAAA,GAAAD,SAAS,CAAC/P,cAAc,cAAAgQ,qBAAA,uBAAxBA,qBAAA,CAA0BG,UAAU,KAAIrQ,SAAS,CAACQ,SAAS;sBACtEF,KAAK,EAAE,EAAA6P,sBAAA,GAAAF,SAAS,CAAC/P,cAAc,cAAAiQ,sBAAA,uBAAxBA,sBAAA,CAA0B5P,SAAS,KAAIP,SAAS,CAACM,KAAK;sBAC7DgQ,UAAU,EAAE,UAAU;sBACtBC,SAAS,EAAE,YAAY;sBACvBjD,OAAO,EAAE;oBACV,CAAE;oBACFkD,uBAAuB,EAAE9P,iBAAiB,CAACuP,SAAS,CAACrO,IAAI,CAAE,CAAC;kBAAA,GARvDqO,SAAS,CAACjR,EAAE,IAAI2O,KAAK;oBAAArO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAS1B,CACD;gBAAA,CACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,EAELgR,MAAM,CAACC,IAAI,CAAC7M,cAAc,CAAC,CAAClF,GAAG,CAAEmF,WAAW;gBAAA,IAAA6M,qBAAA,EAAAC,sBAAA;gBAAA,oBAC5C3b,OAAA,CAACX,GAAG;kBACHma,GAAG,EAAEpV,kBAAmB;kBAExB2T,EAAE,EAAE;oBACHtP,OAAO,EAAE,MAAM;oBACf0L,cAAc,EAAErB,YAAY,EAAA4I,qBAAA,GAAC9M,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAA6M,qBAAA,uBAA9BA,qBAAA,CAAgClQ,SAAS,CAAC;oBACvE4O,QAAQ,EAAE,MAAM;oBAChBxB,MAAM,EAAE3M,cAAc,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO;oBACtC7E,eAAe,GAAAuU,sBAAA,GAAE/M,cAAc,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,cAAA8M,sBAAA,uBAA9BA,sBAAA,CAAgCpM,eAAe;oBAChE8I,OAAO,EAAEpM,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,OAAO;oBAC3C1G,KAAK,EAAE0G,cAAc,CAAC,CAAC,GAAG,MAAM,GAAG,MAAM;oBACzC+C,YAAY,EAAE/C,cAAc,CAAC,CAAC,GAAG,MAAM,GAAGjF;kBAC3C,CAAE;kBAAAsR,QAAA,EAED1J,cAAc,CAACC,WAAW,CAAC,CAACnF,GAAG,CAAC,CAACG,MAAW,EAAE6O,KAAa;oBAAA,IAAAkD,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;oBAAA,oBAC3Dlc,OAAA,CAACV,MAAM;sBAEN6Z,OAAO,EAAEA,CAAA,KAAMzJ,kBAAkB,CAAC7F,MAAM,CAACsS,YAAY,CAAE;sBACvDtE,OAAO,EAAC,WAAW;sBACnBE,EAAE,EAAE;wBACHqE,WAAW,EAAEnQ,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,MAAM;wBAC9C2M,MAAM,EAAE3M,cAAc,CAAC,CAAC,GAAG,KAAK,GAAG,eAAe;wBAClD7E,eAAe,EAAE,EAAAwU,qBAAA,GAAA/R,MAAM,CAACwS,gBAAgB,cAAAT,qBAAA,uBAAvBA,qBAAA,CAAyBU,qBAAqB,KAAI,SAAS;wBAC5EjR,KAAK,EAAE,EAAAwQ,sBAAA,GAAAhS,MAAM,CAACwS,gBAAgB,cAAAR,sBAAA,uBAAvBA,sBAAA,CAAyBU,eAAe,KAAI,MAAM;wBACzDjD,MAAM,EAAE,EAAAwC,sBAAA,GAAAjS,MAAM,CAACwS,gBAAgB,cAAAP,sBAAA,uBAAvBA,sBAAA,CAAyBU,iBAAiB,KAAI,aAAa;wBACnEvI,QAAQ,EAAE,EAAA8H,sBAAA,GAAAlS,MAAM,CAACwS,gBAAgB,cAAAN,sBAAA,uBAAvBA,sBAAA,CAAyBU,QAAQ,KAAI,MAAM;wBACrDlX,KAAK,EAAE,EAAAyW,sBAAA,GAAAnS,MAAM,CAACwS,gBAAgB,cAAAL,sBAAA,uBAAvBA,sBAAA,CAAyB9O,KAAK,KAAI,MAAM;wBAC/CmL,OAAO,EAAEpM,cAAc,CAAC,CAAC,GAAG,kCAAkC,GAAG,SAAS;wBAC1EyQ,UAAU,EAAEzQ,cAAc,CAAC,CAAC,GAAG,0BAA0B,GAAG,QAAQ;wBACpE0Q,aAAa,EAAE,MAAM;wBACrB3N,YAAY,EAAE,EAAAiN,sBAAA,GAAApS,MAAM,CAACwS,gBAAgB,cAAAJ,sBAAA,uBAAvBA,sBAAA,CAAyBW,YAAY,KAAI,KAAK;wBAC5DjP,QAAQ,EAAE1B,cAAc,CAAC,CAAC,GAAG,aAAa,GAAGjF,SAAS;wBACtDoS,SAAS,EAAE,iBAAiB;wBAAE;wBAC9B,SAAS,EAAE;0BACVhS,eAAe,EAAE,EAAA8U,sBAAA,GAAArS,MAAM,CAACwS,gBAAgB,cAAAH,sBAAA,uBAAvBA,sBAAA,CAAyBI,qBAAqB,KAAI,SAAS;0BAAE;0BAC9EO,OAAO,EAAE,GAAG;0BAAE;0BACdzD,SAAS,EAAE,iBAAiB,CAAE;wBAC/B;sBACD,CAAE;sBAAAd,QAAA,EAEDzO,MAAM,CAACiT;oBAAU,GAxBbpE,KAAK;sBAAArO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAyBH,CAAC;kBAAA,CACT;gBAAC,GAxCGqE,WAAW;kBAAAxE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyCZ,CAAC;cAAA,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGD;QAAC,GApIL,aAAaxE,cAAc,EAAE;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqIZ,CAAC,EAElB2M,cAAc,IAAIrW,UAAU,GAAC,CAAC,IAAI+B,gBAAgB,KAAK,MAAM,iBAAI7C,OAAA,CAACX,GAAG;UAAAiZ,QAAA,EAAEV,cAAc,CAAC;QAAC;UAAAvN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAAE,GAAG;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F;IACT;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CACL,eAEDxK,OAAA;MAAAsY,QAAA,EACE;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAjO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACP,CAAC;AAEL,CAAC;AAACxI,EAAA,CAj6CI7B,cAAoC;EAAA,QA8CrCN,cAAc;AAAA;AAAAkd,EAAA,GA9Cb5c,cAAoC;AAm6C1C,eAAeA,cAAc;AAAC,IAAA4c,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}